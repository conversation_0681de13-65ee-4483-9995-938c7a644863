import React, { useState, useEffect } from "react";
import axios from "axios";
import { FiSearch, FiUser, FiPhone, FiCreditCard, FiAward, FiDollarSign, FiClock, FiCalendar, FiHash } from "react-icons/fi";

const API_LOYALTY_POINTS_URL = "http://127.0.0.1:8000/api/loyalty-points";

const LoyaltyReport = () => {
  const [data, setData] = useState([]);
  const [searchInput, setSearchInput] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await axios.get(API_LOYALTY_POINTS_URL);
        setData(res.data.data || []);
      } catch (err) {
        setError("Failed to fetch loyalty points report.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const filteredData = data.filter((customer) => {
    if (!searchInput.trim()) return true;
    const search = searchInput.trim().toLowerCase();
    return (
      (customer.phone || "").toLowerCase().includes(search) ||
      (customer.loyalty_card_number || "").toLowerCase().includes(search) ||
      (customer.customer_name || "").toLowerCase().includes(search)
    );
  });

  if (loading) return (
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-blue-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
      <div className="animate-pulse flex flex-col items-center">
        <div className="h-12 w-12 bg-blue-400 rounded-full mb-4"></div>
        <p className="text-gray-700 dark:text-gray-300">Loading report...</p>
      </div>
    </div>
  );

  if (error) return (
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-blue-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
      <div className="bg-red-100 dark:bg-red-900/30 border-l-4 border-red-500 dark:border-red-400 text-red-700 dark:text-red-100 p-4 rounded-lg shadow-lg max-w-md">
        <p className="font-bold">Error</p>
        <p>{error}</p>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen p-4 md:p-8 bg-gradient-to-br from-blue-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">Loyalty Program</h1>
            <p className="text-gray-600 dark:text-gray-300">Track and manage customer loyalty points</p>
          </div>
          <div className="mt-4 md:mt-0 w-full md:w-auto">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="pl-10 pr-4 py-2 w-full md:w-64 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                placeholder="Search customers..."
              />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden mb-8">
          <div className="p-6 border-b border-gray-200 dark:border-gray-600">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
              <FiUser className="mr-2" /> Customer Summary
            </h2>
          </div>
          
          {filteredData.length === 0 && searchInput.trim() ? (
            <div className="p-8 text-center">
              <div className="text-gray-400 dark:text-gray-500 mb-4">
                <FiSearch className="mx-auto h-12 w-12" />
              </div>
              <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">No customers found</h3>
              <p className="mt-1 text-gray-500 dark:text-gray-400">Try adjusting your search query</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                <thead className="bg-gray-50 dark:bg-gray-600">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        <FiUser className="mr-2" /> Name
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        <FiPhone className="mr-2" /> Mobile
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        <FiHash className="mr-2" /> Card No
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        <FiCreditCard className="mr-2" /> Card
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        Type
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        Mode
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        Reward
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        Points
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        Redeemed
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        Total Sale
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        <FiClock className="mr-2" /> Last Visit
                      </div>
                    </th>
                    {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      <div className="flex items-center">
                        Discount %
                      </div>
                    </th> */}
                    
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                  {filteredData.map((customer) => (
                    <tr key={customer.id} className="hover:bg-gray-50 dark:hover:bg-gray-600/50 transition-colors duration-150">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-blue-600 dark:text-blue-300 font-medium">
                            {customer.customer_name ? customer.customer_name.charAt(0).toUpperCase() : "?"}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {customer.customer_name || "-"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-200">{customer.phone || "-"}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-200">
                          <div className="font-medium">{customer.loyalty_card_number || "-"}</div>
                          <div className="text-gray-500 dark:text-gray-400 text-xs">
                            {customer.card_name || customer.card_name || "-"}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                        {customer.cards && customer.cards.length > 0
                          ? customer.cards.map((card, idx) => (
                              <div key={idx}>{card.card_name || '-'}</div>
                            ))
                          : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                        {customer.cards && customer.cards.length > 0
                          ? customer.cards.map((card, idx) => (
                              <div key={idx}>{card.calculation_type || '-'}</div>
                            ))
                          : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                        {customer.cards && customer.cards.length > 0
                          ? customer.cards.map((card, idx) => (
                              <div key={idx}>
                                {card.threshold_method === 'per-threshold'
                                  ? (card.calculation_type === 'Point-wise'
                                      ? 'Points per Threshold'
                                      : card.calculation_type === 'Percentage-wise'
                                      ? 'Percentage per Threshold'
                                      : '-')
                                  : card.threshold_method === 'single-threshold'
                                  ? 'Single Threshold'
                                  : '-'}
                              </div>
                            ))
                          : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                        {customer.cards && customer.cards.length > 0
                          ? customer.cards.map((card, idx) => (
                              <div key={idx}>{card.reward || '-'}</div>
                            ))
                          : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 mr-2">
                            <div 
                              className="bg-gradient-to-r from-blue-400 to-blue-600 h-2.5 rounded-full" 
                              style={{ width: `${Math.min(100, (customer.points_balance / 1000) * 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {customer.points_balance}
                          </span>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <span>Earned: {customer.points_earned}</span>
                          <span>Redeemed: {customer.points_redeemed}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {customer.points_redeemed != null ? customer.points_redeemed : 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-200 font-medium">
                          LKR {parseFloat(customer.total_sale?.toFixed(2) || 0)}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {customer.visits} visits
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-200">
                          {customer.last_visit || "-"}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {customer.days ? `${customer.days} days ago` : "-"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-200">
                          {customer.discount_percentage != null ? customer.discount_percentage + '%' : '-'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {filteredData.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-white dark:bg-gray-700 rounded-xl shadow p-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Customers</h3>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{filteredData.length}</p>
            </div>
            <div className="bg-white dark:bg-gray-700 rounded-xl shadow p-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Points</h3>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {filteredData.reduce((sum, customer) => sum + (customer.points_balance || 0), 0)}
              </p>
            </div>
            <div className="bg-white dark:bg-gray-700 rounded-xl shadow p-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Sales</h3>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                LKR {parseFloat(filteredData.reduce((sum, customer) => sum + (customer.total_sale || 0), 0).toFixed(2))}
              </p>
            </div>
            <div className="bg-white dark:bg-gray-700 rounded-xl shadow p-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Avg. Visits</h3>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {filteredData.length > 0 
                  ? (filteredData.reduce((sum, customer) => sum + (customer.visits || 0), 0) / filteredData.length)
                  : 0}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoyaltyReport;