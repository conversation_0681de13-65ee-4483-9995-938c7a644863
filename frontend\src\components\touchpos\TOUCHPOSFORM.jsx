import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import debounce from "lodash.debounce";
import { FaPause, FaRedo, FaCreditCard } from "react-icons/fa";
import {
  ClipboardList,
  Trash2,
  LogOut,
  LockOpen,
  Maximize,
  Minimize,
  Calculator,
  LayoutDashboard,
  Plus,
  Minus,
} from "lucide-react";
// import BillPrintModal from "../models/BillPrintModel.jsx";
import TouchPOSBillModel from "../models/TouchPOSBillModal.jsx";
import Notification from "../notification/Notification.jsx";
import { formatNumberWithCommas } from "../../utils/numberformat";
import CalculatorModal from "../models/calculator/CalculatorModal.jsx";
import HeldSalesList from "../pos/HeldSalesList";
import { useRegister } from "../../context/RegisterContext";
import RegisterModal from "../models/registerModel.jsx";
import { useAuth } from "../../context/NewAuthContext.jsx";
import ItemForm from "../../components/Item Form/ItemForm";
// Helper function to check if date is within discount scheme period
const isDateWithinScheme = (invoiceDate, startDate, endDate) => {
  try {
    const invDate = new Date(invoiceDate);
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;
    return (!start || invDate >= start) && (!end || invDate <= end);
  } catch (e) {
    console.error("Error checking date within scheme:", e);
    return false;
  }
};

// Function to apply discount schemes for products or categories
const applyDiscountScheme = (product, saleType, schemes) => {
  if (
    !product ||
    (!product.product_id && !product.id) ||
    !Array.isArray(schemes)
  ) {
    const fallbackPrice =
      saleType === "Wholesale"
        ? parseFloat(product?.wholesale_price || product?.sales_price || 0)
        : parseFloat(product?.sales_price || 0);
    return { price: Math.max(0, fallbackPrice), schemeName: null, free_qty: 0 };
  }

  const basePrice =
    saleType === "Wholesale"
      ? parseFloat(product.wholesale_price || product.sales_price || 0)
      : parseFloat(product.sales_price || 0);

  if (isNaN(basePrice) || basePrice <= 0) {
    return { price: 0, schemeName: null, free_qty: 0 };
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let bestScheme = null;
  let maxDiscountValue = -1;
  let bestFreeScheme = null;
  let maxFreeQty = 0;

  const findBestScheme = (schemeList) => {
    schemeList.forEach((scheme) => {
      if (
        !scheme.active ||
        !scheme.type
      ) {
        return;
      }

      // For non-free schemes, check if value is provided
      if (scheme.type !== "free" && (scheme.value === null || scheme.value === undefined)) {
        return;
      }

      const startDate = scheme.start_date ? new Date(scheme.start_date) : null;
      const endDate = scheme.end_date ? new Date(scheme.end_date) : null;
      if (startDate && startDate > today) return;
      if (endDate) {
        endDate.setHours(23, 59, 59, 999);
        if (endDate < today) return;
      }

      let currentDiscountValue = 0;
      const schemeValue = parseFloat(scheme.value || 0);

      if (scheme.type === "percentage" && schemeValue > 0) {
        currentDiscountValue = (basePrice * schemeValue) / 100;
      } else if (scheme.type === "amount" && schemeValue > 0) {
        currentDiscountValue = schemeValue;
      } else if (scheme.type === "free") {
        const qtyNum = Number(product.qty) || 1;
        const buyQtyNum = Number(scheme.buy_quantity);
        const freeQtyNum = Number(scheme.free_quantity);
        const eligibleFree = Math.floor(qtyNum / buyQtyNum) * freeQtyNum;
        if (eligibleFree > maxFreeQty) {
          maxFreeQty = eligibleFree;
          bestFreeScheme = scheme;
        }
        return;
      } else {
        return;
      }

      currentDiscountValue = Math.min(currentDiscountValue, basePrice);

      if (currentDiscountValue > maxDiscountValue) {
        maxDiscountValue = currentDiscountValue;
        bestScheme = scheme;
      }
    });
  };

  // Create display name for batch-wise matching
  const batchInfo = product.batch_number
    ? ` (Batch: ${product.batch_number})`
    : "";
  const expiryInfo = product.expiry_date
    ? ` (Exp: ${product.expiry_date.split("T")[0]})`
    : "";
  const displayName = `${product.product_name}${batchInfo}${expiryInfo}`;

  const productSchemes = schemes.filter((s) => {
    if (s.applies_to !== "product") return false;
    const target = s.target?.trim().toLowerCase();
    const productName = product.product_name?.trim().toLowerCase() || "";
    // Match both old format (just product name) and new format (with batch info)
    return (
      target === productName || target === displayName.trim().toLowerCase()
    );
  });
  findBestScheme(productSchemes);

  if ((!bestScheme || maxDiscountValue <= 0) && product.category_name) {
    const categorySchemes = schemes.filter(
      (s) => s.applies_to === "category" && s.target === product.category_name
    );
    findBestScheme(categorySchemes);
  }

  if (bestFreeScheme && maxFreeQty > 0) {
    return {
      price: basePrice,
      schemeName: bestFreeScheme.name || "Unnamed Free Scheme",
      free_qty: maxFreeQty,
    };
  }

  if (bestScheme && maxDiscountValue > 0) {
    let discountedPrice = basePrice;
    const schemeValue = parseFloat(bestScheme.value || 0);

    if (bestScheme.type === "percentage") {
      discountedPrice = basePrice * (1 - schemeValue / 100);
    } else if (bestScheme.type === "amount") {
      discountedPrice = basePrice - schemeValue;
    }
    return {
      price: Math.max(0, discountedPrice),
      schemeName: bestScheme.name || "Unnamed Scheme",
      free_qty: 0,
    };
  }

  return { price: basePrice, schemeName: null, free_qty: 0 };
};

const TOUCHPOSFORM = () => {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [saleType, setSaleType] = useState("Retail");
  const [products, setProducts] = useState([]);
  const [discountInputs, setDiscountInputs] = useState({});
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [items, setItems] = useState([]);
  const [activeSchemes, setActiveSchemes] = useState([]);
  const [tax, setTax] = useState(0);
  const [billDiscount, setBillDiscount] = useState(0);
  const [billDiscountPercentage, setBillDiscountPercentage] = useState(0);
  const [shipping, setShipping] = useState(0);
  const [selectedProductId, setSelectedProductId] = useState(null);
  const searchInputRef = useRef(null);
  const taxInputRef = useRef(null);
  const discountInputRef = useRef(null);
  const [showNotification, setShowNotification] = useState(false);
  const [pendingDeleteIndex, setPendingDeleteIndex] = useState(null);
  const [showBillModal, setShowBillModal] = useState(false);
  const navigate = useNavigate();
  const [billNumber, setBillNumber] = useState("");
  const [customerInfo, setCustomerInfo] = useState({
    name: "",
    mobile: "",
    bill_number: "",
    userId: "U-1",
    receivedAmount: 0,
  });
  const [showCalculatorModal, setShowCalculatorModal] = useState(false);
  const [loadingItems, setLoadingItems] = useState(false);
  const [loadingSchemes, setLoadingSchemes] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingBrands, setLoadingBrands] = useState(false);
  const [filterError, setFilterError] = useState(null);
  const [user, setUser] = useState(null);
  const auth = useAuth();
  const {
    registerStatus,
    openRegister,
    closeRegister,
    loading,
    refreshRegisterStatus,
    terminalId,
    isNewDay,
    initializeDailyRegister,
  } = useRegister();
  const userId = auth?.user?.id || 1;
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [isClosingRegister, setIsClosingRegister] = useState(false);
  const [heldSales, setHeldSales] = useState([]);
  const [loadingHeldSales, setLoadingHeldSales] = useState(false);
  const [showHeldSalesList, setShowHeldSalesList] = useState(false);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);

  // Quantity popup state
  const [showQuantityPopup, setShowQuantityPopup] = useState(false);
  const [selectedProductForQty, setSelectedProductForQty] = useState(null);
  const [tempQuantity, setTempQuantity] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [selectedBrand, setSelectedBrand] = useState("All Brands");
  const [lowStockWarning, setLowStockWarning] = useState(null);
  const [stockWarning, setStockWarning] = useState("");
  const [showAddItemForm, setShowAddItemForm] = useState(false);

  // Helper function to parse comma-formatted numbers like "1,500"
  const parseFormattedNumber = (value) => {
    if (typeof value === "number") return value;
    if (typeof value === "string") {
      return parseFloat(value.replace(/,/g, ""));
    }
    return 0;
  };

  // Helper functions for discount calculations
  const calculateDiscountFromPercentage = (percentage, subtotal) => {
    return (subtotal * percentage) / 100;
  };

  const calculatePercentageFromDiscount = (discountAmount, subtotal) => {
    if (subtotal === 0) return 0;
    return (discountAmount / subtotal) * 100;
  };

  useEffect(() => {
    if (auth && auth.user) {
      setUser(auth.user);
    }
  }, [auth]);

  // Load held sales
  const loadHeldSales = useCallback(async () => {
    setLoadingHeldSales(true);
    try {
      const response = await axios.get("/api/holds", {
        params: { status: "held" },
      });
      if (response.data.status === "success") {
        setHeldSales(response.data.data);
      } else {
        alert(
          "Failed to load held sales: " +
            (response.data.message || "Unknown error")
        );
        setHeldSales([]);
      }
    } catch (error) {
      console.error("Error loading held sales:", error);
      setHeldSales([]);
    } finally {
      setLoadingHeldSales(false);
    }
  }, [terminalId]);

  // Daily register initialization
  useEffect(() => {
    if (!loading) {
      initializeDailyRegister();
    }
  }, [loading, initializeDailyRegister]);

  // Register modal handling - Only manual opening
  useEffect(() => {
    // Don't automatically open register modal - removed automatic opening logic
  }, [registerStatus.isOpen, loading]);

  const handleRegisterModalClose = () => {
    setShowRegisterModal(false);
    setIsClosingRegister(false);
  };

  const handleCloseRegister = () => {
    setIsClosingRegister(true);
    setShowRegisterModal(true);
  };

  const handleRegisterConfirm = async (amount) => {
    if (isClosingRegister) {
      const closingDetails = calculateClosingDetails();
      try {
        const result = await closeRegister({
          ...closingDetails,
          inCashierAmount: amount.inCashierAmount,
          otherAmount: amount.otherAmount,
        });
        if (!result.success) {
          alert(result.error || "Failed to close register.");
          return;
        }
        refreshRegisterStatus();
        setIsClosingRegister(false);
        setShowRegisterModal(false);
      } catch (error) {
        console.error("Failed to close register:", error);
        alert("Failed to close register. Check console for details.");
      }
    } else {
      try {
        const result = await openRegister({
          user_id: user?.id || userId,
          terminal_id: terminalId,
          opening_cash: amount,
        });
        if (!result.success) {
          alert(result.error || "Failed to open register.");
          return;
        }
        refreshRegisterStatus();
        setShowRegisterModal(false);
      } catch (error) {
        console.error("Failed to open register:", error);
        alert("Failed to open register. Check console for details.");
      }
    }
  };

  // Fetch Next Bill Number
  useEffect(() => {
    const fetchNextBillNumber = async () => {
      try {
        const token = auth?.user?.token;
        const headers = token ? { Authorization: `Bearer ${token}` } : {};
        const response = await axios.get(
          "http://127.0.0.1:8000/api/next-bill-number",
          { headers }
        );
        if (response.data && response.data.next_bill_number) {
          setBillNumber(response.data.next_bill_number);
        } else {
          throw new Error("Invalid bill number format");
        }
      } catch (error) {
        console.error("Error fetching next bill number:", error);
        const timestamp = Date.now();
        const randomNum = Math.floor(1000 + Math.random() * 9000);
        setBillNumber(`BILL-${timestamp}-${randomNum}`);
      }
    };
    fetchNextBillNumber();
  }, []);

  // Fetch Categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true);
        const response = await axios.get(
          "http://127.0.0.1:8000/api/categories"
        );
        const fetchedCategories = Array.isArray(response.data)
          ? response.data
          : response.data.data || [];
        setCategories([
          { id: 0, name: "All Categories" },
          ...fetchedCategories,
        ]);
        if (fetchedCategories.length === 0) {
          setFilterError("No categories available.");
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        setFilterError("Failed to load categories.");
        setCategories([{ id: 0, name: "All Categories" }]);
      } finally {
        setLoadingCategories(false);
      }
    };
    fetchCategories();
  }, []);

  // Fetch Products and Derive Brands
  useEffect(() => {
    setLoadingItems(true);
    setLoadingBrands(true);
    axios
      .get("http://127.0.0.1:8000/api/products")
      .then((response) => {
        if (response.data && Array.isArray(response.data.data)) {
          const productsWithOpeningStock = response.data.data.map((p) => ({
            ...p,
            id: p.product_id,
            stock: parseFloat(p.closing_stock_quantity || 0),
            category_name: p.category || "Unknown Category",
            brand_name: p.supplier || "Unknown Brand",
            // Include variants for batch selection
            variants: p.variants || [],
          }));
          setItems(productsWithOpeningStock);
          // Don't set searchResults here - it will be set by the useEffect below

          const uniqueCategories = [
            { id: 0, name: "All Categories" },
            ...[
              ...new Set(productsWithOpeningStock.map((p) => p.category_name)),
            ]
              .filter((name) => name && name !== "Unknown Category")
              .map((name, index) => ({
                id: index + 1,
                name,
              })),
          ];
          const uniqueBrands = [
            { id: 0, name: "All Brands" },
            ...[...new Set(productsWithOpeningStock.map((p) => p.brand_name))]
              .filter((name) => name && name !== "Unknown Brand")
              .map((name, index) => ({
                id: index + 1,
                name,
              })),
          ];
          setCategories(uniqueCategories);
          setBrands(uniqueBrands);
          if (uniqueCategories.length === 1) {
            setFilterError("No categories available.");
          }
          if (uniqueBrands.length === 1) {
            setFilterError((prev) =>
              prev ? `${prev} No brands available.` : "No brands available."
            );
          }
        } else {
          console.error("Unexpected product data format:", response.data);
          setFilterError("Invalid product data format.");
          setItems([]);
          setSearchResults([]);
          setCategories([{ id: 0, name: "All Categories" }]);
          setBrands([{ id: 0, name: "All Brands" }]);
        }
      })
      .catch((error) => {
        console.error("Error fetching items:", error);
        setFilterError("Failed to load products.");
        setItems([]);
        setSearchResults([]);
        setCategories([{ id: 0, name: "All Categories" }]);
        setBrands([{ id: 0, name: "All Brands" }]);
      })
      .finally(() => {
        setLoadingItems(false);
        setLoadingBrands(false);
      });
  }, []);

  // Fetch Active Discount Schemes
  useEffect(() => {
    setLoadingSchemes(true);
    axios
      .get("http://127.0.0.1:8000/api/discount-schemes")
      .then((response) => {
        if (response.data && Array.isArray(response.data.data)) {
          const formattedSchemes = response.data.data.map((s) => ({
            ...s,
            applies_to: s.applies_to || s.appliesTo,
            start_date: s.start_date || s.startDate,
            end_date: s.end_date || s.endDate,
          }));
          const active = formattedSchemes.filter((scheme) => scheme.active);
          setActiveSchemes(active);
        } else {
          console.error("Unexpected scheme data format:", response.data);
          setActiveSchemes([]);
        }
      })
      .catch((error) => {
        console.error("Error fetching discount schemes:", error);
        setActiveSchemes([]);
      })
      .finally(() => {
        setLoadingSchemes(false);
      });
  }, []);

  // Calculate special discounts
  const calculateSpecialDiscount = useCallback(
    (item, saleType, billDate) => {
      if (!item || (!item.product_id && !item.id)) {
        console.warn("No product_id provided for discount calculation");
        return 0;
      }

      // Find the product in the items array
      const baseProduct = items.find(
        (p) => p.product_id === item.product_id || p.id === item.id
      );
      if (!baseProduct) {
        console.warn(
          `Base product not found for product_id: ${item.product_id || item.id}`
        );
        return 0;
      }

      // If item has variant_id, find the specific variant, otherwise use the base product
      let product;
      if (
        item.variant_id &&
        baseProduct.variants &&
        baseProduct.variants.length > 0
      ) {
        const variant = baseProduct.variants.find(
          (v) => v.product_variant_id === item.variant_id
        );
        if (variant) {
          // Merge base product with variant data
          product = {
            ...baseProduct,
            ...variant,
            variant_id: variant.product_variant_id,
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            sales_price: variant.sales_price,
            mrp: variant.mrp,
          };
        } else {
          console.warn(`Variant not found for variant_id: ${item.variant_id}`);
          product = baseProduct;
        }
      } else {
        product = baseProduct;
      }

      const categoryName = product.category_name || "Unknown";
      if (categoryName === "Unknown") {
        console.warn(
          `No valid category_name for product: ${product.product_name}`
        );
      }

      const basePrice =
        saleType === "Wholesale"
          ? parseFloat(product.wholesale_price || product.sales_price || 0)
          : parseFloat(product.sales_price || 0);
      const qty = parseFloat(item.qty) || 1;
      // Use MRP for special discount calculation, not sales price
      const totalAmount = qty * (product.mrp || 0);

      const applicableScheme = activeSchemes.find((scheme) => {
        if (
          !scheme.active ||
          !isDateWithinScheme(billDate, scheme.start_date, scheme.end_date)
        ) {
          return false;
        }
        const target = scheme.target?.trim().toLowerCase();

        // Create display name for batch-wise matching
        // Use item data first (from cart), then fall back to product data (from items array)
        const batchNumber = item.batch_number || product.batch_number;
        const expiryDate = item.expiry_date || product.expiry_date;

        const batchInfo = batchNumber ? ` (Batch: ${batchNumber})` : "";
        const expiryInfo = expiryDate
          ? ` (Exp: ${expiryDate.split("T")[0]})`
          : "";
        const displayName = `${product.product_name}${batchInfo}${expiryInfo}`;

        const productMatch =
          scheme.applies_to === "product" &&
          (target === (product.product_name?.trim().toLowerCase() || "") ||
            target === (product.description?.trim().toLowerCase() || "") ||
            target === displayName.trim().toLowerCase());
        const categoryMatch =
          scheme.applies_to === "category" &&
          categoryName &&
          target === categoryName?.trim().toLowerCase();

        return productMatch || categoryMatch;
      });

      if (!applicableScheme) {
        return 0;
      }

      let discount = 0;
      if (applicableScheme.type === "percentage") {
        discount = (totalAmount * parseFloat(applicableScheme.value)) / 100;
      } else if (applicableScheme.type === "amount") {
        discount = parseFloat(applicableScheme.value) * qty;
      }

      return discount >= 0 ? discount : 0;
    },
    [items, activeSchemes]
  );

  // Generate filtered product options with variants (similar to SalesInvoice)
  const getFilteredProductOptions = useMemo(() => {
    const options = [];
    items.forEach((product) => {
      if (product.variants && product.variants.length > 0) {
        // Product has variants - create option for each variant
        product.variants.forEach((variant) => {
          const categoryName = product.category_name || "Unknown Category";
          const brandName = product.brand_name || "Unknown Brand";
          const stockInfo = variant.closing_stock_quantity || 0;
          const batchInfo = variant.batch_number
            ? ` (Batch: ${variant.batch_number})`
            : "";
          const expiryInfo = variant.expiry_date
            ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
            : "";

          options.push({
            ...product,
            ...variant,
            // Use variant-specific values
            id: product.id,
            product_id: product.product_id,
            variant_id: variant.product_variant_id,
            display_name: `${product.product_name}${batchInfo}${expiryInfo}`,
            stock: parseFloat(stockInfo),
            sales_price: parseFloat(variant.sales_price || 0),
            mrp: parseFloat(variant.mrp || 0),
            buying_cost: parseFloat(variant.buying_cost || 0),
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            store_location:
              variant.store_location || product.store_location || "N/A",
            category_name: categoryName,
            brand_name: brandName,
          });
        });
      } else {
        // No variants - add product as is
        options.push({
          ...product,
          display_name: product.product_name,
          stock: parseFloat(product.closing_stock_quantity || 0),
        });
      }
    });
    return options;
  }, [items]);

  // Debounced Search (updated to use variant-aware options)
  const debouncedSearch = useMemo(
    () =>
      debounce((query, categoryName, brandName) => {
        const productOptions = getFilteredProductOptions; // This is already an array from useMemo
        if (!Array.isArray(productOptions)) {
          setSearchResults([]);
          setFilterError("No products available to filter.");
          return;
        }

        let filtered = [...productOptions];

        if (categoryName !== "All Categories") {
          filtered = filtered.filter(
            (item) => item.category_name === categoryName
          );
        }

        if (brandName !== "All Brands") {
          filtered = filtered.filter((item) => item.brand_name === brandName);
        }

        if (query.trim() !== "") {
          const lowerCaseQuery = query.toLowerCase().trim();
          const startsWithOnly = localStorage.getItem("productSearchStartsWithOnly") === "true";
          filtered = filtered.filter((item) => {
            if (!item) return false;
            if (startsWithOnly) {
              return (
                (item.product_name &&
                  item.product_name.toLowerCase().trim().startsWith(lowerCaseQuery)) ||
                (item.display_name &&
                  item.display_name.toLowerCase().trim().startsWith(lowerCaseQuery)) ||
                (item.item_code && String(item.item_code).startsWith(query.trim())) ||
                (item.barcode && String(item.barcode).startsWith(query.trim())) ||
                (item.batch_number && String(item.batch_number).startsWith(query.trim()))
              );
            } else {
              return (
                (item.product_name &&
                  item.product_name.toLowerCase().trim().includes(lowerCaseQuery)) ||
                (item.display_name &&
                  item.display_name.toLowerCase().trim().includes(lowerCaseQuery)) ||
                (item.item_code && String(item.item_code).includes(query.trim())) ||
                (item.barcode && String(item.barcode).includes(query.trim())) ||
                (item.batch_number && String(item.batch_number).includes(query.trim()))
              );
            }
          });
        }

        setSearchResults(filtered);
        setFilterError(
          filtered.length === 0 ? "No products match the filters." : null
        );
      }, 300),
    [items, getFilteredProductOptions]
  );

  const handleSearch = (query) => {
    setSearchQuery(query);
    debouncedSearch(query, selectedCategory, selectedBrand);
  };

  // Initialize search results with variant-aware options when items change
  useEffect(() => {
    if (items.length > 0) {
      // Trigger initial search to populate searchResults with variant-aware options
      debouncedSearch("", selectedCategory, selectedBrand);
    }
  }, [items, debouncedSearch, selectedCategory, selectedBrand]);

  // Apply discounts to products (only for special discounts)
  useEffect(() => {
    if (activeSchemes.length > 0 || !loadingSchemes) {
      setProducts((prevProducts) =>
        prevProducts.map((product) => {
          const { schemeName } = applyDiscountScheme(
            product,
            saleType,
            activeSchemes
          );
          const productWithQty = { ...product, qty: product.qty || 1 };
          const totalSpecialDiscount = calculateSpecialDiscount(
            productWithQty,
            saleType,
            new Date().toISOString().split("T")[0]
          );
          const specialDiscountPerUnit =
            totalSpecialDiscount / (product.qty || 1);
          const discountPerUnit = product.discountPerUnit || 0;
          const combinedDiscountPerUnit =
            discountPerUnit + specialDiscountPerUnit;
          const qty = product.qty || 1;
          const total = qty * ((product.mrp || 0) - combinedDiscountPerUnit);

          return {
            ...product,
            schemeName: schemeName,
            specialDiscountPerUnit: specialDiscountPerUnit,
            discount: combinedDiscountPerUnit * qty,
            total: total >= 0 ? total : 0,
          };
        })
      );
    }
  }, [saleType, activeSchemes, loadingSchemes, calculateSpecialDiscount]);

  // Update product prices when saleType changes
  useEffect(() => {
    setProducts((prevProducts) =>
      prevProducts.map((product) => {
        const price =
          saleType === "Wholesale"
            ? parseFloat(product.wholesale_price || product.sales_price || 0)
            : parseFloat(product.sales_price || 0);

        const mrp = parseFloat(product.mrp || 0);
        const discountPerUnit = Math.max(0, mrp - price);
        const productWithQty = { ...product, qty: product.qty || 1 };
        const totalSpecialDiscount = calculateSpecialDiscount(
          productWithQty,
          saleType,
          new Date().toISOString().split("T")[0]
        );
        const specialDiscountPerUnit =
          totalSpecialDiscount / (product.qty || 1);
        const combinedDiscountPerUnit =
          discountPerUnit + specialDiscountPerUnit;
        const qty = product.qty || 1;
        const total = qty * (mrp - combinedDiscountPerUnit);

        return {
          ...product,
          price: mrp, // Use MRP as base price like POSForm
          discountPerUnit: discountPerUnit,
          specialDiscountPerUnit: specialDiscountPerUnit,
          discount: combinedDiscountPerUnit * qty,
          total: total >= 0 ? total : 0,
        };
      })
    );
  }, [saleType, calculateSpecialDiscount]);

  // Function to refresh a specific product
  const refreshSpecificProduct = useCallback(async (productId, variantId = null) => {
    try {
      const response = await axios.get(`http://127.0.0.1:8000/api/products/${productId}`);
      if (response.data && response.data.data) {
        const updatedProduct = response.data.data;
        const categoryName = updatedProduct.category || updatedProduct.category_name || "Unknown Category";

        const processedProduct = {
          ...updatedProduct,
          id: updatedProduct.product_id,
          stock: parseFloat(updatedProduct.closing_stock_quantity || 0),
          category_name: categoryName,
          brand_name: updatedProduct.supplier || "Unknown Brand",
          sales_price: parseFloat(updatedProduct.sales_price || 0),
          mrp: parseFloat(updatedProduct.mrp || 0),
          supplier: updatedProduct.supplier || "N/A",
          store_location: updatedProduct.store_location || "N/A",
          variants: updatedProduct.variants || [],
        };

        // Update the items array with the refreshed product
        setItems(prevItems => {
          return prevItems.map(item => {
            if (item.product_id === productId || item.id === productId) {
              return processedProduct;
            }
            return item;
          });
        });

        // Update search results if they contain this product
        setSearchResults(prevResults => {
          return prevResults.map(item => {
            if (item.product_id === productId || item.id === productId) {
              return processedProduct;
            }
            return item;
          });
        });

        console.log(`Refreshed product: ${updatedProduct.product_name}`);
        return processedProduct;
      }
    } catch (error) {
      console.error(`Error refreshing product ${productId}:`, error);
      return null;
    }
  }, []);

  // Add Product to Bill
  const addProductToTable = (item) => {
    if (!item || (!item.product_id && !item.id)) {
      alert("Invalid product selected.");
      return;
    }

    setSelectedProductId(item.product_id || item.id);

    const availableStock = parseFloat(item.stock || 0);
    if (isNaN(availableStock)) {
      alert(`Invalid stock quantity for ${item.product_name}.`);
      return;
    }

    // Check if stock is critically low (less than 3)
    if (availableStock < 3) {
      if (!allowMinusStockBilling) {
        setLowStockWarning({
          productName: item.product_name,
          remainingStock: availableStock,
          productId: item.id,
        });
        return;
      }
      // If minus stock billing is enabled, do not show popup, just proceed
    }

    const qtyToAdd = 1;
    // For variant products, match by both product_id and variant_id
    // For non-variant products, match by product_id only
    const existingProductIndex = products.findIndex((p) => {
      if (item.variant_id) {
        return (
          (p.product_id === item.product_id || p.id === item.id) &&
          p.variant_id === item.variant_id
        );
      } else {
        return (
          (p.product_id === item.product_id || p.id === item.id) &&
          !p.variant_id
        );
      }
    });
    const newTotalQty =
      existingProductIndex >= 0
        ? products[existingProductIndex].qty + qtyToAdd
        : qtyToAdd;

    if (newTotalQty > availableStock) {
      if (!allowMinusStockBilling) {
        alert(`Insufficient stock for ${item.product_name}! Only ${availableStock} available.`);
        return;
      }
      // If minus stock billing is enabled, allow adding product without alert
    }

    const { schemeName, free_qty } = applyDiscountScheme(item, saleType, activeSchemes);

    const productWithQty = { ...item, qty: qtyToAdd };
    const totalSpecialDiscount = calculateSpecialDiscount(
      productWithQty,
      saleType,
      new Date().toISOString().split("T")[0]
    );
    const specialDiscountPerUnit = totalSpecialDiscount / qtyToAdd;

    const discountAmountPerUnit = Math.max(
      0,
      (item.mrp || 0) - (item.sales_price || 0)
    );

    let updatedProducts = [...products];

    if (existingProductIndex >= 0) {
      const existingProduct = updatedProducts[existingProductIndex];
      const newQuantity = (existingProduct.qty || 0) + qtyToAdd;

      // Show warning but don't block the sale
      if (newQuantity > availableStock) {
        setStockWarning(
          `Warning: Insufficient stock for ${item.product_name}! Only ${availableStock} available. You're adding ${newQuantity} (including ${existingProduct.qty || 0} already in bill).`
        );
      } else {
        setStockWarning("");
      }

      const mrp = parseFloat(item.mrp || existingProduct.mrp || 0);
      updatedProducts[existingProductIndex] = {
        ...existingProduct,
        qty: newQuantity,
        price: mrp, // Use MRP consistently
        discountPerUnit:
          existingProduct.discountPerUnit || discountAmountPerUnit,
        specialDiscountPerUnit:
          existingProduct.specialDiscountPerUnit || specialDiscountPerUnit,
        discount:
          ((existingProduct.discountPerUnit || discountAmountPerUnit) +
            (existingProduct.specialDiscountPerUnit ||
              specialDiscountPerUnit)) *
          newQuantity,
        discount_percentage: existingProduct.discount_percentage || 0,
        schemeName: schemeName,
        total:
          newQuantity *
          ((existingProduct.mrp || 0) -
            ((existingProduct.discountPerUnit || discountAmountPerUnit) +
              (existingProduct.specialDiscountPerUnit ||
                specialDiscountPerUnit))),
        supplier: item.supplier,
        category: item.category,
        store_location: item.store_location,
        free_qty: free_qty || 0, // Add free_qty
      };
      setProducts(updatedProducts);
    } else {
      // Show warning for new product but don't block the sale
      if (qtyToAdd > availableStock) {
        setStockWarning(
          `Warning: Insufficient stock for ${item.product_name}! Only ${availableStock} available. You're adding ${qtyToAdd}.`
        );
      } else {
        setStockWarning("");
      }

      const mrp = parseFloat(item.mrp || 0);
      const newProduct = {
        ...item,
        qty: qtyToAdd,
        price: mrp, // Use MRP like POSForm does
        discountPerUnit: discountAmountPerUnit,
        specialDiscountPerUnit: specialDiscountPerUnit,
        discount: (discountAmountPerUnit + specialDiscountPerUnit) * qtyToAdd,
        discount_percentage:
          item.mrp && item.sales_price
            ? ((discountAmountPerUnit + specialDiscountPerUnit) / item.mrp) *
              100
            : 0,
        schemeName: schemeName,
        total:
          qtyToAdd *
          ((item.mrp || 0) - (discountAmountPerUnit + specialDiscountPerUnit)),
        serialNumber: products.length + 1,
        supplier: item.supplier,
        category: item.category,
        store_location: item.store_location,
        // Include variant information if present
        variant_id: item.variant_id || null,
        batch_number: item.batch_number || null,
        expiry_date: item.expiry_date || null,
        display_name: (item.display_name || item.product_name)?.trim(),
        free_qty: free_qty || 0, // Add free_qty
      };

      setProducts([...products, newProduct]);
    }

    // Update local stock immediately for better UX
    const updateLocalStock = (productId, variantId, qtyUsed) => {
      setItems(prevItems => {
        return prevItems.map(item => {
          if (item.product_id === productId || item.id === productId) {
            // For variant products, update the specific variant stock
            if (variantId && item.variants && item.variants.length > 0) {
              const updatedVariants = item.variants.map(variant => {
                if (variant.product_variant_id === variantId) {
                  return {
                    ...variant,
                    opening_stock_quantity: Math.max(0, (parseFloat(variant.opening_stock_quantity || 0) - qtyUsed))
                  };
                }
                return variant;
              });
              return { ...item, variants: updatedVariants };
            } else {
              // For non-variant products, update the main stock
              return {
                ...item,
                opening_stock_quantity: Math.max(0, (parseFloat(item.opening_stock_quantity || 0) - qtyUsed)),
                stock: Math.max(0, (parseFloat(item.stock || 0) - qtyUsed))
              };
            }
          }
          return item;
        });
      });

      // Also update search results
      setSearchResults(prevResults => {
        return prevResults.map(item => {
          if (item.product_id === productId || item.id === productId) {
            // For variant products, update the specific variant stock
            if (variantId && item.variants && item.variants.length > 0) {
              const updatedVariants = item.variants.map(variant => {
                if (variant.product_variant_id === variantId) {
                  return {
                    ...variant,
                    opening_stock_quantity: Math.max(0, (parseFloat(variant.opening_stock_quantity || 0) - qtyUsed))
                  };
                }
                return variant;
              });
              return { ...item, variants: updatedVariants };
            } else {
              // For non-variant products, update the main stock
              return {
                ...item,
                opening_stock_quantity: Math.max(0, (parseFloat(item.opening_stock_quantity || 0) - qtyUsed)),
                stock: Math.max(0, (parseFloat(item.stock || 0) - qtyUsed))
              };
            }
          }
          return item;
        });
      });
    };

    // Update local stock immediately
    updateLocalStock(item.product_id || item.id, item.variant_id, qtyToAdd);

    // Refresh the specific product from server to get accurate data (in background)
    refreshSpecificProduct(item.product_id || item.id, item.variant_id);

    setSearchQuery("");
  };
  // Helper to calculate product discounts and total price
  const calculateProductDiscounts = (product, qty) => {
    const mrp = parseFloat(product.mrp || 0);
    const salesPrice = parseFloat(product.price || 0);
    const standardDiscountPerUnit = Math.max(0, mrp - salesPrice);

    const productWithQty = { ...product, qty };
    const specialDiscountTotal = calculateSpecialDiscount(
      productWithQty,
      saleType,
      new Date().toISOString().split("T")[0]
    );

    return {
      standardDiscount: standardDiscountPerUnit * qty,
      specialDiscount: specialDiscountTotal,
      totalPrice: salesPrice * qty - specialDiscountTotal,
    };
  };

  // Handle quantity popup
  const handleQuantityClick = (product, index) => {
    setSelectedProductForQty({ ...product, index });
    setTempQuantity(product.qty?.toString() || "");
    setShowQuantityPopup(true);
  };

  const handleQuantityUpdate = () => {
    if (selectedProductForQty && tempQuantity !== "") {
      updateProductQuantity(selectedProductForQty.index, tempQuantity);
      setShowQuantityPopup(false);
      setSelectedProductForQty(null);
      setTempQuantity("");
    }
  };

  const handleQuantityCancel = () => {
    setShowQuantityPopup(false);
    setSelectedProductForQty(null);
    setTempQuantity("");
  };

  // Update Product Quantity
  const updateProductQuantity = (index, newQtyStr) => {
    // Handle empty string (when field is cleared)
    if (newQtyStr === '' || newQtyStr === null || newQtyStr === undefined) {
      setProducts((prevProducts) => {
        return prevProducts.map((product, i) => {
          if (i === index) {
            return {
              ...product,
              qty: 0,
              discount: 0,
              total: 0,
            };
          }
          return product;
        });
      });
      return;
    }

    // Use helper function to parse comma-formatted numbers
    const newQty = parseFloat(newQtyStr.toString().replace(/,/g, ""));

    if (isNaN(newQty) || newQty < 0) {
      console.warn("Invalid quantity input:", newQtyStr);
      return;
    }

    setProducts((prevProducts) => {
      const productToUpdate = prevProducts[index];
      if (!productToUpdate) return prevProducts;

      const availableStock = parseFloat(productToUpdate.stock || 0);

      if (!isNaN(availableStock) && newQty > availableStock) {
        setStockWarning(
          `Warning: Insufficient stock for ${productToUpdate.product_name}! Only ${availableStock} available. You're setting quantity to ${newQty}.`
        );
      } else {
        setStockWarning("");
      }

      // Recalculate free_qty based on new quantity
      const { free_qty } = applyDiscountScheme(
        { ...productToUpdate, qty: newQty },
        saleType,
        activeSchemes
      );

      const discountPerUnit = productToUpdate.discountPerUnit || 0;
      const specialDiscountPerUnit =
        productToUpdate.specialDiscountPerUnit || 0;
      const combinedDiscountPerUnit = discountPerUnit + specialDiscountPerUnit;

      return prevProducts.map((product, i) => {
        if (i === index) {
          const total = newQty * ((product.mrp || 0) - combinedDiscountPerUnit);
          return {
            ...product,
            qty: newQty,
            discount: combinedDiscountPerUnit * newQty, // Fix: multiply by quantity
            total: total >= 0 ? total : 0,
            free_qty: free_qty || 0, // Update free_qty based on new quantity
          };
        }
        return product;
      });
    });
  };

  // Increment/Decrement Quantity
  const incrementQuantity = (index) => {
    const product = products[index];
    const newQty = (product.qty || 0) + 0.1;
    updateProductQuantity(index, newQty);
  };

  const decrementQuantity = (index) => {
    const product = products[index];
    const newQty = Math.max((product.qty || 0) - 0.1, 0);
    updateProductQuantity(index, newQty);
  };

  // Delete Product
  const handleDeleteClick = (index) => {
    setPendingDeleteIndex(index);
    setShowNotification(true);
  };

  const confirmDelete = () => {
    if (pendingDeleteIndex !== null) {
      const productToDelete = products[pendingDeleteIndex];
      const qtyToRestore = productToDelete.qty || 0;

      // Update local stock immediately for better UX
      const updateLocalStock = (productId, variantId, qtyToRestore) => {
        setItems(prevItems => {
          return prevItems.map(item => {
            if (item.product_id === productId || item.id === productId) {
              // For variant products, update the specific variant stock
              if (variantId && item.variants && item.variants.length > 0) {
                const updatedVariants = item.variants.map(variant => {
                  if (variant.product_variant_id === variantId) {
                    return {
                      ...variant,
                      opening_stock_quantity: parseFloat(variant.opening_stock_quantity || 0) + qtyToRestore
                    };
                  }
                  return variant;
                });
                return { ...item, variants: updatedVariants };
              } else {
                // For non-variant products, update the main stock
                return {
                  ...item,
                  opening_stock_quantity: parseFloat(item.opening_stock_quantity || 0) + qtyToRestore,
                  stock: parseFloat(item.stock || 0) + qtyToRestore
                };
              }
            }
            return item;
          });
        });

        // Also update search results
        setSearchResults(prevResults => {
          return prevResults.map(item => {
            if (item.product_id === productId || item.id === productId) {
              // For variant products, update the specific variant stock
              if (variantId && item.variants && item.variants.length > 0) {
                const updatedVariants = item.variants.map(variant => {
                  if (variant.product_variant_id === variantId) {
                    return {
                      ...variant,
                      opening_stock_quantity: parseFloat(variant.opening_stock_quantity || 0) + qtyToRestore
                    };
                  }
                  return variant;
                });
                return { ...item, variants: updatedVariants };
              } else {
                // For non-variant products, update the main stock
                return {
                  ...item,
                  opening_stock_quantity: parseFloat(item.opening_stock_quantity || 0) + qtyToRestore,
                  stock: parseFloat(item.stock || 0) + qtyToRestore
                };
              }
            }
            return item;
          });
        });
      };

      // Update local stock immediately
      updateLocalStock(productToDelete.product_id || productToDelete.id, productToDelete.variant_id, qtyToRestore);

      // Refresh the specific product from server to get accurate data (in background)
      refreshSpecificProduct(productToDelete.product_id || productToDelete.id, productToDelete.variant_id);

      setProducts((prevProducts) =>
        prevProducts
          .filter((_, i) => i !== pendingDeleteIndex)
          .map((p, idx) => ({ ...p, serialNumber: idx + 1 }))
      );
    }
    setShowNotification(false);
    setPendingDeleteIndex(null);
  };

  const cancelDelete = () => {
    setShowNotification(false);
    setPendingDeleteIndex(null);
  };

  const calculateTotals = useCallback(() => {
    let totalQty = 0;
    let subTotalMRP = 0;
    let totalItemDiscounts = 0;
    let totalNormalDiscounts = 0;
    let totalSpecialDiscounts = 0;
    let grandTotalBeforeAdjustments = 0;

    products.forEach((p) => {
      const qty = p.qty || 0;
      const mrp = parseFloat(p.mrp || 0);
      const normalDiscountPerUnit = p.discountPerUnit || 0;
      const specialDiscountPerUnit = p.specialDiscountPerUnit || 0;
      const normalDiscountTotal = normalDiscountPerUnit * qty;
      const specialDiscountTotal = specialDiscountPerUnit * qty;
      const totalDiscountForItem = normalDiscountTotal + specialDiscountTotal;

      totalQty += qty;
      subTotalMRP += mrp * qty;
      totalItemDiscounts += totalDiscountForItem;
      totalNormalDiscounts += normalDiscountTotal;
      totalSpecialDiscounts += specialDiscountTotal;
      grandTotalBeforeAdjustments += p.total || 0;
    });

    const currentTaxRate = parseFloat(tax || 0);
    const currentBillDiscount = parseFloat(billDiscount || 0);
    const currentShipping = parseFloat(shipping || 0);
    const taxAmount = grandTotalBeforeAdjustments * (currentTaxRate / 100);
    const finalTotalDiscount = totalItemDiscounts + currentBillDiscount;
    const finalTotal =
      grandTotalBeforeAdjustments +
      taxAmount -
      currentBillDiscount +
      currentShipping;

    return {
      totalQty,
      subTotalMRP: isNaN(subTotalMRP) ? 0 : subTotalMRP,
      totalItemDiscounts: isNaN(totalItemDiscounts) ? 0 : totalItemDiscounts,
      totalNormalDiscounts: isNaN(totalNormalDiscounts)
        ? 0
        : totalNormalDiscounts,
      totalSpecialDiscounts: isNaN(totalSpecialDiscounts)
        ? 0
        : totalSpecialDiscounts,
      totalBillDiscount: isNaN(currentBillDiscount) ? 0 : currentBillDiscount,
      finalTotalDiscount: isNaN(finalTotalDiscount) ? 0 : finalTotalDiscount,
      taxAmount: isNaN(taxAmount) ? 0 : taxAmount,
      grandTotalBeforeAdjustments: isNaN(grandTotalBeforeAdjustments)
        ? 0
        : grandTotalBeforeAdjustments,
      finalTotal: isNaN(finalTotal) ? 0 : finalTotal,
    };
  }, [products, tax, billDiscount, shipping]);

  const calculateClosingDetails = () => {
    const totals = calculateTotals();
    return {
      salesAmount:
        registerStatus.totalSales !== undefined
          ? registerStatus.totalSales
          : totals.finalTotal,
      totalSalesQty:
        registerStatus.totalSalesQty !== undefined
          ? registerStatus.totalSalesQty
          : totals.totalQty,
      cashOnHand:
        registerStatus.openingCash !== undefined
          ? registerStatus.openingCash
          : registerStatus.cashOnHand,
      inCashierAmount: 0,
      otherAmount: 0,
    };
  };

  // Toggle Fullscreen
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(
          `Error attempting to enable full-screen mode: ${err.message}`
        );
        alert(`Could not enter full-screen mode: ${err.message}`);
      });
      setIsFullScreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
        setIsFullScreen(false);
      }
    }
  };

  const [editingDiscountIndex, setEditingDiscountIndex] = useState(null);
  const [tempDiscountValue, setTempDiscountValue] = useState(0);
  // Hold Sale
  const holdSale = useCallback(async () => {
    if (products.length === 0) {
      alert("Cannot hold an empty sale.");
      return;
    }
    const currentTotals = calculateTotals();
    const saleData = {
      products,
      totals: currentTotals,
      tax,
      billDiscount,
      billDiscountPercentage,
      shipping,
      saleType,
      customerInfo,
      billNumber,
    };
    try {
      const response = await axios.post("/api/holds", {
        terminal_id: terminalId,
        user_id: userId,
        sale_data: saleData,
      });
      if (response.data.status === "success") {
        alert(`Sale held successfully with ID: ${response.data.data.hold_id}`);
        resetPOS(false);
        loadHeldSales();
      } else {
        alert(
          "Failed to hold sale: " + (response.data.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error holding sale:", error);
      alert("Failed to hold sale. Check console for details.");
    }
  }, [
    products,
    calculateTotals,
    tax,
    billDiscount,
    billDiscountPercentage,
    shipping,
    saleType,
    customerInfo,
    billNumber,
  ]);

  // Reset POS
  const resetPOS = useCallback(
    (fetchNewBill = true) => {
      setProducts([]);
      setTax(0);
      setBillDiscount(0);
      setBillDiscountPercentage(0);
      setShipping(0);
      setSearchQuery("");
      // Reset search results with variant-aware options
      debouncedSearch("", "All Categories", "All Brands");
      setCustomerInfo({
        name: "",
        mobile: "",
        bill_number: "",
        userId: "U-1",
        receivedAmount: 0,
      });
      setSelectedCategory("All Categories");
      setSelectedBrand("All Brands");
      setFilterError(null);

      if (fetchNewBill) {
        const fetchNextBillNumber = async () => {
          try {
            const response = await axios.get(
              "http://127.0.0.1:8000/api/next-bill-number"
            );
            setBillNumber(response.data.next_bill_number);
          } catch (error) {
            console.error("Error fetching next bill number post-reset:", error);
            setBillNumber("ERR-XXX");
          }
        };
        fetchNextBillNumber();
      } else {
        setCustomerInfo((prev) => ({ ...prev, bill_number: "" }));
      }
    },
    [items]
  );

  useEffect(() => {
    console.log("Current Products in Cart:", products);
  }, [products]);

  // Open Bill Modal
  const handleOpenBill = useCallback(() => {
    if (products.length === 0) {
      alert("Cannot proceed to payment with an empty bill.");
      return;
    }

    setCustomerInfo((prev) => ({ ...prev, bill_number: billNumber }));
    setShowBillModal(true);
  }, [products, billNumber]);

  // Close Bill Modal
  const closeBillModal = useCallback(
    (saleSaved = false) => {
      setShowBillModal(false);
      if (saleSaved) {
        resetPOS(true);
      }
      setCustomerInfo((prevState) => ({
        ...prevState,
        name: "",
        mobile: "",
        bill_number: "",
        receivedAmount: 0,
      }));
    },
    [resetPOS]
  );

  // Held Sales Functions
  const openHeldSalesList = () => {
    loadHeldSales();
    setShowHeldSalesList(true);
  };

  const closeHeldSalesList = () => {
    setShowHeldSalesList(false);
  };

  const recallHeldSale = async (hold_id) => {
    try {
      const response = await axios.post(`/api/holds/${hold_id}/recall`);
      if (response.data.status === "success") {
        const sale = response.data.data;
        const productsWithSerial =
          sale.products?.map((p, idx) => ({
            ...p,
            serialNumber: idx + 1,
          })) || [];
        setProducts(productsWithSerial);
        setProducts(sale.products || []);
        setTax(sale.tax || 0);
        setBillDiscount(sale.billDiscount || 0);
        setBillDiscountPercentage(sale.billDiscountPercentage || 0);
        setShipping(sale.shipping || 0);
        setSaleType(sale.saleType || "Retail");
        setCustomerInfo(
          sale.customerInfo || {
            name: "",
            mobile: "",
            bill_number: "",
            userId: "U-1",
            receivedAmount: 0,
          }
        );
        setBillNumber(sale.billNumber || "");
        setHeldSales((prevHeldSales) =>
          prevHeldSales.filter((s) => s.hold_id !== hold_id)
        );
        setShowHeldSalesList(false);
        alert(`Recalled sale with ID: ${hold_id}`);
      } else {
        alert(
          "Failed to recall sale: " + (response.data.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error recalling sale:", error);
      alert("Failed to recall sale. Check console for details.");
    }
  };

  const deleteHeldSale = async (hold_id) => {
    try {
      const response = await axios.delete(`/api/holds/${hold_id}`);
      if (response.data.status === "success") {
        alert("Held sale deleted successfully");
        loadHeldSales();
      } else {
        alert(
          "Failed to delete held sale: " +
            (response.data.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error deleting held sale:", error);
      alert("Failed to delete held sale. Check console for details.");
    }
  };

  // Category Colors
  const categoryColors = {
    Milk: "bg-gradient-to-br from-blue-50 to-blue-100",
    Beverages: "bg-gradient-to-br from-green-50 to-green-100",
    Snacks: "bg-gradient-to-br from-yellow-50 to-yellow-100",
    Chocolates: "bg-gradient-to-br from-pink-50 to-pink-100",
    "Instant Food": "bg-gradient-to-br from-orange-50 to-orange-100",
  };

  const totals = calculateTotals();

  // Add this handler for item add
  const handleAddItemFromPOS = () => {
    setShowAddItemForm(true);
  };

  // Add this helper at the top of the file (after imports):
  const allowMinusStockBilling = localStorage.getItem("allowMinusStockBilling") === "true";

  // Read auto-add scanned product setting from localStorage
  const autoAddScannedProductToCart = localStorage.getItem("autoAddScannedProductToCart") === "true";

  const handleItemSelection = (item, { fromBarcodeScan = false } = {}) => {
    if (!item || !item.product_id) {
      console.error("Invalid item selected:", item);
      return;
    }
    setSelectedProductId(item.product_id);
    setSearchQuery(item.product_name?.trim() || "");
    setSearchResults([]);
    setSelectedSearchIndex(-1);
    if (fromBarcodeScan && autoAddScannedProductToCart) {
      setTimeout(() => addProductToTable(item), 0);
    }
  };

  return (
    <div
      className={`min-h-screen w-full p-2 sm:p-4 flex flex-col ${isFullScreen ? "fullscreen-mode" : ""}`}
    >
      <div className="flex-1 grid grid-cols-1 md:grid-cols-[40%_60%] gap-2 sm:gap-4">
        {/* Left Side: Billing System */}
        <div className="relative flex flex-col p-2 bg-white rounded-lg shadow-lg sm:p-4 dark:bg-slate-900 dark:text-white">
          <div className="flex items-center justify-between mb-2 sm:mb-4">
            <h2 className="text-lg font-bold sm:text-xl">Billing</h2>
            <div className="flex gap-1 sm:gap-2">
              <button
                className={`px-2 py-1 sm:px-4 sm:py-2 rounded-lg text-sm sm:text-lg ${
                  saleType === "Retail"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-800"
                }`}
                onClick={() => setSaleType("Retail")}
              >
                Retail
              </button>
              <button
                className={`px-2 py-1 sm:px-4 sm:py-2 rounded-lg text-sm sm:text-lg ${
                  saleType === "Wholesale"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-800"
                }`}
                onClick={() => setSaleType("Wholesale")}
              >
                Wholesale
              </button>
            </div>
          </div>

          {/* Stock Warning */}
          {stockWarning && (
            <div className="p-3 mb-4 text-sm text-orange-800 bg-orange-100 border border-orange-300 rounded-md dark:bg-orange-900 dark:text-orange-200 dark:border-orange-700">
              <div className="flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                {stockWarning}
              </div>
            </div>
          )}

          {/* Bill Table */}
          <div className="overflow-x-auto">
            <table className="w-full border">
              <thead className="bg-gray-200 dark:bg-slate-700">
                <tr>
                  <th className="p-1 text-xs text-left sm:p-2 sm:text-sm">
                    No
                  </th>
                  <th className="p-1 text-xs text-left sm:p-2 sm:text-sm">
                    Item
                  </th>
                  <th className="p-1 text-xs text-center sm:p-2 sm:text-sm">
                    Qty
                  </th>
                  <th className="p-1 text-xs text-center sm:p-2 sm:text-sm">
                    Free
                  </th>
                  <th className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                    MRP
                  </th>
                  <th className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                    Discount
                  </th>
                  <th className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                    Price
                  </th>
                  <th className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                    Total
                  </th>
                  <th className="p-1 sm:p-2"></th>
                </tr>
              </thead>
              <tbody>
                {products.length === 0 ? (
                  <tr>
                    <td
                      colSpan="8"
                      className="p-4 text-center text-gray-500 dark:text-white"
                    >
                      No items added to the bill yet.
                    </td>
                  </tr>
                ) : (
                  products.map((product, index) => {
                    const serialNumber =
                      product.serialNumber !== undefined
                        ? product.serialNumber
                        : index + 1;

                    const qty = product.qty || 0;
                    const mrp = parseFloat(product.mrp || 0);
                    // const salesPrice = parseFloat(product.sales_price || 0);
                    const price = product.price || 0;
                    const itemDiscount = product.discount || 0;
                    const specialDiscount = product.specialDiscount || 0;
                    const totalDiscount =
                      (itemDiscount || 0) + (specialDiscount || 0);
                    // const price = product.price || 0;
                    const salesPrice = parseFloat(product.sales_price || 0);
                    const total = qty * mrp - (itemDiscount + specialDiscount);
                    const totalSafe = total >= 0 ? total : 0;

                    return (
                      <tr
                        key={`${product.id || product.product_id || index}-${serialNumber}-${index}`}
                        className="border-b"
                      >
                        <td className="p-1 text-xs sm:p-2 sm:text-sm">
                          {serialNumber}
                        </td>
                        <td className="p-1 sm:p-2">
                          <div className="flex flex-col">
                            <span className="text-xs font-semibold sm:text-sm">
                              {product.product_name}
                            </span>
                          </div>
                        </td>
                        <td className="p-1 text-center sm:p-2">
                          <div className="flex items-center justify-center gap-1 sm:gap-2">
                            <div
                              className="w-16 p-1 text-sm text-center border rounded cursor-pointer bg-blue-50 hover:bg-blue-100 dark:bg-slate-700 dark:text-white dark:hover:bg-slate-600 sm:text-lg transition-colors"
                              onClick={() =>
                                handleQuantityClick(product, index)
                              }
                              title="Click to edit quantity"
                            >
                              {qty}
                            </div>
                          </div>
                        </td>
                        <td className="p-1 text-center sm:p-2">
                          <div className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                            {product.free_qty || 0}
                          </div>
                        </td>
                        <td className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                          {formatNumberWithCommas(price.toFixed(2))}
                        </td>
                        {/* <td className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                          {formatNumberWithCommas(salesPrice.toFixed(2))}
                        </td> */}

                        <td className="px-3 text-right border-r dark:border-gray-700">
                          <div className="flex flex-col">
                            <input
                              type="number"
                              className="w-20 py-1 text-sm text-right bg-gray-100 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                              value={formatNumberWithCommas(
                                (product.discount || 0).toFixed(2)
                              )}
                              onChange={(e) => {
                                const newTotalDiscount = parseFloat(
                                  e.target.value.replace(/,/g, "")
                                );
                                if (
                                  isNaN(newTotalDiscount) ||
                                  newTotalDiscount < 0
                                )
                                  return;

                                setProducts((prevProducts) =>
                                  prevProducts.map((p, i) => {
                                    if (i === index) {
                                      const qty = p.qty || 1;
                                      // Calculate discount per unit from total discount
                                      const newDiscountPerUnit =
                                        qty > 0 ? newTotalDiscount / qty : 0;
                                      const specialDiscountPerUnit =
                                        p.specialDiscountPerUnit || 0;

                                      // Update normal discount per unit (subtract special discount)
                                      const normalDiscountPerUnit = Math.max(
                                        0,
                                        newDiscountPerUnit -
                                          specialDiscountPerUnit
                                      );

                                      // Recalculate free_qty based on current product state
                                      const { free_qty } = applyDiscountScheme(
                                        { ...p, qty: qty },
                                        saleType,
                                        activeSchemes
                                      );

                                      const newTotal =
                                        qty * mrp - newTotalDiscount;

                                      return {
                                        ...p,
                                        discountPerUnit: normalDiscountPerUnit,
                                        discount: newTotalDiscount,
                                        price: mrp,
                                        total: newTotal >= 0 ? newTotal : 0,
                                        free_qty: free_qty || 0, // Update free_qty
                                      };
                                    }
                                    return p;
                                  })
                                );
                              }}
                              onFocus={(e) => e.target.select()}
                              placeholder="Total Discount"
                            />
                          </div>
                        </td>
                        {/* <td className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                          {formatNumberWithCommas(price.toFixed(2))}
                        </td> */}
                        <td className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                          {formatNumberWithCommas(salesPrice.toFixed(2))}
                        </td>
                        <td className="p-1 text-xs text-right sm:p-2 sm:text-sm">
                          {formatNumberWithCommas(totalSafe.toFixed(2))}
                        </td>
                        <td className="p-1 text-xs text-center sm:p-2">
                          <button
                            onClick={() => handleDeleteClick(index)}
                            className="p-1 text-white bg-red-500 rounded-lg sm:p-2 dark:text-slate-900"
                          >
                            <Trash2 size={16} className="sm:w-5 sm:h-5" />
                          </button>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Totals Section */}
          <div className="flex flex-col gap-4 mt-2 sm:mt-4 dark:bg-slate-800 sm:flex-row sm:gap-6">
            <div className="flex-1">
              <div className="flex items-center gap-2 mt-1 text-sm font-semibold sm:text-lg">
                <span>Total Item Quantity:</span>
                <span>
                  {formatNumberWithCommas((totals.totalQty || 0).toFixed(2))}
                </span>
              </div>
              {/* Free Qty Summary */}
              <div className="flex items-center gap-2 mt-1 text-sm font-semibold sm:text-lg">
                <span>Total Free Qty:</span>
                <span>
                  {formatNumberWithCommas((products.reduce((sum, p) => sum + (p.free_qty || 0), 0)).toFixed(2))}
                </span>
              </div>
            </div>
            <div className="flex-1 space-y-2 sm:space-y-3">
              <div className="grid grid-cols-1 gap-4 sm:gap-6 min-w-fit">
                <div className="flex-shrink-0">
                  <label className="block text-xs font-medium text-gray-700 whitespace-normal sm:text-sm dark:text-white">
                    Tax (%):
                  </label>
                  <div className="flex items-center gap-1 sm:gap-2">
                    <button
                      onClick={() => setTax((prev) => Math.max(prev - 1, 0))}
                      className="p-2 bg-gray-300 rounded-lg sm:p-3 dark:bg-slate-800"
                    >
                      <Minus size={16} className="sm:w-5 sm:h-5" />
                    </button>
                    <input
                      ref={taxInputRef}
                      type="number"
                      value={tax}
                      onChange={(e) => {
                        const value = e.target.value;
                        setTax(value === "" ? 0 : parseFloat(value) || 0);
                      }}
                      className="w-20 p-2 text-xs text-center placeholder-gray-400 placeholder-opacity-75 transition-colors bg-white border border-gray-300 rounded-lg sm:text-sm sm:p-3 dark:bg-slate-800 sm:w-24 focus:ring-2 focus:ring-blue-500 placeholder-italic"
                      min="0"
                      step="0.01"
                      placeholder="e.g., 5.0%"
                      aria-label="Tax percentage"
                    />
                    <button
                      onClick={() => setTax((prev) => prev + 1)}
                      className="p-2 bg-gray-300 rounded-lg sm:p-3 dark:bg-slate-800"
                    >
                      <Plus size={16} className="sm:w-5 sm:h-5" />
                    </button>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <label className="block text-xs font-medium text-gray-700 whitespace-normal sm:text-sm dark:bg-slate-800">
                    Discount (Rs.):
                  </label>
                  <div className="flex items-center gap-1 sm:gap-2">
                    <button
                      onClick={() => {
                        const newDiscount = Math.max(billDiscount - 100, 0);
                        setBillDiscount(newDiscount);
                        const currentSubtotal = totals.grandTotalBeforeAdjustments;
                        const percentage = calculatePercentageFromDiscount(newDiscount, currentSubtotal);
                        setBillDiscountPercentage(percentage);
                      }}
                      className="p-2 bg-gray-300 rounded-lg sm:p-3 dark:bg-slate-800"
                    >
                      <Minus size={16} className="sm:w-5 sm:h-5" />
                    </button>
                    <input
                      ref={discountInputRef}
                      type="number"
                      value={billDiscount}
                      onChange={(e) => {
                        const value = e.target.value;
                        const discountAmount = value === "" ? 0 : parseFloat(value) || 0;
                        setBillDiscount(discountAmount);

                        // Calculate and update percentage based on discount amount
                        const currentSubtotal = totals.grandTotalBeforeAdjustments;
                        const percentage = calculatePercentageFromDiscount(discountAmount, currentSubtotal);
                        setBillDiscountPercentage(percentage);
                      }}
                      className="w-20 p-2 text-xs text-center placeholder-gray-300 placeholder-opacity-75 transition-colors bg-white border border-gray-300 rounded-lg sm:text-sm sm:p-3 dark:bg-slate-800 sm:w-24 focus:ring-2 focus:ring-blue-500 placeholder-italic"
                      min="0"
                      step="1"
                      placeholder="e.g., 100 Rs."
                      aria-label="Bill discount amount"
                    />
                    <button
                      onClick={() => {
                        const newDiscount = billDiscount + 100;
                        setBillDiscount(newDiscount);
                        const currentSubtotal = totals.grandTotalBeforeAdjustments;
                        const percentage = calculatePercentageFromDiscount(newDiscount, currentSubtotal);
                        setBillDiscountPercentage(percentage);
                      }}
                      className="p-2 bg-gray-300 rounded-lg sm:p-3 dark:bg-slate-800"
                    >
                      <Plus size={16} className="sm:w-5 sm:h-5" />
                    </button>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <label className="block text-xs font-medium text-gray-700 whitespace-normal sm:text-sm dark:bg-slate-800">
                    Discount (%):
                  </label>
                  <div className="flex items-center gap-1 sm:gap-2">
                    <button
                      onClick={() =>
                        setBillDiscountPercentage((prev) => Math.max(prev - 1, 0))
                      }
                      className="p-2 bg-gray-300 rounded-lg sm:p-3 dark:bg-slate-800"
                    >
                      <Minus size={16} className="sm:w-5 sm:h-5" />
                    </button>
                    <input
                      type="number"
                      value={billDiscountPercentage}
                      onChange={(e) => {
                        const value = e.target.value;
                        const percentage = value === "" ? 0 : parseFloat(value) || 0;
                        setBillDiscountPercentage(percentage);

                        // Calculate and update discount amount based on percentage
                        const currentSubtotal = totals.grandTotalBeforeAdjustments;
                        const discountAmount = calculateDiscountFromPercentage(percentage, currentSubtotal);
                        setBillDiscount(discountAmount);
                      }}
                      className="w-20 p-2 text-xs text-center placeholder-gray-300 placeholder-opacity-75 transition-colors bg-white border border-gray-300 rounded-lg sm:text-sm sm:p-3 dark:bg-slate-800 sm:w-24 focus:ring-2 focus:ring-blue-500 placeholder-italic"
                      min="0"
                      max="100"
                      step="0.1"
                      placeholder="e.g., 10%"
                      aria-label="Bill discount percentage"
                    />
                    <button
                      onClick={() => setBillDiscountPercentage((prev) => Math.min(prev + 1, 100))}
                      className="p-2 bg-gray-300 rounded-lg sm:p-3 dark:bg-slate-800"
                    >
                      <Plus size={16} className="sm:w-5 sm:h-5" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="p-2 bg-gray-100 border border-gray-200 rounded-lg sm:p-4 dark:bg-slate-800">
                <div className="space-y-2 sm:space-y-3">
                  <div className="flex justify-between text-xs sm:text-sm">
                    <span>Sub Total (MRP):</span>
                    <span>
                      Rs.{" "}
                      {formatNumberWithCommas(
                        (totals.subTotalMRP ?? 0).toFixed(2)
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-xs text-red-600 sm:text-sm">
                    <span>(-) Item Discounts:</span>
                    <span>
                      Rs.{" "}
                      {formatNumberWithCommas(
                        (totals.totalItemDiscounts ?? 0).toFixed(2)
                      )}
                    </span>
                  </div>
                  {/* <div className="flex justify-between ml-4 text-xs text-red-500 sm:text-sm">
                    <span>• Normal Discounts:</span>
                    <span>
                      Rs.{" "}
                      {formatNumberWithCommas(
                        (totals.totalNormalDiscounts ?? 0).toFixed(2)
                      )}
                    </span>
                  </div> */}
                  {/* <div className="flex justify-between ml-4 text-xs text-red-500 sm:text-sm">
                    <span>• Special Discounts:</span>
                    <span>
                      Rs.{" "}
                      {formatNumberWithCommas(
                        (totals.totalSpecialDiscounts ?? 0).toFixed(2)
                      )}
                    </span>
                  </div> */}
                  {/* <div className="flex justify-between text-xs sm:text-sm">
                    <span>Net Item Total:</span>
                    <span>
                      Rs.{" "}
                      {formatNumberWithCommas(
                        (totals.grandTotalBeforeAdjustments ?? 0).toFixed(2)
                      )}
                    </span>
                  </div> */}
                  <div className="flex justify-between text-xs text-yellow-600 sm:text-sm">
                    <span>(+) Tax ({parseFloat(tax || 0).toFixed(1)}%):</span>
                    <span>
                      Rs. {formatNumberWithCommas(totals.taxAmount.toFixed(2))}
                    </span>
                  </div>
                  <div className="flex justify-between text-xs text-red-600 sm:text-sm">
                    <span>(-) Bill Discount:</span>
                    <span>
                      Rs.{" "}
                      {formatNumberWithCommas(
                        totals.totalBillDiscount.toFixed(2)
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm font-bold sm:text-lg">
                    <span>Grand Total:</span>
                    <span>
                      Rs.{" "}
                      {formatNumberWithCommas(
                        (
                          totals.grandTotalBeforeAdjustments +
                          totals.taxAmount -
                          totals.totalBillDiscount
                        ).toFixed(2)
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="sticky bottom-0 pt-2 bg-white dark:bg-slate-800 sm:pt-4">
            <div className="flex gap-1 sm:gap-2">
              <button
                className="flex items-center justify-center flex-1 gap-2 p-2 text-sm text-white bg-pink-500 rounded-lg sm:p-4 dark:text-white sm:text-lg"
                onClick={holdSale}
                disabled={products.length === 0}
              >
                <FaPause /> Hold
              </button>
              <button
                className="flex items-center justify-center flex-1 gap-2 p-2 text-sm text-white bg-red-500 rounded-lg sm:p-4 dark:text-white sm:text-lg"
                onClick={() => resetPOS(false)}
              >
                <FaRedo /> Reset
              </button>
              <button
                className="flex items-center justify-center flex-1 gap-2 p-2 text-sm text-white bg-green-500 rounded-lg sm:p-4 dark:text-white sm:text-lg"
                onClick={handleOpenBill}
                disabled={products.length === 0}
              >
                <FaCreditCard /> Pay Now
              </button>
            </div>
          </div>
        </div>

        {/* Right Side: Product Selection */}
        <div className="flex flex-col p-2 bg-white rounded-lg shadow-lg sm:p-4 dark:bg-slate-800">
          {/* Search Bar */}
          <div className="flex items-center gap-2 mb-2">
            <input
              ref={searchInputRef}
              type="text"
              className="w-full p-2 text-sm border rounded-lg sm:p-4 sm:text-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Scan/Search by Code/Name"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              disabled={
                loadingItems ||
                loadingSchemes ||
                loadingCategories ||
                loadingBrands
              }
            />
            {/* Add Item Button */}
            <button
              className="p-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-400"
              title="Add New Item"
              onClick={handleAddItemFromPOS}
            >
              <Plus size={20} />
            </button>
          </div>
          {(loadingItems ||
            loadingSchemes ||
            loadingCategories ||
            loadingBrands) && (
            <span className="text-xs text-gray-500 dark:text-white">
              Loading...
            </span>
          )}

          {/* Action Buttons */}
          <div className="flex gap-1 mb-2 sm:gap-2 sm:mb-4">
            <button
              className="p-2 text-white bg-blue-500 rounded-lg sm:p-3 dark:text-slate-800"
              onClick={openHeldSalesList}
            >
              <ClipboardList size={20} className="sm:w-7 sm:h-7" />
            </button>
            <button
              className="p-2 text-white bg-green-500 rounded-lg shadow dark:text-slate-800 hover:bg-green-600"
              title={
                registerStatus.isOpen
                  ? "Update Opening Cash"
                  : !registerStatus.dailyInitialized
                  ? "Daily Register Setup"
                  : "Add Opening Cash"
              }
              onClick={() => setShowRegisterModal(true)}
            >
              <LockOpen size={24} />
            </button>
            {registerStatus.isOpen && (
              <button
                className="p-2 text-white bg-red-500 rounded-lg shadow dark:text-slate-800 hover:bg-red-600"
                title="Close Register"
                onClick={handleCloseRegister}
              >
                <LogOut size={24} />
              </button>
            )}
            <button
              className="p-2 text-white bg-green-500 rounded-lg sm:p-3 dark:text-slate-800"
              onClick={toggleFullScreen}
            >
              {isFullScreen ? (
                <Minimize size={20} className="sm:w-7 sm:h-7" />
              ) : (
                <Maximize size={20} className="sm:w-7 sm:h-7" />
              )}
            </button>
            <button
              className="p-2 text-white bg-purple-500 rounded-lg sm:p-3 dark:text-slate-800"
              onClick={() => setShowCalculatorModal(true)}
            >
              <Calculator size={20} className="sm:w-7 sm:h-7" />
            </button>
            <button
              className="p-2 text-white bg-yellow-500 rounded-lg sm:p-3 dark:text-slate-800"
              onClick={() => navigate("/Dashboard")}
            >
              <LayoutDashboard size={20} className="sm:w-7 sm:h-7" />
            </button>
          </div>

          {/* Category Filter - Modified */}
          <div className="mb-2 sm:mb-4">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium dark:text-white">
                Categories:
              </span>
              {categories.length > 8 && (
                <select
                  className="p-1 text-sm border rounded-lg dark:bg-slate-700 dark:text-white"
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    debouncedSearch(searchQuery, e.target.value, selectedBrand);
                  }}
                >
                  {categories.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              )}
            </div>

            {categories.length <= 8 && (
              <div className="flex gap-1 pb-2 overflow-x-auto sm:gap-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    className={`flex-shrink-0 p-2 sm:p-3 rounded-lg text-sm sm:text-lg ${
                      selectedCategory === category.name
                        ? "bg-blue-500 text-white dark:text-slate-800"
                        : "bg-gray-200 dark:bg-slate-700 dark:text-white"
                    }`}
                    onClick={() => {
                      setSelectedCategory(category.name);
                      debouncedSearch(
                        searchQuery,
                        category.name,
                        selectedBrand
                      );
                    }}
                    disabled={loadingCategories}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Brand Filter - Modified */}
          <div className="mb-2 sm:mb-4">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium dark:text-white">
                Brands:
              </span>
              {brands.length > 8 && (
                <select
                  className="p-1 text-sm border rounded-lg dark:bg-slate-700 dark:text-white"
                  value={selectedBrand}
                  onChange={(e) => {
                    setSelectedBrand(e.target.value);
                    debouncedSearch(
                      searchQuery,
                      selectedCategory,
                      e.target.value
                    );
                  }}
                >
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.name}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              )}
            </div>

            {brands.length <= 8 && (
              <div className="flex gap-1 pb-2 overflow-x-auto sm:gap-2">
                {brands.map((brand) => (
                  <button
                    key={brand.id}
                    className={`flex-shrink-0 p-2 sm:p-3 rounded-lg text-sm sm:text-lg ${
                      selectedBrand === brand.name
                        ? "bg-blue-500 text-white dark:text-slate-800"
                        : "bg-gray-200 dark:bg-slate-700 dark:text-white"
                    }`}
                    onClick={() => {
                      setSelectedBrand(brand.name);
                      debouncedSearch(
                        searchQuery,
                        selectedCategory,
                        brand.name
                      );
                    }}
                    disabled={loadingBrands}
                  >
                    {brand.name}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Error Message */}
          {filterError && (
            <div className="mb-2 text-sm text-red-500">{filterError}</div>
          )}

          {/* Product Results */}
          <div className="grid grid-cols-2 dark:bg-slate-600 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3 max-h-[50vh] sm:max-h-[60vh] overflow-auto">
            {searchResults.length === 0 ? (
              <div className="text-center text-gray-500 col-span-full dark:bg-slate-600 dark:text-white">
                No products found.
              </div>
            ) : (
              searchResults.map((item, index) => (
                <div
                  key={`${item.id || item.product_id || index}-${index}`}
                  className={`
                    relative p-2 sm:p-3 rounded-xl shadow-lg cursor-pointer
                    border-4 border-cyan-500 dark:border-cyan-700 bg-blue-500 dark:bg-slate-700
                    ${
                      categoryColors[item.category_name] ||
                      "bg-gradient-to-br from-blue-200 to-blue-100 dark:bg-slate-700 dark:text-white"
                    }
                    hover:shadow-xl hover:border-purple-800
                    active:scale-95 transition-all duration-200
                    flex flex-col justify-between min-h-[140px] sm:min-h-[160px]
                  `}
                  onClick={() => addProductToTable(item)}
                >
                  <div className="flex items-start justify-between mb-1 dark:bg-slate-200">
                    <div className="flex flex-col">
                      <div className="text-xs font-semibold text-green-700 sm:text-sm">
                        {formatNumberWithCommas(
                          applyDiscountScheme(item, saleType, activeSchemes)
                            .price
                        )}{" "}
                        Rs.
                      </div>
                      <div className="text-[10px] sm:text-xs text-gray-600">
                        {item.category_name}
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <div className="text-xs font-semibold text-blue-700 sm:text-sm">
                        Qty: {item.stock || 0}
                      </div>
                      {(item.stock || 0) < 10 && (
                        <span className="text-[10px] bg-red-500 text-white px-1.5 py-0.5 rounded-full mt-0.5">
                          Low Stock
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col items-center">
                    <h3
                      className="text-xs font-bold text-center text-gray-800 dark:text-white sm:text-sm line-clamp-2 hover:line-clamp-none"
                      title={`${item.product_name}${item.batch_number ? ` (${item.batch_number})` : ''}`}
                    >
                      {item.product_name}{item.batch_number && ` (${item.batch_number})`}
                    </h3>
                    <div className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-200 mt-0.5">
                      {item.barcode && `Barcode: ${item.barcode}`}
                      {item.expiry_date && (
                        <div>Exp: {item.expiry_date.split("T")[0]}</div>
                      )}
                      {item.batch_number && (
                        <div>Batch: {item.batch_number}</div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showBillModal && (
        <TouchPOSBillModel
          initialProducts={products}
          initialBillDiscount={parseFloat(billDiscount || 0)}
          initialTax={parseFloat(tax || 0)}
          initialShipping={parseFloat(shipping || 0)}
          initialTotals={totals}
          initialCustomerInfo={{ ...customerInfo, bill_number: billNumber }}
          onClose={closeBillModal}
        />
      )}

      {showCalculatorModal && (
        <CalculatorModal
          isOpen={showCalculatorModal}
          onClose={() => setShowCalculatorModal(false)}
        />
      )}
      {showNotification && (
        <Notification
          message={`Delete item "${products[pendingDeleteIndex]?.product_name ?? "this item"}"?`}
          onClose={cancelDelete}
        >
          <button
            onClick={confirmDelete}
            className="p-2 text-sm text-white bg-red-600 rounded-lg sm:p-3 sm:text-lg"
          >
            Yes
          </button>
          <button
            onClick={cancelDelete}
            className="p-2 text-sm text-white bg-gray-400 rounded-lg sm:p-3 sm:text-lg"
          >
            No
          </button>
        </Notification>
      )}
      {showRegisterModal && (
        <RegisterModal
          isOpen={showRegisterModal}
          onClose={() => {
            handleRegisterModalClose();
          }}
          onConfirm={(amount) => {
            handleRegisterConfirm(amount);
            handleRegisterModalClose();
          }}
          cashOnHand={registerStatus.cashOnHand}
          user={user}
          mode={isClosingRegister ? "close" : registerStatus.isOpen ? "updateCash" : "open"}
          closingDetails={calculateClosingDetails()}
        />
      )}

      {showHeldSalesList && (
        <HeldSalesList
          heldSales={heldSales}
          loading={loadingHeldSales}
          onRecall={recallHeldSale}
          onDelete={deleteHeldSale}
          onClose={closeHeldSalesList}
        />
      )}

      {lowStockWarning && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-xl dark:bg-slate-800">
            <h3 className="mb-4 text-lg font-bold dark:text-white">
              Low Stock Warning
            </h3>
            <p className="mb-4 dark:text-white">
              Only {lowStockWarning.remainingStock} units of{" "}
              {lowStockWarning.productName} remaining! Do you want to proceed
              anyway?
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => {
                  // Find the product again in case it changed
                  const item = items.find(
                    (i) => i.id === lowStockWarning.productId
                  );
                  if (item) {
                    const availableStock = parseFloat(item.closingStock || 0);
                    const qtyToAdd = 1;
                    const existingProductIndex = products.findIndex(
                      (p) => p.id === item.id
                    );
                    const newTotalQty =
                      existingProductIndex >= 0
                        ? products[existingProductIndex].qty + qtyToAdd
                        : qtyToAdd;

                    const salesPrice = parseFloat(item.sales_price || 0);
                    const mrp = parseFloat(item.mrp || 0);
                    const discountPerUnit = Math.max(0, mrp - salesPrice);
                    const { schemeName } = applyDiscountScheme(
                      item,
                      saleType,
                      activeSchemes
                    );
                    const productWithQty = { ...item, qty: newTotalQty };
                    const specialDiscount =
                      existingProductIndex >= 0
                        ? calculateSpecialDiscount(
                            productWithQty,
                            saleType,
                            new Date().toISOString().split("T")[0]
                          )
                        : calculateSpecialDiscount(
                            { ...item, qty: qtyToAdd },
                            saleType,
                            new Date().toISOString().split("T")[0]
                          );

                    let updatedProducts = [...products];

                    if (existingProductIndex >= 0) {
                      updatedProducts[existingProductIndex] = {
                        ...updatedProducts[existingProductIndex],
                        qty: newTotalQty,
                        price: salesPrice,
                        discount: discountPerUnit,
                        schemeName: schemeName,
                        specialDiscount: specialDiscount,
                        total: salesPrice * newTotalQty - specialDiscount,
                      };
                    } else {
                      const newProduct = {
                        ...item,
                        qty: qtyToAdd,
                        price: salesPrice,
                        discount: discountPerUnit,
                        schemeName: schemeName,
                        specialDiscount: specialDiscount,
                        total: salesPrice * qtyToAdd - specialDiscount,
                        serialNumber: products.length + 1,
                      };
                      updatedProducts = [...products, newProduct];
                    }

                    setProducts(updatedProducts);

                    // Update local stock immediately for better UX
                    const updateLocalStock = (productId, variantId, qtyUsed) => {
                      setItems(prevItems => {
                        return prevItems.map(item => {
                          if (item.product_id === productId || item.id === productId) {
                            // For variant products, update the specific variant stock
                            if (variantId && item.variants && item.variants.length > 0) {
                              const updatedVariants = item.variants.map(variant => {
                                if (variant.product_variant_id === variantId) {
                                  return {
                                    ...variant,
                                    opening_stock_quantity: Math.max(0, (parseFloat(variant.opening_stock_quantity || 0) - qtyUsed))
                                  };
                                }
                                return variant;
                              });
                              return { ...item, variants: updatedVariants };
                            } else {
                              // For non-variant products, update the main stock
                              return {
                                ...item,
                                opening_stock_quantity: Math.max(0, (parseFloat(item.opening_stock_quantity || 0) - qtyUsed)),
                                stock: Math.max(0, (parseFloat(item.stock || 0) - qtyUsed))
                              };
                            }
                          }
                          return item;
                        });
                      });

                      // Also update search results
                      setSearchResults(prevResults => {
                        return prevResults.map(item => {
                          if (item.product_id === productId || item.id === productId) {
                            // For variant products, update the specific variant stock
                            if (variantId && item.variants && item.variants.length > 0) {
                              const updatedVariants = item.variants.map(variant => {
                                if (variant.product_variant_id === variantId) {
                                  return {
                                    ...variant,
                                    opening_stock_quantity: Math.max(0, (parseFloat(variant.opening_stock_quantity || 0) - qtyUsed))
                                  };
                                }
                                return variant;
                              });
                              return { ...item, variants: updatedVariants };
                            } else {
                              // For non-variant products, update the main stock
                              return {
                                ...item,
                                opening_stock_quantity: Math.max(0, (parseFloat(item.opening_stock_quantity || 0) - qtyUsed)),
                                stock: Math.max(0, (parseFloat(item.stock || 0) - qtyUsed))
                              };
                            }
                          }
                          return item;
                        });
                      });
                    };

                    // Update local stock immediately
                    updateLocalStock(item.product_id || item.id, item.variant_id, qtyToAdd);

                    // Refresh the specific product from server to get accurate data (in background)
                    refreshSpecificProduct(item.product_id || item.id, item.variant_id);

                    setSearchQuery("");
                  }
                  setLowStockWarning(null);
                }}
                className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
              >
                Proceed
              </button>
              <button
                onClick={() => setLowStockWarning(null)}
                className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400 dark:bg-slate-700 dark:text-white"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quantity Edit Popup */}
      {showQuantityPopup && selectedProductForQty && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-md mx-4 shadow-xl">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Edit Quantity
            </h3>

            {/* Product Info - Two Column Layout */}
            <div className="mb-6 grid grid-cols-2 gap-4">
              {/* Left Side */}
              <div className="space-y-4">
                {/* Item Name */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-200 mb-1">
                    Item Name:
                  </label>
                  <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm font-medium text-gray-900 dark:text-white">
                    {selectedProductForQty.product_name}
                  </div>
                </div>

                {/* Quantity Input */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-200 mb-1">
                    Item Qty:
                  </label>
                  <input
                    type="number"
                    value={tempQuantity}
                    onChange={(e) => setTempQuantity(e.target.value)}
                    placeholder="Enter quantity"
                    className="w-full p-3 text-lg text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    autoFocus
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleQuantityUpdate();
                      } else if (e.key === "Escape") {
                        handleQuantityCancel();
                      }
                    }}
                  />
                </div>
              </div>

              {/* Right Side */}
              <div className="space-y-4">
                {/* Minimum Price */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-200 mb-1">
                    Minimum Price:
                  </label>
                  <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm font-medium text-gray-900 dark:text-white">
                    LKR{" "}
                    {formatNumberWithCommas(
                      (selectedProductForQty.minimum_price || 0).toFixed(2)
                    )}
                  </div>
                </div>

                {/* Sales Price (Not Changeable) */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-200 mb-1">
                    Price (Sales Price):
                  </label>
                  <div className="p-3 bg-gray-100 dark:bg-gray-600 rounded-lg text-lg font-medium text-gray-900 dark:text-white text-center border-2 border-gray-300 dark:border-gray-500">
                    LKR{" "}
                    {formatNumberWithCommas(
                      (
                        selectedProductForQty.sales_price ||
                        selectedProductForQty.price ||
                        0
                      ).toFixed(2)
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Buttons */}
            <div className="flex gap-3">
              <button
                onClick={handleQuantityCancel}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg transition-colors"
              >
                Close
              </button>
              <button
                onClick={handleQuantityUpdate}
                className="flex-1 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                disabled={!tempQuantity || parseFloat(tempQuantity) < 0}
              >
                Update
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Add Item Modal */}
      {showAddItemForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-lg p-6 bg-white rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold text-gray-900">Add New Item</h2>
            <ItemForm
              onSubmit={() => {
                setShowAddItemForm(false);
                // Optionally, you can refresh items here if needed
              }}
              onClose={() => setShowAddItemForm(false)}
              initialData={null}
              isBatchOnly={false}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TOUCHPOSFORM;
