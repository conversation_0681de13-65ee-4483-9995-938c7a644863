import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import { motion, AnimatePresence } from "framer-motion";
import { FaSave, FaTimes } from "react-icons/fa";
import PrintVoucher from "./PrintVoucher";
import PaymentVoucherTransaction from "./PaymentVoucherTransaction";
import { useAuth } from "../../context/NewAuthContext";

const PaymentVoucher = () => {
  const { user: authUser } = useAuth ? useAuth() : { user: null };
  
  // Helper function to get authentication headers
  const getAuthHeaders = () => {
    let token = authUser?.token;
    if (!token) {
      let user = null;
      if (localStorage.getItem("user")) {
        user = JSON.parse(localStorage.getItem("user"));
      } else if (sessionStorage.getItem("user")) {
        user = JSON.parse(sessionStorage.getItem("user"));
      }
      token = user?.token;
    }
    return token ? { Authorization: `Bearer ${token}` } : {};
  };

  const [form, setForm] = useState({
    voucherNo: "",
    date: new Date().toISOString().slice(0, 10),
    personType: "Supplier",
    supplierId: "",
    ledgerId: "",
    accountType: "",
    totalAmount: "",
    paymentMethod: "",
    chequeNo: "",
    bankName: "",
    bank: "",
    issueDate: "",
    discount: "",
    remainingBalance: "",
    note: "",
  });
  const [suppliers, setSuppliers] = useState([]);
  const [ledgers, setLedgers] = useState([]);
  const [bankAccounts, setBankAccounts] = useState([]);
  const [outstandingTransactions, setOutstandingTransactions] = useState([]);
  const [selectedTransactions, setSelectedTransactions] = useState([]);
  const [totalPendingAmount, setTotalPendingAmount] = useState(0);
  const [supplierOpeningBalance, setSupplierOpeningBalance] = useState(0);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState(null);
  const [showPrintDialog, setShowPrintDialog] = useState(false);
  const [showPrintPreview, setShowPrintPreview] = useState(false);
  const [showTransactionsModal, setShowTransactionsModal] = useState(false);

  // Refs for form fields
  const dateRef = useRef(null);
  const supplierRef = useRef(null);
  const totalAmountRef = useRef(null);
  const paymentMethodRef = useRef(null);
  const chequeNoRef = useRef(null);
  const bankNameRef = useRef(null);
  const bankRef = useRef(null);
  const issueDateRef = useRef(null);
  const discountRef = useRef(null);
  const noteRef = useRef(null);
  const transactionsRef = useRef(null);

  useEffect(() => {
    fetchSuppliers();
    fetchLedgers();
    fetchBankAccounts();
    fetchNextVoucherNumber();
  }, []);

  const fetchNextVoucherNumber = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/voucher/next-payment-number", { headers: getAuthHeaders() });
      if (response.data.success) {
        setForm((prev) => ({ ...prev, voucherNo: response.data.voucher_number }));
      }
    } catch (err) {
      console.error("Error fetching voucher number:", err);
      const timestamp = Date.now();
      setForm((prev) => ({ ...prev, voucherNo: `PAY-${timestamp}` }));
    }
  };

  const handleKeyDown = (e, nextRef) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (nextRef && nextRef.current) {
        nextRef.current.focus();
      }
    }
  };

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      const response = await axios.get("http://127.0.0.1:8000/api/suppliers", { headers: getAuthHeaders() });
      setSuppliers(Array.isArray(response.data) ? response.data : response.data.data || []);
    } catch (err) {
      setErrors((prev) => ({ ...prev, general: "Failed to fetch suppliers." }));
      console.error("Error fetching suppliers:", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchLedgers = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/staff-ledger/vouchers", { headers: getAuthHeaders() });
      setLedgers(Array.isArray(response.data) ? response.data : response.data.data || []);
    } catch (err) {
      setErrors((prev) => ({ ...prev, general: "Failed to fetch ledgers." }));
      console.error("Error fetching ledgers:", err);
    }
  };

  const fetchBankAccounts = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/staff-ledger/bank-accounts", { headers: getAuthHeaders() });
      setBankAccounts(Array.isArray(response.data) ? response.data : response.data.data || []);
    } catch (err) {
      setErrors((prev) => ({ ...prev, general: "Failed to fetch bank accounts." }));
      console.error("Error fetching bank accounts:", err);
    }
  };

  const fetchOutstandingTransactions = async (supplierId) => {
    try {
      setLoading(true);
      const response = await axios.get("http://127.0.0.1:8000/api/payable", {
        params: { supplier_id: supplierId },
        headers: getAuthHeaders(),
      });
      const data = response.data;
      const transactions = Array.isArray(data.transactions)
        ? data.transactions.filter(
            (transaction) =>
              transaction.supplier_id === parseInt(supplierId) &&
              transaction.status !== "Paid" &&
              transaction.final_outstanding_amount > 0
          )
        : [];
      setOutstandingTransactions(transactions);
      setTotalPendingAmount(data.total_pending_amount || 0);
      setSupplierOpeningBalance(data.supplier_opening_balance || 0);
    } catch (err) {
      setErrors((prev) => ({ ...prev, general: "Failed to fetch outstanding transactions." }));
      console.error("Error fetching transactions:", err);
    } finally {
      setLoading(false);
    }
  };

  const autoSelectTransactions = (totalAmount) => {
    if (!outstandingTransactions.length) return;
    
    // Sort transactions by priority: opening_balance first, then by date (oldest first)
    const sortedTransactions = [...outstandingTransactions].sort((a, b) => {
      if (a.type === "opening_balance") return -1;
      if (b.type === "opening_balance") return 1;
      if (a.type !== b.type) return a.type === "purchase" ? -1 : 1;
      return new Date(a.date) - new Date(b.date);
    });

    let remainingAmount = totalAmount;
    const newSelectedTransactions = [];

    for (const transaction of sortedTransactions) {
      if (remainingAmount <= 0) break;
      
      const pendingAmount = transaction.final_outstanding_amount;
      const paymentAmount = Math.min(pendingAmount, remainingAmount);
      
      if (paymentAmount > 0) {
        const updatedTransaction = {
          ...transaction,
          paymentAmount: paymentAmount,
          newStatus: paymentAmount >= pendingAmount ? "Paid" : "Partial",
        };
        newSelectedTransactions.push(updatedTransaction);
        remainingAmount -= paymentAmount;
      }
    }

    setSelectedTransactions(newSelectedTransactions);
    updateRemainingBalance(newSelectedTransactions);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setErrors((prev) => ({ ...prev, [name]: null, general: null }));

    setForm((prev) => {
      const updatedForm = { ...prev, [name]: value };

      if (name === "personType") {
        updatedForm.supplierId = "";
        updatedForm.ledgerId = "";
        updatedForm.accountType = "";
        updatedForm.totalAmount = "";
        updatedForm.paymentMethod = "";
        updatedForm.chequeNo = "";
        updatedForm.bankName = "";
        updatedForm.bank = "";
        updatedForm.issueDate = "";
        updatedForm.discount = "";
        updatedForm.remainingBalance = "";
        updatedForm.note = "";
        setSelectedTransactions([]);
        setOutstandingTransactions([]);
        setTotalPendingAmount(0);
        setSupplierOpeningBalance(0);
        return updatedForm;
      }

      if (name === "ledgerId" && value) {
        const selectedLedger = ledgers.find((l) => l.id === parseInt(value));
        if (selectedLedger) {
          updatedForm.accountType = selectedLedger.account_group;
        }
        return updatedForm;
      }

      if (name === "totalAmount") {
        const totalAmount = parseFloat(value) || 0;
        if (prev.personType === "Supplier" && totalAmount > totalPendingAmount) {
          setErrors((prev) => ({
            ...prev,
            totalAmount: `Total amount cannot exceed ${formatCurrency(totalPendingAmount)}`,
          }));
          return prev;
        }
        if (prev.personType !== "Ledger") {
          updatedForm.remainingBalance = (totalAmount + parseFloat(updatedForm.discount || 0)).toFixed(2);
        }
      } else if (name === "discount") {
        const discount = parseFloat(value) || 0;
        const totalAmount = parseFloat(prev.totalAmount) || 0;
        if (prev.personType !== "Ledger") {
          updatedForm.remainingBalance = (totalAmount + discount).toFixed(2);
        }
      } else if (name === "paymentMethod") {
        if (value !== "Cheque") {
          updatedForm.chequeNo = "";
          updatedForm.bankName = "";
          updatedForm.issueDate = "";
        }
        if (!["Card", "Cheque", "Online Payment"].includes(value)) {
          updatedForm.bank = "";
        }
      }

      return updatedForm;
    });

    if (name === "supplierId" && value) {
      fetchOutstandingTransactions(value);
      setSelectedTransactions([]);
      setForm((prev) => ({
        ...prev,
        totalAmount: "",
        paymentMethod: "",
        chequeNo: "",
        bankName: "",
        bank: "",
        issueDate: "",
        discount: "",
        remainingBalance: "",
        note: "",
      }));
    }
  };

  const handleTransactionSelect = (e) => {
    const transactionId = e.target.value;
    if (!transactionId) return;

    let transaction;
    if (transactionId.startsWith("opening_balance_")) {
      transaction = outstandingTransactions.find((trans) => trans.id === transactionId);
    } else {
      transaction = outstandingTransactions.find((trans) => trans.id === parseInt(transactionId));
    }

    if (transaction && !selectedTransactions.some((trans) => trans.id === transaction.id)) {
      const remainingBalance = parseFloat(form.remainingBalance) || 0;
      
      // Don't allow selection if remaining balance is zero
      if (remainingBalance <= 0) {
        setErrors((prev) => ({ ...prev, transactions: "No remaining balance to allocate." }));
        return;
      }
      
      const pendingAmount = transaction.final_outstanding_amount;
      const paymentAmount = Math.min(pendingAmount, remainingBalance);
      const updatedTransaction = {
        ...transaction,
        paymentAmount: paymentAmount,
        newStatus: paymentAmount >= pendingAmount ? "Paid" : "Partial",
      };

      const newSelectedTransactions = [...selectedTransactions, updatedTransaction];
      setSelectedTransactions(newSelectedTransactions);
      updateRemainingBalance(newSelectedTransactions);
      setErrors((prev) => ({ ...prev, transactions: null }));
    }

    e.target.value = "";
  };

  const handlePaymentAmountChange = (transactionId, value) => {
    const paymentAmount = parseFloat(value) || 0;
    const transaction = selectedTransactions.find((trans) => trans.id === transactionId);
    if (!transaction) return;

    const pendingAmount = transaction.final_outstanding_amount;
    if (paymentAmount > pendingAmount) {
      setErrors((prev) => ({
        ...prev,
        general: `Payment amount for transaction #${transaction.reference_no || "Opening Balance"} cannot exceed ${formatCurrency(
          pendingAmount
        )}`,
      }));
      return;
    }

    const updatedTransactions = selectedTransactions.map((trans) =>
      trans.id === transactionId
        ? {
            ...trans,
            paymentAmount: paymentAmount,
            newStatus: paymentAmount >= trans.final_outstanding_amount ? "Paid" : "Partial",
          }
        : trans
    );

    setSelectedTransactions(updatedTransactions);
    updateRemainingBalance(updatedTransactions);
  };

  const updateRemainingBalance = (transactions) => {
    const totalPaid = transactions.reduce((sum, transaction) => sum + transaction.paymentAmount, 0);
    const totalAmount = parseFloat(form.totalAmount) || 0;
    const discount = parseFloat(form.discount) || 0;
    const remainingBalance = totalAmount + discount - totalPaid;
    setForm((prev) => ({
      ...prev,
      remainingBalance: remainingBalance >= 0 ? remainingBalance.toFixed(2) : prev.remainingBalance,
    }));
  };

  const removeTransaction = (transactionId) => {
    const newSelectedTransactions = selectedTransactions.filter((trans) => trans.id !== transactionId);
    setSelectedTransactions(newSelectedTransactions);
    updateRemainingBalance(newSelectedTransactions);
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "LKR",
    }).format(value);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const validateForm = () => {
    const newErrors = {};

    if (form.personType === "Supplier") {
      if (!form.supplierId) {
        newErrors.supplierId = "Supplier is required.";
      }
      if (selectedTransactions.length === 0) {
        newErrors.transactions = "At least one transaction must be selected.";
      }
      if (parseFloat(form.remainingBalance) !== 0) {
        newErrors.general = "Remaining balance must be zero to submit.";
      }
    } else if (form.personType === "Ledger") {
      if (!form.ledgerId) {
        newErrors.ledgerId = "Ledger is required.";
      }
    }

    if (!form.totalAmount || isNaN(form.totalAmount) || parseFloat(form.totalAmount) <= 0) {
      newErrors.totalAmount = "Valid total amount is required.";
    }
    if (!form.paymentMethod) {
      newErrors.paymentMethod = "Payment method is required.";
    }
    if (form.paymentMethod === "Cheque") {
      if (!form.chequeNo) {
        newErrors.chequeNo = "Cheque number is required.";
      }
      if (!form.bankName) {
        newErrors.bankName = "Bank name is required.";
      }
      if (!form.issueDate) {
        newErrors.issueDate = "Issue date is required.";
      }
    }
    if (["Card", "Cheque", "Online Payment"].includes(form.paymentMethod)) {
      if (!form.bank) {
        newErrors.bank = "Bank account is required.";
      }
    }

    return newErrors;
  };

  const handleSubmit = async () => {
    const validationErrors = validateForm();
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      return;
    }

    const authHeaders = getAuthHeaders();
    if (!authHeaders.Authorization) {
      setErrors((prev) => ({ ...prev, general: "Authentication required. Please log in again." }));
      return;
    }

    try {
      setLoading(true);

      if (form.personType === "Ledger") {
        const ledgerPayload = {
          staff_id: parseInt(form.ledgerId),
          amount: parseFloat(form.totalAmount),
          discount: parseFloat(form.discount) || 0,
          payment_date: form.date,
          payment_method: form.paymentMethod,
          cheque_no: form.paymentMethod === "Cheque" ? form.chequeNo : null,
          bank_name: form.paymentMethod === "Cheque" ? form.bankName : null,
          bank: ["Card", "Cheque", "Online Payment"].includes(form.paymentMethod) ? form.bank : null,
          issue_date: form.paymentMethod === "Cheque" ? form.issueDate : null,
          note: form.note || null,
        };

        await axios.post("http://127.0.0.1:8000/api/staff-ledger/payment-voucher", ledgerPayload, { headers: authHeaders });
      } else {
        await Promise.all(
          selectedTransactions.map((transaction) =>
            axios.put(`http://127.0.0.1:8000/api/payable/${transaction.id}`, {
              paid_amount: transaction.paymentAmount,
              payment_date: form.date,
              payment_method: form.paymentMethod,
              cheque_no: form.paymentMethod === "Cheque" ? form.chequeNo : null,
              bank_name: form.paymentMethod === "Cheque" ? form.bankName : null,
              bank: ["Card", "Cheque", "Online Payment"].includes(form.paymentMethod) ? form.bank : null,
              issue_date: form.paymentMethod === "Cheque" ? form.issueDate : null,
              status: transaction.newStatus,
              final_outstanding_amount: transaction.final_outstanding_amount - transaction.paymentAmount - (parseFloat(form.discount) || 0),
              note: form.note || null,
              reference_no: transaction.reference_no,
              transaction_type: transaction.type,
              opening_balance: transaction.type === "opening_balance" ? transaction.paymentAmount : 0,
              supplier_id: form.supplierId,
              discount: parseFloat(form.discount) || 0,
            }, { headers: authHeaders })
          )
        );
      }

      setSuccessMessage("Payment voucher processed successfully!");
      setShowPrintDialog(true);
    } catch (err) {
      console.error("Error processing payment voucher:", err);
      if (err.response && err.response.data && err.response.data.errors) {
        setErrors(err.response.data.errors);
      } else {
        setErrors((prev) => ({ ...prev, general: "Failed to process payment voucher." }));
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    setShowPrintDialog(false);
    setShowPrintPreview(true);
  };

  const handleSkipPrint = () => {
    setShowPrintDialog(false);
    resetForm();
  };

  const resetForm = () => {
    setForm({
      voucherNo: "",
      date: new Date().toISOString().slice(0, 10),
      personType: "Supplier",
      supplierId: "",
      ledgerId: "",
      accountType: "",
      totalAmount: "",
      paymentMethod: "",
      chequeNo: "",
      bankName: "",
      bank: "",
      issueDate: "",
      discount: "",
      remainingBalance: "",
      note: "",
    });
    setSelectedTransactions([]);
    setOutstandingTransactions([]);
    setTotalPendingAmount(0);
    setSupplierOpeningBalance(0);
    setErrors({});
    setSuccessMessage(null);
    setShowPrintPreview(false);
    setShowPrintDialog(false);
    fetchNextVoucherNumber();
  };

  return (
    <div className="min-h-screen p-6 text-gray-900 bg-gray-50 dark:bg-gray-900 dark:text-white">
      <motion.h2
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-6 text-2xl font-semibold"
      >
        Payment Voucher
      </motion.h2>

      <AnimatePresence>
        {errors.general && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="p-4 mb-4 text-red-700 bg-red-100 rounded-lg dark:bg-red-900 dark:text-red-100"
          >
            {errors.general}
          </motion.div>
        )}
        {successMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="p-4 mb-4 text-green-700 bg-green-100 rounded-lg dark:bg-green-900 dark:text-green-100"
          >
            {successMessage}
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showPrintDialog && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              className="p-6 bg-white rounded-lg shadow-lg dark:bg-gray-900"
            >
              <h3 className="mb-4 text-lg font-semibold">Print Voucher?</h3>
              <p className="mb-6">Would you like to print the voucher details?</p>
              <div className="flex justify-end space-x-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSkipPrint}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg dark:bg-gray-600 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-500"
                >
                  No, Skip Print
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handlePrint}
                  className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                >
                  Yes, Print
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showPrintPreview && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              className="w-full max-w-2xl p-6 bg-white rounded-lg shadow-lg dark:bg-gray-800"
            >
              <PrintVoucher
                form={form}
                selectedItems={selectedTransactions}
                parties={suppliers}
                partyIdField="supplierId"
                partyNameField="supplier_name"
                voucherType="Payment"
                formatCurrency={formatCurrency}
                formatDate={formatDate}
                onClose={() => {
                  setShowPrintPreview(false);
                  resetForm();
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Show All Transactions Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setShowTransactionsModal(true)}
        className="flex items-center px-4 py-2 mb-6 space-x-2 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <span>Show All Transactions</span>
      </motion.button>

      {/* Modal for Transactions */}
        {showTransactionsModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <motion.div 
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="w-full max-w-6xl p-6 mx-4 bg-white rounded-lg shadow-xl dark:bg-gray-800 max-h-[90vh] overflow-auto"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">Payment Voucher Transactions</h3>
                <button 
                  onClick={() => setShowTransactionsModal(false)}
                  className="p-2 text-gray-500 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <FaTimes className="w-5 h-5" />
                </button>
              </div>
              <PaymentVoucherTransaction />
            </motion.div>
          </div>
        )}

      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="p-6 mb-6 bg-white rounded-lg shadow-md dark:bg-gray-800"
      >
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <label htmlFor="voucherNo" className="block mb-1 text-sm font-medium">
              Voucher No
            </label>
            <input
              id="voucherNo"
              name="voucherNo"
              value={form.voucherNo}
              onChange={handleChange}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              readOnly
            />
          </div>
          <div>
            <label htmlFor="date" className="block mb-1 text-sm font-medium">
              Date
            </label>
            <input
              id="date"
              name="date"
              type="date"
              value={form.date}
              onChange={handleChange}
              ref={dateRef}
              onKeyDown={(e) => handleKeyDown(e, supplierRef)}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="personType" className="block mb-1 text-sm font-medium">
              Person Type
            </label>
            <select
              id="personType"
              name="personType"
              value={form.personType}
              onChange={handleChange}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="Supplier">Supplier</option>
              <option value="Ledger">Ledger</option>
            </select>
          </div>

          {form.personType === "Supplier" && (
            <div>
              <label htmlFor="supplierId" className="block mb-1 text-sm font-medium">
                Supplier
              </label>
              <select
                id="supplierId"
                name="supplierId"
                value={form.supplierId}
                onChange={handleChange}
                ref={supplierRef}
                onKeyDown={(e) => handleKeyDown(e, totalAmountRef)}
                className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Supplier</option>
                {suppliers.map((supplier) => (
                  <option key={supplier.id} value={supplier.id}>
                    {supplier.supplier_name}
                  </option>
                ))}
              </select>
              {errors.supplierId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.supplierId}
                </p>
              )}
              {form.supplierId && (
                <div className="mt-1 text-sm text-green-600 dark:text-green-400">
                  <p>Opening Balance: {formatCurrency(supplierOpeningBalance)}</p>
                  <p>
                    Remaining Opening Balance:{" "}
                    {formatCurrency(
                      outstandingTransactions.find((t) => t.type === "opening_balance")?.final_outstanding_amount || 0
                    )}
                  </p>
                  <p>Total Pending: {formatCurrency(totalPendingAmount)}</p>
                </div>
              )}
            </div>
          )}

          {form.personType === "Ledger" && (
            <>
              <div>
                <label htmlFor="ledgerId" className="block mb-1 text-sm font-medium">
                  Ledger
                </label>
                <select
                  id="ledgerId"
                  name="ledgerId"
                  value={form.ledgerId}
                  onChange={handleChange}
                  className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Ledger</option>
                  {ledgers.map((ledger) => (
                    <option key={ledger.id} value={ledger.id}>
                      {ledger.name} ({ledger.staff_id})
                    </option>
                  ))}
                </select>
                {errors.ledgerId && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.ledgerId}
                  </p>
                )}
              </div>

              {form.accountType && (
                <div>
                  <label htmlFor="accountType" className="block mb-1 text-sm font-medium">
                    Account Type
                  </label>
                  <input
                    id="accountType"
                    name="accountType"
                    type="text"
                    value={form.accountType}
                    readOnly
                    className="w-full p-2 text-gray-700 bg-gray-200 border border-gray-300 rounded-lg dark:bg-gray-600 dark:text-white dark:border-gray-600"
                  />
                </div>
              )}
            </>
          )}
          <div>
            <label htmlFor="totalAmount" className="block mb-1 text-sm font-medium">
              Total Amount
            </label>
            <input
              id="totalAmount"
              name="totalAmount"
              type="number"
              value={form.totalAmount}
              onChange={handleChange}
              placeholder="Enter total amount"
              ref={totalAmountRef}
              onKeyDown={(e) => handleKeyDown(e, paymentMethodRef)}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={form.personType === "Supplier" ? !form.supplierId : !form.ledgerId}
            />
            {errors.totalAmount && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.totalAmount}
              </p>
            )}
          </div>
          <div>
            <label htmlFor="paymentMethod" className="block mb-1 text-sm font-medium">
              Payment Method
            </label>
            <select
              id="paymentMethod"
              name="paymentMethod"
              value={form.paymentMethod}
              onChange={handleChange}
              ref={paymentMethodRef}
              onKeyDown={(e) => handleKeyDown(e, form.paymentMethod === "Cheque" ? chequeNoRef : discountRef)}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!form.totalAmount}
            >
              <option value="">Select Payment Method</option>
              <option value="Cash">Cash</option>
              <option value="Card">Card</option>
              <option value="Cheque">Cheque</option>
              <option value="Online Payment">Online Payment</option>
            </select>
            {errors.paymentMethod && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.paymentMethod}
              </p>
            )}
          </div>
          {form.paymentMethod === "Cheque" && (
            <>
              <div>
                <label htmlFor="chequeNo" className="block mb-1 text-sm font-medium">
                  Cheque No
                </label>
                <input
                  id="chequeNo"
                  name="chequeNo"
                  value={form.chequeNo}
                  onChange={handleChange}
                  placeholder="Enter cheque number"
                  ref={chequeNoRef}
                  onKeyDown={(e) => handleKeyDown(e, bankNameRef)}
                  className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.chequeNo && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.chequeNo}
                  </p>
                )}
              </div>
              <div>
                <label htmlFor="bankName" className="block mb-1 text-sm font-medium">
                  Bank Name
                </label>
                <input
                  id="bankName"
                  name="bankName"
                  value={form.bankName}
                  onChange={handleChange}
                  placeholder="Enter bank name"
                  ref={bankNameRef}
                  onKeyDown={(e) => handleKeyDown(e, issueDateRef)}
                  className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.bankName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.bankName}
                  </p>
                )}
              </div>
              <div>
                <label htmlFor="issueDate" className="block mb-1 text-sm font-medium">
                  Issue Date
                </label>
                <input
                  id="issueDate"
                  name="issueDate"
                  type="date"
                  value={form.issueDate}
                  onChange={handleChange}
                  ref={issueDateRef}
                  onKeyDown={(e) => handleKeyDown(e, discountRef)}
                  className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.issueDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.issueDate}
                  </p>
                )}
              </div>
            </>
          )}
          {(form.paymentMethod === "Card" || form.paymentMethod === "Cheque" || form.paymentMethod === "Online Payment") && (
            <div>
              <label htmlFor="bank" className="block mb-1 text-sm font-medium">
                Bank Account
              </label>
              <select
                id="bank"
                name="bank"
                value={form.bank}
                onChange={handleChange}
                ref={bankRef}
                onKeyDown={(e) => handleKeyDown(e, discountRef)}
                className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Bank Account</option>
                {bankAccounts.map((account) => (
                  <option key={`${account.type}-${account.id}`} value={`${account.type}-${account.id}`}>
                    {account.name} ({account.account_group})
                  </option>
                ))}
              </select>
              {errors.bank && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.bank}
                </p>
              )}
            </div>
          )}
          <div>
            <label htmlFor="discount" className="block mb-1 text-sm font-medium">
              Discount
            </label>
            <input
              id="discount"
              name="discount"
              type="number"
              value={form.discount}
              onChange={handleChange}
              placeholder="Enter discount"
              ref={discountRef}
              onKeyDown={(e) => handleKeyDown(e, noteRef)}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!form.totalAmount}
            />
          </div>
          {form.personType !== "Ledger" && (
            <div>
              <label htmlFor="remainingBalance" className="block mb-1 text-sm font-medium">
                Remaining Balance
              </label>
              <input
                id="remainingBalance"
                name="remainingBalance"
                type="number"
                value={form.remainingBalance}
                readOnly
                className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600"
              />
            </div>
          )}
          <div className="md:col-span-2">
            <label htmlFor="note" className="block mb-1 text-sm font-medium">
              Note (Optional)
            </label>
            <textarea
              id="note"
              name="note"
              value={form.note}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              ref={noteRef}
              onKeyDown={(e) => handleKeyDown(e, transactionsRef)}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="4"
            />
          </div>
          {form.personType === "Supplier" && (
            <div className="md:col-span-2">
              <label htmlFor="transactions" className="block mb-1 text-sm font-medium">
                Outstanding Transactions
              </label>
              <select
                id="transactions"
                onChange={handleTransactionSelect}
                ref={transactionsRef}
                className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={!form.totalAmount}
              >
                <option value="">Select Transaction</option>
                {outstandingTransactions
                  .filter((transaction) => {
                    // Don't show already selected transactions
                    if (selectedTransactions.some((trans) => trans.id === transaction.id)) {
                      return false;
                    }
                    
                    // Only show transactions that can be paid with remaining balance
                    const remainingBalance = parseFloat(form.remainingBalance) || 0;
                    if (remainingBalance <= 0) return false;
                    
                    return true;
                  })
                  .map((transaction) => (
                    <option key={transaction.id} value={transaction.id}>
                      {transaction.type === "opening_balance"
                        ? `Opening Balance - ${formatCurrency(transaction.final_outstanding_amount)}`
                        : `#${transaction.reference_no} - ${formatCurrency(transaction.final_outstanding_amount)} (${formatDate(
                            transaction.date
                          )})`}
                    </option>
                  ))}
              </select>
              {errors.transactions && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.transactions}
                </p>
              )}
            </div>
          )}
        </div>

        {form.personType === "Supplier" && selectedTransactions.length > 0 && (
          <div className="mt-6">
            <h3 className="mb-2 text-lg font-medium">Selected Transactions</h3>
            <table className="w-full table-auto">
              <thead className="bg-gray-100 dark:bg-gray-700">
                <tr>
                  <th className="px-4 py-2 text-left">Reference #</th>
                  <th className="px-4 py-2 text-left">Date</th>
                  <th className="px-4 py-2 text-left">Pending Amount</th>
                  <th className="px-4 py-2 text-left">Payment Amount</th>
                  <th className="px-4 py-2 text-left">Status</th>
                  <th className="px-4 py-2 text-left">Action</th>
                </tr>
              </thead>
              <tbody>
                {selectedTransactions.map((transaction) => (
                  <tr key={transaction.id} className="border-t border-gray-200 dark:border-gray-600">
                    <td className="px-4 py-2">{transaction.reference_no || "Opening Balance"}</td>
                    <td className="px-4 py-2">{formatDate(transaction.date)}</td>
                    <td className="px-4 py-2">{formatCurrency(transaction.final_outstanding_amount)}</td>
                    <td className="px-4 py-2">
                      <input
                        type="number"
                        value={transaction.paymentAmount}
                        onChange={(e) => handlePaymentAmountChange(transaction.id, e.target.value)}
                        className="w-full p-1 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        max={transaction.final_outstanding_amount}
                        disabled={loading}
                      />
                    </td>
                    <td className="px-4 py-2">{transaction.newStatus}</td>
                    <td className="px-4 py-2">
                      <button
                        onClick={() => removeTransaction(transaction.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        <div className="flex justify-end mt-6 space-x-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={resetForm}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg dark:bg-gray-600 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-500"
            disabled={loading}
          >
            <FaTimes className="inline mr-2" />
            Reset
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleSubmit}
            className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
            disabled={loading}
          >
            <FaSave className="inline mr-2" />
            {loading ? "Processing..." : "Submit"}
          </motion.button>
        </div>
      </motion.div>

      {loading && (
        <div className="flex items-center justify-center p-8">
          <div className="w-12 h-12 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};

export default PaymentVoucher;