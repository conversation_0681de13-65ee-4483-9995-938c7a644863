import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { FiTrash2, FiEdit2, FiPlus, FiSearch } from "react-icons/fi";
import { useAuth } from "../../context/NewAuthContext";
import RoleModal from "./RoleModal";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

axios.defaults.baseURL = "http://127.0.0.1:8000";

const RoleList = () => {
  const navigate = useNavigate();
  const { user: currentUser, checkPermission } = useAuth();
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(null);
  const [validPermissions, setValidPermissions] = useState([]);

  useEffect(() => {
    fetchRoles();
  }, [currentPage, navigate, checkPermission, searchTerm]);

  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        const token = currentUser?.token;
        const response = await axios.get("/api/permissions", {
          headers: { Authorization: `Bearer ${token}` },
        });
        // console.log("Permissions response:", response.data);
        if (Array.isArray(response.data)) {
          setValidPermissions(response.data.map((p) => p.name));
        }
      } catch (error) {
        console.error("Failed to fetch permissions:", error);
      }
    };
    fetchPermissions();
  }, [currentUser]);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      setError("");
      const token = currentUser?.token;
      const response = await axios.get(
        `/api/roles?page=${currentPage}&search=${searchTerm}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      let rolesData = [];
      let lastPage = 1;

      if (response.data && Array.isArray(response.data)) {
        rolesData = response.data;
        lastPage = 1;
      } else if (response.data && response.data.data) {
        rolesData = response.data.data || [];
        lastPage = response.data.last_page || 1;
      }

      // console.log("Raw roles data:", JSON.stringify(rolesData, null, 2));

      const transformedRoles = rolesData.map((role) => {
        // console.log(`Role ${role.name} fetched permissions:`, JSON.stringify(role.permissions, null, 2));
        let permissionsArray = [];
        let permissionsCount = 0;
        if (Array.isArray(role.permissions)) {
          // Handle both array of paths and array of objects with 'name' property
          permissionsArray = role.permissions
            .map((p) => (typeof p === 'string' ? p : p.name))
            .filter(Boolean);
          permissionsCount = permissionsArray.length;
        }
        return {
          ...role,
          permissionsArray,
          permissionsCount,
          permissionsOriginal: role.permissions,
        };
      });

      // console.log("Transformed roles:", JSON.stringify(transformedRoles, null, 2));
      setRoles(transformedRoles);
      setTotalPages(lastPage);
      setLastFetchTime(new Date().toLocaleTimeString());
    } catch (err) {
      console.error("Fetch roles error:", err.response || err);
      setError(err.response?.data?.message || "Failed to fetch roles");
      toast.error(err.response?.data?.message || "Failed to fetch roles");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRole = async (roleId, roleName) => {
    if (
      roleName.toLowerCase() === "admin" ||
      roleName.toLowerCase() === "superadmin"
    ) {
      toast.error("Admin and Super Admin roles cannot be deleted.");
      return;
    }
    if (!window.confirm("Are you sure you want to delete this role?")) return;

    try {
      const token = currentUser?.token;
      await axios.delete(`/api/roles/${roleId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      toast.success("Role deleted successfully");
      fetchRoles();
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to delete role");
    }
  };

  const handleEditRole = (role) => {
    if (
      role.name.toLowerCase() === "admin" ||
      role.name.toLowerCase() === "superadmin"
    ) {
      toast.info(
        "Admin and Super Admin roles have all permissions and cannot be edited."
      );
      return;
    }
    // Convert permissions to array of paths for RoleModal
    let permissionsArray = [];
    if (Array.isArray(role.permissionsOriginal)) {
      permissionsArray = role.permissionsOriginal
        .map((p) => (typeof p === 'string' ? p : p.name))
        .filter(Boolean);
    }
    // console.log("Permissions array for edit:", permissionsArray);
    setSelectedRole({ ...role, permissions: permissionsArray });
    setIsModalOpen(true);
  };

  const handleAddRole = () => {
    setSelectedRole({ name: "", description: "", permissions: [] });
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedRole(null);
  };

  const handleSubmit = async (roleData) => {
    try {
      const token = currentUser?.token;

      // Use permissions directly as an array of paths from the modal only
      let permissionsArray = [];
      if (Array.isArray(roleData.permissions)) {
        permissionsArray = roleData.permissions.filter(
          (path) => validPermissions.length === 0 || validPermissions.includes(path)
        );
      }
      // console.log("Submitting permissions array:", permissionsArray);

      const payload = {
        name: roleData.name,
        description: roleData.description || "",
        permissions: permissionsArray,
      };

      let updatedRole = null;
      if (roleData.id) {
        const response = await axios.put(`/api/roles/${roleData.id}`, payload, {
          headers: { Authorization: `Bearer ${token}` },
        });
        updatedRole = response.data;
        toast.success("Role updated successfully");
      } else {
        const response = await axios.post("/api/roles", payload, {
          headers: { Authorization: `Bearer ${token}` },
        });
        updatedRole = response.data;
        toast.success("Role created successfully");
      }

      // Refresh permissions
      const permResponse = await axios.get("/api/permissions", {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (Array.isArray(permResponse.data)) {
        setValidPermissions(permResponse.data.map((p) => p.name));
      }

      // Update roles state with the updated role
      setRoles((prevRoles) => {
        // If creating, add; if editing, replace
        if (roleData.id) {
          return prevRoles.map((r) =>
            r.id === updatedRole.id
              ? {
                  ...updatedRole,
                  permissionsArray: Array.isArray(updatedRole.permissions)
                    ? updatedRole.permissions.map((p) => (typeof p === 'string' ? p : p.name)).filter(Boolean)
                    : [],
                  permissionsCount: Array.isArray(updatedRole.permissions)
                    ? updatedRole.permissions.length
                    : 0,
                  permissionsOriginal: updatedRole.permissions,
                }
              : r
          );
        } else {
          return [
            ...prevRoles,
            {
              ...updatedRole,
              permissionsArray: Array.isArray(updatedRole.permissions)
                ? updatedRole.permissions.map((p) => (typeof p === 'string' ? p : p.name)).filter(Boolean)
                : [],
              permissionsCount: Array.isArray(updatedRole.permissions)
                ? updatedRole.permissions.length
                : 0,
              permissionsOriginal: updatedRole.permissions,
            },
          ];
        }
      });

      fetchRoles(); // Optionally, re-fetch from backend for full sync
      handleModalClose();
    } catch (error) {
      console.error("Submit role error:", error.response || error);
      toast.error(error.response?.data?.message || "Failed to submit role");
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-4 md:p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Role Management</h1>
        <button
          onClick={handleAddRole}
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
        >
          <FiPlus className="inline mr-1" /> Add Role
        </button>
      </div>

      <div className="mb-2 text-sm text-gray-600">
        {loading && <span>Loading roles...</span>}
        {!loading && lastFetchTime && (
          <span>Last updated at: {lastFetchTime}</span>
        )}
        {error && <span className="text-red-600">Error: {error}</span>}
      </div>

      <div className="mb-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FiSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search roles..."
            className="w-full pl-10 p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Pages Assigned
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {roles.length === 0 && !loading ? (
              <tr>
                <td colSpan="3" className="px-6 py-4 text-center text-gray-500">
                  No roles found
                </td>
              </tr>
            ) : (
              roles.map((role) => (
                <tr key={role.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {role.name}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700">
                      {role.name.toLowerCase() === "admin" ||
                      role.name.toLowerCase() === "superadmin"
                        ? "All pages"
                        : `${role.permissionsCount || 0} (${role.permissionsArray.join(", ") || "none"})`}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <button
                      onClick={() => handleEditRole(role)}
                      className={`text-blue-600 hover:text-blue-900 ${role.name.toLowerCase() === "admin" || role.name.toLowerCase() === "superadmin" ? "cursor-not-allowed opacity-50" : ""}`}
                      disabled={
                        role.name.toLowerCase() === "admin" ||
                        role.name.toLowerCase() === "superadmin"
                      }
                    >
                      <FiEdit2 className="inline" />
                    </button>
                    <button
                      onClick={() => handleDeleteRole(role.id, role.name)}
                      className={`text-red-600 hover:text-red-900 ${role.name.toLowerCase() === "admin" || role.name.toLowerCase() === "superadmin" ? "cursor-not-allowed opacity-50" : ""}`}
                      disabled={
                        role.name.toLowerCase() === "admin" ||
                        role.name.toLowerCase() === "superadmin"
                      }
                    >
                      <FiTrash2 className="inline" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <RoleModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSubmit={handleSubmit}
        roleData={selectedRole}
      />
    </div>
  );
};

export default RoleList;