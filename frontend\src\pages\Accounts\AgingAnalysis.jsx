import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "../../context/NewAuthContext";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import * as XLSX from "xlsx";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { Book, Clock } from "lucide-react";
import { FiSearch, FiChevronDown, FiChevronUp } from "react-icons/fi";
import { FaFileExcel, FaFilePdf, FaPrint } from "react-icons/fa";

const API_BASE_URL = "http://127.0.0.1:8000/api";

// axios.defaults.timeout = 10000; // Disabled to allow unlimited wait for API responses

const formatCurrency = (amount) => {
  const numericAmount = Number(amount);
  if (isNaN(numericAmount)) {
    return "LKR 0.00";
  }
  return new Intl.NumberFormat("en-LK", {
    style: "currency",
    currency: "LKR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(Math.abs(numericAmount));
};

const calculateDaysOutstanding = (invoiceDate) => {
  try {
    const invoice = new Date(invoiceDate);
    const today = new Date();
    if (isNaN(invoice.getTime())) {
      return "N/A";
    }
    const diffTime = today - invoice;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return diffDays >= 0 ? diffDays : "N/A";
  } catch (error) {
    console.warn("Error calculating days outstanding:", error);
    return "N/A";
  }
};

const computeAgingData = (data) => {
  const customerMap = {};
  data.forEach((row) => {
    const key = `${row.customer_name}-${row.customer_phone || "-"}`;
    if (!customerMap[key]) {
      customerMap[key] = {
        customer_name: row.customer_name,
        customer_phone: row.customer_phone || "-",
        days_0_30: 0,
        days_31_60: 0,
        days_61_90: 0,
        days_above_91: 0,
      };
    }
    const days = row.days_outstanding;
    const balance = parseFloat(row.balance) || 0;
    if (days !== "N/A") {
      if (days <= 30) customerMap[key].days_0_30 += balance;
      else if (days <= 60) customerMap[key].days_31_60 += balance;
      else if (days <= 90) customerMap[key].days_61_90 += balance;
      else customerMap[key].days_above_91 += balance;
    }
  });
  return Object.values(customerMap).sort((a, b) =>
    a.customer_name.localeCompare(b.customer_name)
  );
};

const AgingAnalysisTable = ({ data, reportData, toggleRow, expandedRow }) => {
  const totals = data.reduce(
    (acc, customer) => {
      acc.days_0_30 += customer.days_0_30 || 0;
      acc.days_31_60 += customer.days_31_60 || 0;
      acc.days_61_90 += customer.days_61_90 || 0;
      acc.days_above_91 += customer.days_above_91 || 0;
      acc.subtotal +=
        (customer.days_0_30 || 0) +
        (customer.days_31_60 || 0) +
        (customer.days_61_90 || 0) +
        (customer.days_above_91 || 0);
      return acc;
    },
    {
      days_0_30: 0,
      days_31_60: 0,
      days_61_90: 0,
      days_above_91: 0,
      subtotal: 0,
    }
  );

  const printCustomerSales = (customer, customerSales) => {
    const subtotal =
      (customer.days_0_30 || 0) +
      (customer.days_31_60 || 0) +
      (customer.days_61_90 || 0) +
      (customer.days_above_91 || 0);

    const printWindow = window.open("", "_blank");
    printWindow.document.write(`
      <html>
        <head>
          <title>${customer.customer_name} - Outstanding Details</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 2rem; color: #333; }
            .header { display: flex; justify-content: space-between; margin-bottom: 2rem; }
            .title { font-size: 2.5rem; font-weight: bold; color: #1a365d; }
            .company-name { font-size: 1.5rem; font-weight: bold; color: #2b6cb0; }
            .section-title { font-size: 1.25rem; font-weight: bold; color: #2b6cb0; margin-bottom: 1rem; }
            .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 2rem; }
            th { background-color: #2b6cb0; color: white; padding: 0.75rem; text-align: left; border: 1px solid #e2e8f0; }
            td { padding: 0.75rem; border: 1px solid #e2e8f0; }
            .text-right { text-align: right; }
            .text-center { text-align: center; }
            .total-amount { text-align: right; font-size: 1.25rem; font-weight: bold; color: #2b6cb0; margin-bottom: 2rem; }
            .footer-grid { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1.5rem; padding-top: 3rem; text-align: center; }
            .footer-title { font-weight: bold; color: #2b6cb0; }
            .print-date { text-align: center; margin-top: 1rem; font-size: 0.875rem; color: #718096; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">OUTSTANDING DETAILS</div>
          </div>

          <div class="details-grid">
            <div>
              <div class="section-title">Customer Details</div>
              <p><strong>Name:</strong> ${customer.customer_name}</p>
              <p><strong>Phone:</strong> ${customer.customer_phone}</p>
            </div>
            <div>
              <div class="section-title">Report Details</div>
              <p><strong>Generated On:</strong> ${new Date().toLocaleString()}</p>
              <p><strong>Total Outstanding:</strong> ${formatCurrency(subtotal)}</p>
            </div>
          </div>

          <div class="section-title">Transaction Details</div>
          <table>
            <thead>
              <tr>
                <th>No</th>
                <th>Type</th>
                <th>Bill Number</th>
                <th>Date</th>
                <th class="text-right">Total Amount</th>
                <th class="text-right">Paid Amount</th>
                <th class="text-right">Balance</th>
                <th class="text-right">Days Outstanding</th>
              </tr>
            </thead>
            <tbody>
              ${customerSales
                .map(
                  (sale, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${sale.type.charAt(0).toUpperCase() + sale.type.slice(1)}</td>
                  <td>${sale.bill_number}</td>
                  <td>${new Date(sale.invoice_date).toLocaleDateString()}</td>
                  <td class="text-right">${formatCurrency(sale.total_amount)}</td>
                  <td class="text-right">${formatCurrency(sale.paid_amount)}</td>
                  <td class="text-right">${formatCurrency(sale.balance)}</td>
                  <td class="text-right">${sale.days_outstanding}</td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>

          <div class="total-amount">
            Total Outstanding: ${formatCurrency(subtotal)}
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
    printWindow.close();
  };

  return (
    <div
      className="overflow-x-auto rounded-lg shadow-lg"
      style={{ maxHeight: "500px", overflowY: "auto" }}
    >
      <table className="w-full bg-white border-collapse dark:bg-gray-800">
        <thead className="sticky top-0 bg-blue-100 dark:bg-blue-800">
          <tr>
            {[
              { title: "Customer Name", align: "left" },
              { title: "Phone Number", align: "left" },
              { title: "0-30 Days", align: "right" },
              { title: "31-60 Days", align: "right" },
              { title: "61-90 Days", align: "right" },
              { title: "Above 91 Days", align: "right" },
              { title: "Subtotal", align: "right" },
              { title: "", align: "right" },
            ].map((header) => (
              <th
                key={header.title}
                className={`px-4 py-3 text-sm font-semibold text-${header.align} text-blue-800 border-b-2 border-blue-200 dark:text-blue-200`}
              >
                {header.title}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((customer, index) => {
            const subtotal =
              (customer.days_0_30 || 0) +
              (customer.days_31_60 || 0) +
              (customer.days_61_90 || 0) +
              (customer.days_above_91 || 0);
            const customerSales = reportData.filter(
              (row) =>
                row.customer_name === customer.customer_name &&
                row.customer_phone === customer.customer_phone
            );
            return (
              <React.Fragment
                key={`${customer.customer_name}-${customer.customer_phone}`}
              >
                <tr
                  className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200 ${
                    subtotal > 0
                      ? "bg-red-100 dark:bg-red-800 animate-pulse"
                      : ""
                  } ${expandedRow === index ? "bg-blue-50 dark:bg-blue-700" : ""}`}
                >
                  <td className="px-4 py-3 text-sm text-blue-600 border-b border-gray-100 dark:border-gray-700 dark:text-blue-400">
                    <button
                      onClick={() => toggleRow(index)}
                      className="text-left hover:underline focus:outline-none"
                    >
                      {customer.customer_name}
                    </button>
                  </td>
                  <td className="px-4 py-3 text-sm text-left text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                    {customer.customer_phone}
                  </td>
                  <td className="px-4 py-3 text-sm text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                    {formatCurrency(customer.days_0_30)}
                  </td>
                  <td className="px-4 py-3 text-sm text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                    {formatCurrency(customer.days_31_60)}
                  </td>
                  <td className="px-4 py-3 text-sm text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                    {formatCurrency(customer.days_61_90)}
                  </td>
                  <td className="px-4 py-3 text-sm text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                    {formatCurrency(customer.days_above_91)}
                  </td>
                  <td className="px-4 py-3 text-sm text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                    {formatCurrency(subtotal)}
                  </td>
                  <td className="px-4 py-3 text-right border-b border-gray-100 dark:border-gray-700">
                    <button
                      onClick={() => toggleRow(index)}
                      title={
                        expandedRow === index
                          ? "Collapse Details"
                          : "Expand Details"
                      }
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white focus:outline-none"
                    >
                      {expandedRow === index ? (
                        <FiChevronUp size={18} />
                      ) : (
                        <FiChevronDown size={18} />
                      )}
                    </button>
                  </td>
                </tr>
                {expandedRow === index && (
                  <tr className="bg-gray-50 dark:bg-gray-900/30">
                    <td colSpan="8" className="px-4 py-4">
                      <div className="p-3 border border-gray-200 rounded-md dark:border-gray-700">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-xs font-semibold tracking-wide text-gray-600 uppercase dark:text-gray-400">
                            Sales Details ({customerSales.length})
                          </h4>
                          <button
                            onClick={() =>
                              printCustomerSales(customer, customerSales)
                            }
                            disabled={customerSales.length === 0}
                            className={`flex items-center gap-2 px-3 py-1 rounded-lg text-white font-medium transition duration-300 ${
                              customerSales.length === 0
                                ? "bg-blue-300 cursor-not-allowed"
                                : "bg-blue-500 hover:bg-blue-600"
                            }`}
                          >
                            <FaPrint size={16} /> Print Sales
                          </button>
                        </div>
                        {customerSales.length > 0 ? (
                          <div className="overflow-x-auto max-h-60">
                            <table className="w-full bg-white border-collapse dark:bg-gray-800">
                              <thead className="bg-gray-100 dark:bg-gray-700">
                                <tr>
                                  {[
                                    { title: "Type", align: "left" },
                                    { title: "Bill Number", align: "left" },
                                    { title: "Date", align: "left" },
                                    { title: "Total Amount", align: "right" },
                                    { title: "Paid Amount", align: "right" },
                                    { title: "Balance", align: "right" },
                                    {
                                      title: "Days Outstanding",
                                      align: "right",
                                    },
                                  ].map((header) => (
                                    <th
                                      key={header.title}
                                      className={`px-4 py-2 text-xs font-semibold text-${header.align} text-gray-700 border-b-2 border-gray-200 dark:text-gray-200`}
                                    >
                                      {header.title}
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody>
                                {customerSales.map((sale, idx) => (
                                  <tr
                                    key={`${sale.type}-${sale.id}-${idx}`}
                                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                                  >
                                    <td className="px-4 py-2 text-xs text-left text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                                      {sale.type.charAt(0).toUpperCase() +
                                        sale.type.slice(1)}
                                    </td>
                                    <td className="px-4 py-2 text-xs text-left text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                                      {sale.bill_number}
                                    </td>
                                    <td className="px-4 py-2 text-xs text-left text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                                      {new Date(
                                        sale.invoice_date
                                      ).toLocaleDateString()}
                                    </td>
                                    <td className="px-4 py-2 text-xs text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                                      {formatCurrency(sale.total_amount)}
                                    </td>
                                    <td className="px-4 py-2 text-xs text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                                      {formatCurrency(sale.paid_amount)}
                                    </td>
                                    <td className="px-4 py-2 text-xs text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                                      {formatCurrency(sale.balance)}
                                    </td>
                                    <td className="px-4 py-2 text-xs text-right text-gray-700 border-b border-gray-100 dark:border-gray-700 dark:text-gray-200">
                                      {sale.days_outstanding}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                              <tfoot>
                                <tr className="font-bold bg-gray-200 dark:bg-gray-700">
                                  <td
                                    className="px-4 py-2 text-xs text-left text-gray-700 dark:text-gray-200"
                                    colSpan={5}
                                  >
                                    Total Outstanding
                                  </td>
                                  <td className="px-4 py-2 text-xs text-right text-gray-700 dark:text-gray-200">
                                    {formatCurrency(subtotal)}
                                  </td>
                                  <td className="px-4 py-2"></td>
                                </tr>
                              </tfoot>
                            </table>
                          </div>
                        ) : (
                          <p className="text-sm text-center text-gray-500 dark:text-gray-400">
                            No sales details available.
                          </p>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            );
          })}
        </tbody>
        <tfoot>
          <tr className="font-bold bg-gray-200 border-t-2 border-gray-400 dark:bg-gray-700 dark:border-gray-600">
            <td
              className="px-4 py-3 text-sm text-left text-gray-700 dark:text-gray-200"
              colSpan={2}
            >
              Total
            </td>
            <td className="px-4 py-3 text-sm text-right text-gray-700 dark:text-gray-200">
              {formatCurrency(totals.days_0_30)}
            </td>
            <td className="px-4 py-3 text-sm text-right text-gray-700 dark:text-gray-200">
              {formatCurrency(totals.days_31_60)}
            </td>
            <td className="px-4 py-3 text-sm text-right text-gray-700 dark:text-gray-200">
              {formatCurrency(totals.days_61_90)}
            </td>
            <td className="px-4 py-3 text-sm text-right text-gray-700 dark:text-gray-200">
              {formatCurrency(totals.days_above_91)}
            </td>
            <td className="px-4 py-3 text-sm text-right text-gray-700 dark:text-gray-200">
              {formatCurrency(totals.subtotal)}
            </td>
            <td className="px-4 py-3"></td>
          </tr>
        </tfoot>
      </table>
    </div>
  );
};

const AgingAnalysis = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const today = new Date().toISOString().split("T")[0];
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const lastMonth = oneMonthAgo.toISOString().split("T")[0];

  const [fromDate, setFromDate] = useState(lastMonth);
  const [toDate, setToDate] = useState(today);
  const [searchQuery, setSearchQuery] = useState("");
  const [reportData, setReportData] = useState([]);
  const [agingData, setAgingData] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [expandedRow, setExpandedRow] = useState(null);

  useEffect(() => {
    if (!user || !["admin", "manager"].includes(user.role)) {
      navigate("/unauthorized");
    }
  }, [user, navigate]);

  const fetchCustomers = useCallback(async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/customers`);
      const customerData = Array.isArray(response.data)
        ? response.data
        : response.data.data || [];
      setCustomers(customerData);
      return customerData;
    } catch (err) {
      console.error("Error fetching customers:", err);
      setError("Failed to fetch customer data for phone numbers");
      return [];
    }
  }, []);

  const normalizeName = (name) => {
    return name ? name.trim().toLowerCase().replace(/\s+/g, " ") : "";
  };

  const validateDates = (from, to) => {
    if (!from || !to) {
      return "Please select both From and To dates.";
    }
    const fromD = new Date(from);
    const toD = new Date(to);
    if (isNaN(fromD.getTime()) || isNaN(toD.getTime())) {
      return "Invalid date format.";
    }
    if (fromD > toD) {
      return "From date must be before or equal to To date.";
    }
    return null;
  };

  const isDateInRange = (dateStr, from, to) => {
    try {
      const date = new Date(dateStr);
      const fromDate = new Date(from);
      const toDate = new Date(to);
      // Set toDate to end of day to include records on toDate
      toDate.setHours(23, 59, 59, 999);
      return date >= fromDate && date <= toDate;
    } catch {
      return false;
    }
  };

  const fetchReportData = useCallback(async () => {
    setIsLoading(true);
    setError("");
    setPhoneError("");

    // Validate dates
    const dateError = validateDates(fromDate, toDate);
    if (dateError) {
      setError(dateError);
      setIsLoading(false);
      return;
    }

    try {
      console.log("Fetching data with params:", { from: fromDate, to: toDate });

      const invoiceResponse = await axios.get(`${API_BASE_URL}/invoices`, {
        params: { from: fromDate, to: toDate, payment_method: "credit" },
        headers: { Accept: "application/json" },
      });
      const salesResponse = await axios.get(`${API_BASE_URL}/sales`, {
        params: { from: fromDate, to: toDate, payment_method: "credit" },
        headers: { Accept: "application/json" },
      });
      const customerData = await fetchCustomers();
      const customerPhoneMap = new Map(
        customerData.map((c) => [
          normalizeName(c.customer_name),
          c.phone || "-",
        ])
      );

      let invoices = Array.isArray(invoiceResponse.data)
        ? invoiceResponse.data
        : invoiceResponse.data.data || [];
      let missingPhoneRecords = [];
      const processedInvoices = invoices
        .filter((invoice) =>
          isDateInRange(
            invoice.invoice_date || invoice.created_at,
            fromDate,
            toDate
          )
        )
        .map((invoice) => {
          const normalizedName = normalizeName(invoice.customer_name);
          const phone =
            invoice.customer_phone ||
            customerPhoneMap.get(normalizedName) ||
            "-";
          if (
            phone === "-" &&
            (invoice.payment_method || "credit").toLowerCase() === "credit"
          ) {
            missingPhoneRecords.push({
              type: "invoice",
              id: invoice.id,
              customer_name: invoice.customer_name,
              phone: phone,
            });
          }
          const total_amount = parseFloat(invoice.total_amount) || 0;
          const balance = parseFloat(invoice.balance) || 0;
          const paid_amount = parseFloat(invoice.purchase_amount) || 0;
          return {
            ...invoice,
            type: "invoice",
            bill_number: invoice.invoice_no || `INV-${invoice.id}`,
            customer_name: invoice.customer_name || "Unknown Customer",
            customer_phone: phone,
            total_amount,
            balance,
            paid_amount,
            invoice_date:
              invoice.invoice_date ||
              invoice.created_at ||
              new Date().toISOString(),
            payment_method: invoice.payment_method || "Credit",
            days_outstanding: calculateDaysOutstanding(
              invoice.invoice_date || invoice.created_at
            ),
          };
        });

      let sales = Array.isArray(salesResponse.data)
        ? salesResponse.data
        : salesResponse.data.data || [];
      const processedSales = sales
        .filter((sale) => isDateInRange(sale.created_at, fromDate, toDate))
        .map((sale) => {
          const normalizedName = normalizeName(sale.customer_name);
          const phone =
            sale.customer_phone || customerPhoneMap.get(normalizedName) || "-";
          if (
            phone === "-" &&
            (sale.payment_type || "credit").toLowerCase() === "credit"
          ) {
            missingPhoneRecords.push({
              type: "sale",
              id: sale.id,
              customer_name: sale.customer_name,
              phone: phone,
            });
          }
          const total_amount = parseFloat(sale.total) || 0;
          const balance = parseFloat(sale.balance_amount) || 0;
          const paid_amount = parseFloat(sale.received_amount) || 0;
          return {
            ...sale,
            type: "sale",
            bill_number: sale.bill_number || `SALE-${sale.id}`,
            customer_name: sale.customer_name || "Walk-in Customer",
            customer_phone: phone,
            total_amount,
            balance,
            paid_amount,
            invoice_date: sale.created_at || new Date().toISOString(),
            payment_method: sale.payment_type || "Credit",
            days_outstanding: calculateDaysOutstanding(sale.created_at),
          };
        });

      const combinedData = [...processedInvoices, ...processedSales]
        .filter((row) => row.payment_method.toLowerCase().includes("credit"))
        .sort((a, b) => new Date(b.invoice_date) - new Date(a.invoice_date));

      console.log("Processed data:", combinedData);

      if (missingPhoneRecords.length > 0) {
        console.warn(
          "Records with missing phone numbers:",
          missingPhoneRecords
        );
        setPhoneError(
          `Phone numbers missing for ${missingPhoneRecords.length} credit record(s). Check console for details.`
        );
      }

      setReportData(combinedData);
      setAgingData(computeAgingData(combinedData));
    } catch (error) {
      console.error(
        "Error fetching aging analysis data:",
        error.response || error
      );
      setError(
        `Error fetching data: ${error.response?.data?.message || error.message}`
      );
      setReportData([]);
      setAgingData([]);
    } finally {
      setIsLoading(false);
    }
  }, [fromDate, toDate, fetchCustomers]);

  useEffect(() => {
    fetchReportData();
  }, [fetchReportData]);

  const filteredData = reportData.filter((row) => {
    const searchableFields = [row.customer_name, row.customer_phone];
    return searchableFields.some(
      (value) =>
        value &&
        value.toString().toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const filteredAgingData = computeAgingData(filteredData);

  const toggleRow = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  const totals = filteredAgingData.reduce(
    (acc, customer) => {
      acc.totalCustomers += 1;
      acc.totalOutstanding +=
        (customer.days_0_30 || 0) +
        (customer.days_31_60 || 0) +
        (customer.days_61_90 || 0) +
        (customer.days_above_91 || 0);
      acc.current += customer.days_0_30 || 0;
      acc.pastDue30 += customer.days_31_60 || 0;
      acc.pastDue60 += customer.days_61_90 || 0;
      acc.pastDue90 += customer.days_above_91 || 0;
      return acc;
    },
    {
      totalCustomers: 0,
      totalOutstanding: 0,
      current: 0,
      pastDue30: 0,
      pastDue60: 0,
      pastDue90: 0,
    }
  );

  const exportToExcel = () => {
    const wb = XLSX.utils.book_new();
    const agingData = filteredAgingData.map((customer) => ({
      "Customer Name": customer.customer_name,
      "Phone Number": customer.customer_phone,
      "0-30 Days": formatCurrency(customer.days_0_30),
      "31-60 Days": formatCurrency(customer.days_31_60),
      "61-90 Days": formatCurrency(customer.days_61_90),
      "Above 91 Days": formatCurrency(customer.days_above_91),
      Subtotal: formatCurrency(
        (customer.days_0_30 || 0) +
          (customer.days_31_60 || 0) +
          (customer.days_61_90 || 0) +
          (customer.days_above_91 || 0)
      ),
    }));
    const reportDetails = [
      ["Authorized By:", "[Manager Name]"],
      ["Generated By:", "[User Name]"],
      ["Date & Time:", new Date().toLocaleString()],
      ["Report Period:", `${fromDate} to ${toDate}`],
      ["Report Generated On:", new Date().toLocaleString()],
      ["Company Contact:", "[Your Business Contact Info]"],
    ];
    const salesDetails = filteredData.map((sale) => ({
      Type: sale.type,
      "Bill Number": sale.bill_number,
      Date: new Date(sale.invoice_date).toLocaleDateString(),
      "Total Amount": formatCurrency(sale.total_amount),
      "Paid Amount": formatCurrency(sale.paid_amount),
      Balance: formatCurrency(sale.balance),
      "Days Outstanding": sale.days_outstanding,
      "Customer Name": sale.customer_name,
      "Phone Number": sale.customer_phone,
    }));
    const agingWs = XLSX.utils.json_to_sheet(agingData);
    const detailsWs = XLSX.utils.aoa_to_sheet(reportDetails);
    const salesWs = XLSX.utils.json_to_sheet(salesDetails);
    XLSX.utils.book_append_sheet(wb, agingWs, "Aging Analysis");
    XLSX.utils.book_append_sheet(wb, detailsWs, "Report Details");
    XLSX.utils.book_append_sheet(wb, salesWs, "Sales Details");
    XLSX.writeFile(wb, `Aging_Analysis_${fromDate}_to_${toDate}.xlsx`);
  };

  const exportToPDF = () => {
    const doc = new jsPDF();
    doc.setFontSize(18);
    doc.text("Accounts Receivable Aging Report", 15, 15);
    const columns = [
      "Customer Name",
      "Phone Number",
      "0-30 Days",
      "31-60 Days",
      "61-90 Days",
      "Above 91 Days",
      "Subtotal",
    ];
    const rows = filteredAgingData.map((customer) => [
      customer.customer_name,
      customer.customer_phone,
      formatCurrency(customer.days_0_30),
      formatCurrency(customer.days_31_60),
      formatCurrency(customer.days_61_90),
      formatCurrency(customer.days_above_91),
      formatCurrency(
        (customer.days_0_30 || 0) +
          (customer.days_31_60 || 0) +
          (customer.days_61_90 || 0) +
          (customer.days_above_91 || 0)
      ),
    ]);
    const totalsRow = [
      "Total",
      "",
      formatCurrency(totals.current),
      formatCurrency(totals.pastDue30),
      formatCurrency(totals.pastDue60),
      formatCurrency(totals.pastDue90),
      formatCurrency(totals.totalOutstanding),
    ];
    autoTable(doc, {
      head: [columns],
      body: [...rows, totalsRow],
      startY: 20,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [30, 64, 175], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [245, 245, 245] },
      columnStyles: {
        0: { halign: "left" },
        1: { halign: "left" },
        2: { halign: "right" },
        3: { halign: "right" },
        4: { halign: "right" },
        5: { halign: "right" },
        6: { halign: "right" },
      },
    });
    doc.text("Sales Details", 15, doc.lastAutoTable.finalY + 10);
    const salesColumns = [
      "Type",
      "Bill Number",
      "Date",
      "Total Amount",
      "Paid Amount",
      "Balance",
      "Days Outstanding",
      "Customer Name",
      "Phone Number",
    ];
    const salesRows = filteredData.map((sale) => [
      sale.type.charAt(0).toUpperCase() + sale.type.slice(1),
      sale.bill_number,
      new Date(sale.invoice_date).toLocaleDateString(),
      formatCurrency(sale.total_amount),
      formatCurrency(sale.paid_amount),
      formatCurrency(sale.balance),
      sale.days_outstanding,
      sale.customer_name,
      sale.customer_phone,
    ]);
    autoTable(doc, {
      head: [salesColumns],
      body: salesRows,
      startY: doc.lastAutoTable.finalY + 20,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [30, 64, 175], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [245, 245, 245] },
      columnStyles: {
        0: { halign: "left" },
        1: { halign: "left" },
        2: { halign: "left" },
        3: { halign: "right" },
        4: { halign: "right" },
        5: { halign: "right" },
        6: { halign: "right" },
        7: { halign: "left" },
        8: { halign: "left" },
      },
    });
    doc.save(`Aging_Analysis_${fromDate}_${toDate}.pdf`);
  };

  const printCustomerDetails = () => {
    const doc = new jsPDF();
    doc.setFontSize(18);
    doc.text("Accounts Receivable Aging Report", 15, 15);
    doc.setFontSize(10);
    doc.text(`Generated By: [User Name]`, 15, 25);
    doc.text(`Date & Time: ${new Date().toLocaleString()}`, 15, 32);
    doc.text(`Report Period: ${fromDate} to ${toDate}`, 15, 39);

    doc.setFontSize(14);
    doc.text("Customer Details", 15, 50);
    const columns = [
      "Customer Name",
      "Phone Number",
      "0-30 Days",
      "31-60 Days",
      "61-90 Days",
      "Above 91 Days",
      "Subtotal",
    ];
    const rows = filteredAgingData.map((customer) => [
      customer.customer_name,
      customer.customer_phone,
      formatCurrency(customer.days_0_30),
      formatCurrency(customer.days_31_60),
      formatCurrency(customer.days_61_90),
      formatCurrency(customer.days_above_91),
      formatCurrency(
        (customer.days_0_30 || 0) +
          (customer.days_31_60 || 0) +
          (customer.days_61_90 || 0) +
          (customer.days_above_91 || 0)
      ),
    ]);
    const totalsRow = [
      "Total",
      "",
      formatCurrency(totals.current),
      formatCurrency(totals.pastDue30),
      formatCurrency(totals.pastDue60),
      formatCurrency(totals.pastDue90),
      formatCurrency(totals.totalOutstanding),
    ];
    autoTable(doc, {
      head: [columns],
      body: [...rows, totalsRow],
      startY: 55,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [30, 64, 175], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [245, 245, 245] },
      columnStyles: {
        0: { halign: "left" },
        1: { halign: "left" },
        2: { halign: "right" },
        3: { halign: "right" },
        4: { halign: "right" },
        5: { halign: "right" },
        6: { halign: "right" },
      },
    });

    const pdfBlob = doc.output("blob");
    const pdfUrl = URL.createObjectURL(pdfBlob);
    const printWindow = window.open(pdfUrl);
    printWindow.onload = () => {
      printWindow.print();
      printWindow.onafterprint = () => {
        printWindow.close();
        URL.revokeObjectURL(pdfUrl);
      };
    };
  };

  return (
    <div className="flex flex-col items-center min-h-screen p-6 font-sans bg-gray-50 dark:bg-gray-900">
      <div className="w-full p-4 mb-6 py-6 text-center text-white rounded-lg shadow-lg bg-gradient-to-r from-blue-600 to-blue-800">
        <div className="flex items-center mb-2 justify-center">
          <Book size={28} className="mr-2" />
          <h1 className="text-2xl font-bold">
            Accounts Receivable Aging Report
          </h1>
        </div>
        <div className="grid grid-cols-1 gap-4 mt-4 md:grid-cols-2 lg:grid-cols-3">
          <p className="text-sm">Generated By: [User Name]</p>
          <p className="text-sm">Date & Time: {new Date().toLocaleString()}</p>
          <p className="text-sm">
            Report Period: {fromDate} to {toDate}
          </p>
        </div>
      </div>

      {(error || phoneError) && (
        <div className="flex items-center justify-between w-full max-w-6xl p-4 mb-4 text-red-800 bg-red-100 rounded-lg shadow-md">
          <div>
            {error && <p>{error}</p>}
            {phoneError && <p>{phoneError}</p>}
          </div>
          <button
            onClick={fetchReportData}
            className="px-3 py-1 text-white font-medium transition bg-blue-500 rounded-md hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      )}

      <div className="w-full max-w-6xl mb-6">
        <h2 className="mb-4 text-xl font-bold dark:text-gray-200">Filters</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <label
              htmlFor="from-date"
              className="block text-sm font-medium text-gray-700 dark:text-gray-200"
            >
              From Date
            </label>
            <input
              id="from-date"
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              className="w-full p-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
            />
          </div>
          <div>
            <label
              htmlFor="to-date"
              className="block text-sm font-medium text-gray-700 dark:text-gray-200"
            >
              To Date
            </label>
            <input
              id="to-date"
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              max={today}
              className="w-full p-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
            />
          </div>
          <div>
            <label
              htmlFor="search"
              className="block text-sm font-medium text-gray-700 dark:text-gray-200"
            >
              Search
            </label>
            <div className="relative">
              <FiSearch className="absolute text-gray-400 top-3 left-3" />
              <input
                id="search"
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by customer name, phone..."
                className="w-full p-2 pl-10 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="w-full max-w-6xl mb-6">
        <h2 className="mb-4 text-xl font-bold dark:text-gray-200">Summary</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="p-4 rounded-lg bg-cyan-800">
            <p className="text-sm text-cyan-500">Total Customers</p>
            <p className="text-2xl font-bold text-cyan-300">
              {totals.totalCustomers}
            </p>
          </div>
          <div className="p-4 rounded-lg bg-rose-800">
            <p className="text-sm text-pink-500">Total Outstanding</p>
            <p className="text-2xl font-bold text-pink-300">
              {formatCurrency(totals.totalOutstanding)}
            </p>
          </div>
          <div className="p-4 rounded-lg bg-lime-800">
            <p className="text-sm text-lime-500">Current (0-30 Days)</p>
            <p className="text-2xl font-bold text-lime-300">
              {formatCurrency(totals.current)}
            </p>
          </div>
          <div className="p-4 bg-purple-800 rounded-lg">
            <p className="text-sm text-purple-500">Past Due (31-60 Days)</p>
            <p className="text-2xl font-bold text-purple-300">
              {formatCurrency(totals.pastDue30)}
            </p>
          </div>
          <div className="p-4 bg-orange-800 rounded-lg">
            <p className="text-sm text-orange-500">Past Due (61-90 Days)</p>
            <p className="text-2xl font-bold text-orange-300">
              {formatCurrency(totals.pastDue60)}
            </p>
          </div>
          <div className="p-4 bg-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-500">Past Due ({">"}90 Days)</p>
            <p className="text-2xl font-bold text-yellow-300">
              {formatCurrency(totals.pastDue90)}
            </p>
          </div>
        </div>
      </div>

      <div id="printable-report" className="w-full max-w-6xl mb-6">
        <div className="flex items-center mb-4">
          <Clock size={20} className="mr-2 text-gray-600 dark:text-gray-400" />
          <h2 className="text-xl font-bold dark:text-gray-200">
            Accounts Receivable Aging
          </h2>
        </div>
        <p className="mb-4 text-gray-700 dark:text-gray-400">
          Overview of outstanding amounts by customer, categorized by aging
          periods.
        </p>
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <div className="w-12 h-12 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
          </div>
        ) : error ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            {error}
          </div>
        ) : filteredAgingData.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            No aging data available for the selected dates.
          </div>
        ) : (
          <AgingAnalysisTable
            data={filteredAgingData}
            reportData={filteredData}
            toggleRow={toggleRow}
            expandedRow={expandedRow}
          />
        )}
      </div>

      <div className="w-full max-w-6xl mt-6">
        <div className="grid grid-cols-1 gap-4 p-4 text-center bg-white rounded-lg shadow-lg dark:bg-gray-800 md:grid-cols-2 lg:grid-cols-3">
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Date & Time:
            </p>
            <p className="text-sm">{new Date().toLocaleString()}</p>
          </div>
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Report Period:
            </p>
            <p className="text-sm">
              {fromDate} to {toDate}
            </p>
          </div>
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Report Generated On:
            </p>
            <p className="text-sm">{new Date().toLocaleDateString()}</p>
          </div>
          <div className="flex justify-center col-span-1 mt-4 space-x-4 md:col-span-2 lg:col-span-3">
            <button
              onClick={exportToExcel}
              disabled={filteredAgingData.length === 0 || isLoading}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white font-medium shadow-md transition duration-300 ${
                filteredAgingData.length === 0 || isLoading
                  ? "bg-yellow-300 cursor-not-allowed"
                  : "bg-yellow-500 hover:bg-yellow-dark-600"
              }`}
            >
              <FaFileExcel /> Export to Excel
            </button>
            <button
              onClick={exportToPDF}
              disabled={filteredAgingData.length === 0 || isLoading}
              className={`flex items-center gap-2 px-4 py-4 rounded-lg text-white font-medium shadow-md transition duration-300 ${
                filteredAgingData.length === 0 || isLoading
                  ? "bg-red-r300 cursor-not-allowed"
                  : "bg-red-blue-500 hover:bg-red-600"
              }`}
            >
              <FaFilePdf /> Export to PDF
            </button>
            <button
              onClick={printCustomerDetails}
              disabled={filteredAgingData.length === 0 || isLoading}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white font-medium shadow-md transition duration-300 ${
                filteredAgingData.length === 0 || isLoading
                  ? "bg-blue-300 cursor-not-allowed"
                  : "bg-blue-500 hover:bg-blue-600"
              }`}
            >
              <FaPrint /> Print Customers
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgingAnalysis;
