import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { FiSearch, FiPrinter, FiDownload, FiChevronDown, FiChevronUp, FiCalendar, FiDollarSign, FiUser, FiCreditCard } from 'react-icons/fi';

const DailySales = () => {
  const [sales, setSales] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedRow, setExpandedRow] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('All');
  const [dateFilter, setDateFilter] = useState('All');
  const [exactDate, setExactDate] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [summaryData, setSummaryData] = useState({
    totalSales: 0,
    totalTax: 0,
    totalDiscount: 0,
    totalItems: 0,
    totalTransactions: 0
  });

  useEffect(() => {
    setLoading(true);
    Promise.all([
      axios.get('http://127.0.0.1:8000/api/sales'),
      axios.get('http://127.0.0.1:8000/api/invoices')
    ])
      .then(([salesRes, invoicesRes]) => {
        // Normalize invoices
        let invoices = [];
        if (invoicesRes.data && Array.isArray(invoicesRes.data.data)) {
          invoices = invoicesRes.data.data;
        } else if (Array.isArray(invoicesRes.data)) {
          invoices = invoicesRes.data;
        }
        const processedInvoices = invoices.map((invoice) => ({
          ...invoice,
          type: 'invoice',
          bill_number: invoice.invoice_no || `INV-${invoice.id}`,
          customer_name: invoice.customer_name || 'Unknown Customer',
          items: Array.isArray(invoice.items)
            ? invoice.items.map(item => ({
                ...item,
                product_name: item.product_name || item.description || item.name || 'Unnamed Product',
              }))
            : [],
          total_amount: parseFloat(invoice.total_amount) || 0,
          subtotal: parseFloat(invoice.subtotal) || 0,
          tax_amount: parseFloat(invoice.tax_amount) || 0,
          discount: parseFloat(invoice.discount_amount) || 0,
          payment_method: invoice.payment_method || 'Cash',
          balance: parseFloat(invoice.balance) || 0,
          purchase_amount: parseFloat(invoice.purchase_amount) || 0,
          invoice_date: invoice.invoice_date || invoice.created_at || new Date().toISOString(),
          created_at: invoice.created_at || invoice.invoice_date || new Date().toISOString(),
          updated_at: invoice.updated_at || invoice.created_at || invoice.invoice_date || new Date().toISOString(),
          status: invoice.status || 'unpaid',
        }));

        // Normalize sales
        let sales = [];
        if (salesRes.data && Array.isArray(salesRes.data.data)) {
          sales = salesRes.data.data;
        } else if (Array.isArray(salesRes.data)) {
          sales = salesRes.data;
        }
        const processedSales = sales.map((sale) => ({
          ...sale,
          type: 'sale',
          bill_number: sale.bill_number || `SALE-${sale.id}`,
          customer_name: sale.customer_name || 'Walk-in Customer',
          items: Array.isArray(sale.items) ? sale.items : [],
          total_amount: parseFloat(sale.total) || 0,
          subtotal: parseFloat(sale.subtotal) || (() => {
            // Calculate subtotal if not provided
            if (Array.isArray(sale.items)) {
              return sale.items.reduce((sum, item) => {
                const quantity = parseFloat(item.quantity || item.qty || 0);
                const price = parseFloat(item.unit_price || item.sales_price || 0);
                return sum + (quantity * price);
              }, 0);
            }
            return 0;
          })(),
          tax_amount: parseFloat(sale.tax) || 0,
          discount: parseFloat(sale.discount) || 0,
          payment_method: sale.payment_type || 'Cash',
          balance: parseFloat(sale.balance_amount) || 0,
          purchase_amount: parseFloat(sale.received_amount) || 0,
          invoice_date: sale.created_at || new Date().toISOString(),
          status: sale.status || 'Completed',
        }));

        // Combine and sort
        const combinedData = [...processedInvoices, ...processedSales].sort(
          (a, b) => new Date(b.invoice_date) - new Date(a.invoice_date)
        );
        setSales(combinedData);
        setInvoices([]);
        calculateSummaryData(combinedData);
      })
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  const calculateSummaryData = (data) => {
    const summary = {
      totalSales: 0,
      totalTax: 0,
      totalDiscount: 0,
      totalItems: 0,
      totalTransactions: data.length
    };

    data.forEach(record => {
      summary.totalSales += parseFloat(record.total_amount) || 0;
      summary.totalTax += parseFloat(record.tax_amount) || 0;
      summary.totalDiscount += parseFloat(record.discount) || 0;
      summary.totalItems += record.items?.length || 0;
    });

    setSummaryData(summary);
  };

  // Combine and sort all records by date (descending)
  const allRecords = [...sales, ...invoices].sort((a, b) => {
    const dateA = new Date(a.created_at || a.invoice_date);
    const dateB = new Date(b.created_at || b.invoice_date);
    return dateB - dateA;
  });

  const getField = (record, saleField, invoiceField) =>
    record.hasOwnProperty('bill_number') ? record[saleField] : record[invoiceField];

  const getItems = (record) => record.items || [];

  const getRecordDate = (record) => {
    if (record.type === 'sale') {
      return record.created_at;
    } else if (record.type === 'invoice') {
      return record.invoice_date;
    }
    return null;
  };

  const filterRecordsByDate = (record, recordDateObj) => {
    switch(dateFilter) {
      case 'All':
        return true;
      case 'Today': {
        const todayDate = new Date();
        return (
          recordDateObj.getDate() === todayDate.getDate() &&
          recordDateObj.getMonth() === todayDate.getMonth() &&
          recordDateObj.getFullYear() === todayDate.getFullYear()
        );
      }
      case 'Yesterday': {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return (
          recordDateObj.getDate() === yesterday.getDate() &&
          recordDateObj.getMonth() === yesterday.getMonth() &&
          recordDateObj.getFullYear() === yesterday.getFullYear()
        );
      }
      case 'This Week': {
        const today = new Date();
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        return recordDateObj >= weekStart && recordDateObj <= weekEnd;
      }
      case 'This Month': {
        const today = new Date();
        return (
          recordDateObj.getMonth() === today.getMonth() &&
          recordDateObj.getFullYear() === today.getFullYear()
        );
      }
      case 'Exact Date': {
        if (!exactDate) return true;
        const exactDateObj = new Date(exactDate);
        return (
          recordDateObj.getDate() === exactDateObj.getDate() &&
          recordDateObj.getMonth() === exactDateObj.getMonth() &&
          recordDateObj.getFullYear() === exactDateObj.getFullYear()
        );
      }
      case 'Date Range': {
        const startDateObj = startDate ? new Date(startDate) : null;
        const endDateObj = endDate ? new Date(endDate) : null;
        
        if (startDateObj && endDateObj) {
          // Set start date to beginning of day (00:00:00)
          const startOfDay = new Date(startDateObj);
          startOfDay.setHours(0, 0, 0, 0);
          
          // Set end date to end of day (23:59:59)
          const endOfDay = new Date(endDateObj);
          endOfDay.setHours(23, 59, 59, 999);
          
          // Set record date to beginning of day for comparison
          const recordStartOfDay = new Date(recordDateObj);
          recordStartOfDay.setHours(0, 0, 0, 0);
          
          return recordStartOfDay >= startOfDay && recordStartOfDay <= endOfDay;
        } else if (startDateObj) {
          const startOfDay = new Date(startDateObj);
          startOfDay.setHours(0, 0, 0, 0);
          
          const recordStartOfDay = new Date(recordDateObj);
          recordStartOfDay.setHours(0, 0, 0, 0);
          
          return recordStartOfDay >= startOfDay;
        } else if (endDateObj) {
          const endOfDay = new Date(endDateObj);
          endOfDay.setHours(23, 59, 59, 999);
          
          const recordStartOfDay = new Date(recordDateObj);
          recordStartOfDay.setHours(0, 0, 0, 0);
          
          return recordStartOfDay <= endOfDay;
        }
        return true;
      }
      default:
        return true;
    }
  };

  const filteredRecords = allRecords.filter(record => {
    const recordDateRaw = getRecordDate(record);
    const recordDateObj = recordDateRaw ? new Date(recordDateRaw) : null;
    
    // Date filtering
    const dateMatch = recordDateObj ? filterRecordsByDate(record, recordDateObj) : true;

    // Payment method filter
    const paymentMethod = (record.payment_method || '').toLowerCase().trim();
    const filter = paymentMethodFilter.toLowerCase().trim();
    let paymentMatch = true;
    if (filter === 'all') {
      paymentMatch = true;
    } else if (filter === 'card') {
      paymentMatch = paymentMethod.includes('card');
    } else {
      paymentMatch = paymentMethod === filter;
    }

    // Search filter
    const billNo = getField(record, 'bill_number', 'invoice_no') || '';
    const customer = getField(record, 'customer_name', 'customer_name') || '';
    const matchesSearch =
      billNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.toLowerCase().includes(searchTerm.toLowerCase());

    return dateMatch && paymentMatch && matchesSearch;
  });

  const formatCurrency = (amount) => {
    const numericAmount = Number(amount);
    if (isNaN(numericAmount)) {
      return 'LKR 0.00';
    }
    return new Intl.NumberFormat('en-LK', {
      style: 'currency',
      currency: 'LKR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numericAmount);
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      completed: 'bg-green-100 text-green-800',
      paid: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      unpaid: 'bg-red-100 text-red-800',
      cancelled: 'bg-red-100 text-red-800',
      refunded: 'bg-purple-100 text-purple-800'
    };
    const className = statusMap[status?.toLowerCase()] || 'bg-gray-100 text-gray-800';
    return <span className={`px-2 py-1 rounded-full text-xs font-semibold ${className}`}>
      {status || 'N/A'}
    </span>;
  };

  const getItemDiscount = (item) =>
    item.discount ?? item.discount_amount ?? item.item_discount ?? 0;

  const getInvoiceTotalDiscount = (record) => {
    if (record.type === 'invoice') {
      const discount =
        record.discount ||
        record.discount_amount ||
        record.total_discount ||
        0;
      if (discount > 0) return discount;
      const subtotal = Number(record.subtotal) || 0;
      const tax = Number(record.tax_amount) || 0;
      const total = Number(record.total_amount) || 0;
      const calculatedDiscount = subtotal - total - tax;
      return calculatedDiscount > 0 ? calculatedDiscount : 0;
    }
    return record.discount || 0;
  };

  const getPaymentMethodIcon = (method) => {
    switch (method.toLowerCase()) {
      case 'cash':
        return <FiDollarSign className="mr-1" />;
      case 'card':
        return <FiCreditCard className="mr-1" />;
      case 'credit':
        return <FiUser className="mr-1" />;
      default:
        return <FiDollarSign className="mr-1" />;
    }
  };

  const handlePrint = () => {
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Sales Report</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            color: #333; 
            font-size: 12px;
          }
          .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 2px solid #333; 
            padding-bottom: 10px;
          }
          .header h1 { 
            margin: 0; 
            font-size: 24px; 
            color: #333;
          }
          .header p { 
            margin: 5px 0; 
            color: #666;
          }
          .summary-section { 
            margin-bottom: 30px;
          }
          .summary-grid { 
            display: grid; 
            grid-template-columns: repeat(5, 1fr); 
            gap: 15px; 
            margin-bottom: 20px;
          }
          .summary-card { 
            border: 1px solid #ddd; 
            padding: 10px; 
            text-align: center; 
            background-color: #f9f9f9;
          }
          .summary-card h3 { 
            margin: 0 0 5px 0; 
            font-size: 14px; 
            color: #666;
          }
          .summary-card .value { 
            font-size: 18px; 
            font-weight: bold; 
            color: #333;
          }
          .summary-card .subtitle { 
            font-size: 10px; 
            color: #666; 
            margin-top: 5px;
          }
          .filters-section { 
            margin-bottom: 20px; 
            padding: 10px; 
            background-color: #f5f5f5; 
            border-radius: 5px;
          }
          .filters-section h3 { 
            margin: 0 0 10px 0; 
            font-size: 14px;
          }
          .filters-grid { 
            display: grid; 
            grid-template-columns: repeat(3, 1fr); 
            gap: 10px;
          }
          .filter-item { 
            font-size: 11px;
          }
          .filter-item strong { 
            color: #333;
          }
          table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-bottom: 20px; 
            font-size: 10px;
          }
          th, td { 
            padding: 8px; 
            text-align: left; 
            border-bottom: 1px solid #ddd; 
            border-right: 1px solid #ddd;
          }
          th { 
            background-color: #f2f2f2; 
            font-weight: bold; 
            text-align: center;
          }
          th:last-child, td:last-child { 
            border-right: none;
          }
          .type-badge { 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-size: 9px; 
            font-weight: bold;
          }
          .type-invoice { 
            background-color: #e3f2fd; 
            color: #1976d2;
          }
          .type-sale { 
            background-color: #e8f5e8; 
            color: #2e7d32;
          }
          .status-badge { 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-size: 9px; 
            font-weight: bold;
          }
          .status-completed, .status-paid { 
            background-color: #e8f5e8; 
            color: #2e7d32;
          }
          .status-pending { 
            background-color: #fff3e0; 
            color: #f57c00;
          }
          .status-unpaid { 
            background-color: #ffebee; 
            color: #c62828;
          }
          .text-right { 
            text-align: right;
          }
          .text-center { 
            text-align: center;
          }
          .font-bold { 
            font-weight: bold;
          }
          .page-break { 
            page-break-after: always;
          }
          @page { 
            size: A4; 
            margin: 15mm;
          }
          .items-table { 
            margin-top: 10px; 
            font-size: 9px;
          }
          .items-table th, .items-table td { 
            padding: 4px 6px;
          }
          .transaction-details { 
            margin-top: 10px; 
            padding: 10px; 
            background-color: #f9f9f9; 
            border-radius: 3px;
          }
          .details-grid { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 10px; 
            font-size: 9px;
          }
          .detail-item { 
            display: flex; 
            justify-content: space-between;
          }
          .detail-label { 
            font-weight: bold; 
            color: #666;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Sales Report</h1>
          <p>Detailed Transaction Report</p>
          <p>Generated on: ${new Date().toLocaleString()}</p>
        </div>

        <div class="summary-section">
          <h3>Summary</h3>
          <div class="summary-grid">
            <div class="summary-card">
              <h3>Total Sales</h3>
              <div class="value">${formatCurrency(summaryData.totalSales)}</div>
              <div class="subtitle">${summaryData.totalTransactions} transactions</div>
            </div>
            <div class="summary-card">
              <h3>Total Tax</h3>
              <div class="value">${formatCurrency(summaryData.totalTax)}</div>
              <div class="subtitle">${summaryData.totalSales > 0 ? ((summaryData.totalTax / summaryData.totalSales) * 100).toFixed(1) : 0}% of sales</div>
            </div>
            <div class="summary-card">
              <h3>Total Discount</h3>
              <div class="value">${formatCurrency(summaryData.totalDiscount)}</div>
              <div class="subtitle">${summaryData.totalSales > 0 ? ((summaryData.totalDiscount / summaryData.totalSales) * 100).toFixed(1) : 0}% of sales</div>
            </div>
            <div class="summary-card">
              <h3>Total Items</h3>
              <div class="value">${summaryData.totalItems}</div>
              <div class="subtitle">~${summaryData.totalTransactions > 0 ? (summaryData.totalItems / summaryData.totalTransactions).toFixed(1) : 0} per transaction</div>
            </div>
            <div class="summary-card">
              <h3>Average Sale</h3>
              <div class="value">${summaryData.totalTransactions > 0 ? formatCurrency(summaryData.totalSales / summaryData.totalTransactions) : formatCurrency(0)}</div>
              <div class="subtitle">per transaction</div>
            </div>
          </div>
        </div>

        <div class="filters-section">
          <h3>Applied Filters</h3>
          <div class="filters-grid">
            <div class="filter-item">
              <strong>Date Filter:</strong> ${dateFilter}
            </div>
            <div class="filter-item">
              <strong>Payment Method:</strong> ${paymentMethodFilter}
            </div>
            <div class="filter-item">
              <strong>Search Term:</strong> ${searchTerm || 'None'}
            </div>
            ${dateFilter === 'Exact Date' && exactDate ? `
              <div class="filter-item">
                <strong>Selected Date:</strong> ${new Date(exactDate).toLocaleDateString()}
              </div>
            ` : ''}
            ${dateFilter === 'Date Range' ? `
              <div class="filter-item">
                <strong>Date Range:</strong> ${startDate ? new Date(startDate).toLocaleDateString() : 'From start'} - ${endDate ? new Date(endDate).toLocaleDateString() : 'To end'}
              </div>
            ` : ''}
          </div>
        </div>

        <h3>Transaction Details (${filteredRecords.length} records)</h3>
        <table>
          <thead>
            <tr>
              <th>Type</th>
              <th>Bill #</th>
              <th>Customer</th>
              <th>Date</th>
              <th>Payment Method</th>
              <th>Status</th>
              <th class="text-right">Subtotal</th>
              <th class="text-right">Tax</th>
              <th class="text-right">Discount</th>
              <th class="text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            ${filteredRecords.map((record, index) => {
              const rowKey = record.type + '-' + record.bill_number;
              const isExpanded = expandedRow === rowKey;
              
              return `
                <tr>
                  <td>
                    <span class="type-badge type-${record.type}">
                      ${record.type === 'invoice' ? 'Invoice' : 'Sale'}
                    </span>
                  </td>
                  <td>${getField(record, 'bill_number', 'invoice_no')}</td>
                  <td>${getField(record, 'customer_name', 'customer_name') || 'Walk-in Customer'}</td>
                  <td>${new Date(getField(record, 'invoice_date')).toLocaleDateString()}</td>
                  <td>${getField(record, 'payment_method', 'payment_method') || 'N/A'}</td>
                  <td>
                    <span class="status-badge status-${(getField(record, 'status', 'status') || '').toLowerCase()}">
                      ${getField(record, 'status', 'status') || 'N/A'}
                    </span>
                  </td>
                  <td class="text-right">${formatCurrency(getField(record, 'subtotal', 'subtotal'))}</td>
                  <td class="text-right">${formatCurrency(getField(record, 'tax_amount', 'tax_amount'))}</td>
                  <td class="text-right">${formatCurrency(getInvoiceTotalDiscount(record))}</td>
                  <td class="text-right font-bold">${formatCurrency(getField(record, 'total_amount', 'total_amount'))}</td>
                </tr>
                ${isExpanded ? `
                  <tr>
                    <td colspan="10" style="padding: 0;">
                      <div class="transaction-details">
                        <div class="details-grid">
                          <div>
                            <div class="detail-item">
                              <span class="detail-label">Bill Number:</span>
                              <span>${getField(record, 'bill_number', 'invoice_no')}</span>
                            </div>
                            <div class="detail-item">
                              <span class="detail-label">Date/Time:</span>
                              <span>${new Date(getField(record, 'invoice_date')).toLocaleString([], {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                              })}</span>
                            </div>
                            <div class="detail-item">
                              <span class="detail-label">Customer:</span>
                              <span>${getField(record, 'customer_name', 'customer_name') || 'Walk-in Customer'}</span>
                            </div>
                            <div class="detail-item">
                              <span class="detail-label">Payment Method:</span>
                              <span>${getField(record, 'payment_method', 'payment_method') || 'N/A'}</span>
                            </div>
                          </div>
                          <div>
                            <div class="detail-item">
                              <span class="detail-label">Subtotal:</span>
                              <span>${formatCurrency(getField(record, 'subtotal', 'subtotal'))}</span>
                            </div>
                            <div class="detail-item">
                              <span class="detail-label">Discount:</span>
                              <span>-${formatCurrency(getInvoiceTotalDiscount(record))}</span>
                            </div>
                            <div class="detail-item">
                              <span class="detail-label">Tax:</span>
                              <span>${formatCurrency(getField(record, 'tax_amount', 'tax_amount'))}</span>
                            </div>
                            <div class="detail-item">
                              <span class="detail-label">Grand Total:</span>
                              <span class="font-bold">${formatCurrency(getField(record, 'total_amount', 'total_amount'))}</span>
                            </div>
                          </div>
                        </div>
                        
                        <h4 style="margin: 15px 0 10px 0; font-size: 12px;">${record.type === 'invoice' ? 'Invoice Items' : 'Sale Items'} (${getItems(record).length || 0})</h4>
                        <table class="items-table">
                          <thead>
                            <tr>
                              <th>Product</th>
                              <th class="text-center">Qty</th>
                              <th class="text-right">Unit Price</th>
                              <th class="text-right">Discount</th>
                              <th class="text-right">Total</th>
                            </tr>
                          </thead>
                          <tbody>
                            ${getItems(record).map((item, idx) => `
                              <tr>
                                <td>
                                  <div>${item.product_name || item.name || item.item_name || item.product || 'Unnamed Product'}</div>
                                  ${item.batch_no ? `<div style="font-size: 8px; color: #666;">Batch: ${item.batch_no}${item.expiry_date ? `, Exp: ${item.expiry_date}` : ''}</div>` : ''}
                                </td>
                                <td class="text-center">${item.quantity || item.qty || '-'}</td>
                                <td class="text-right">${formatCurrency(item.unit_price || item.sales_price || 0)}</td>
                                <td class="text-right">${getItemDiscount(item) ? `-${formatCurrency(getItemDiscount(item))}` : '-'}</td>
                                <td class="text-right font-bold">${formatCurrency(item.total || item.sub_total || 0)}</td>
                              </tr>
                            `).join('')}
                          </tbody>
                        </table>
                      </div>
                    </td>
                  </tr>
                ` : ''}
              `;
            }).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
  };

  const handleExport = () => {
    const headers = ['Type', 'Bill #', 'Customer', 'Date', 'Payment Method', 'Subtotal', 'Tax', 'Discount', 'Total'];
    const csvContent = [
      headers.join(','),
      ...filteredRecords.map(record => [
        record.type === 'invoice' ? 'Invoice' : 'Sale',
        getField(record, 'bill_number', 'invoice_no'),
        getField(record, 'customer_name', 'customer_name') || 'Walk-in Customer',
        new Date(getField(record, 'invoice_date')).toLocaleDateString(),
        getField(record, 'payment_method', 'payment_method') || 'N/A',
        getField(record, 'subtotal', 'subtotal'),
        getField(record, 'tax_amount', 'tax_amount'),
        getInvoiceTotalDiscount(record),
        getField(record, 'total_amount', 'total_amount')
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `sales_report_${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <div id="printable-area" className="bg-white rounded-xl shadow-md overflow-hidden p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 print:hidden">
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Sales Report</h2>
              <p className="text-gray-600">Detailed overview of all transactions</p>
            </div>
            <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiSearch className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search bills or customers..."
                  className="pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <button 
                  onClick={handlePrint}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center gap-2"
                >
                  <FiPrinter /> Print
                </button>
                <button 
                  onClick={handleExport}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center gap-2"
                >
                  <FiDownload /> Export
                </button>
              </div>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6 print:grid-cols-5 print:gap-2">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <div className="text-blue-800 font-medium">Total Sales</div>
              <div className="text-2xl font-bold text-blue-900">{formatCurrency(summaryData.totalSales)}</div>
              <div className="text-sm text-blue-600">{summaryData.totalTransactions} transactions</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg border border-green-100">
              <div className="text-green-800 font-medium">Total Tax</div>
              <div className="text-2xl font-bold text-green-900">{formatCurrency(summaryData.totalTax)}</div>
              <div className="text-sm text-green-600">{(summaryData.totalTax / summaryData.totalSales * 100).toFixed(1)}% of sales</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
              <div className="text-purple-800 font-medium">Total Discount</div>
              <div className="text-2xl font-bold text-purple-900">{formatCurrency(summaryData.totalDiscount)}</div>
              <div className="text-sm text-purple-600">{(summaryData.totalDiscount / summaryData.totalSales * 100).toFixed(1)}% of sales</div>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <div className="text-amber-800 font-medium">Total Items</div>
              <div className="text-2xl font-bold text-amber-900">{summaryData.totalItems}</div>
              <div className="text-sm text-amber-600">~{(summaryData.totalItems / summaryData.totalTransactions).toFixed(1)} per transaction</div>
            </div>
            <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
              <div className="text-indigo-800 font-medium">Avg. Sale</div>
              <div className="text-2xl font-bold text-indigo-900">
                {summaryData.totalTransactions > 0 ? formatCurrency(summaryData.totalSales / summaryData.totalTransactions) : formatCurrency(0)}
              </div>
              <div className="text-sm text-indigo-600">per transaction</div>
            </div>
          </div>

          {/* Filter Controls */}
          <div className="flex flex-wrap gap-4 mb-6 items-end bg-gray-50 p-4 rounded-lg print:hidden">
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Filter</label>
              <select
                className="w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                value={dateFilter}
                onChange={e => setDateFilter(e.target.value)}
              >
                <option value="All">All Dates</option>
                <option value="Today">Today</option>
                <option value="Yesterday">Yesterday</option>
                <option value="This Week">This Week</option>
                <option value="This Month">This Month</option>
                <option value="Exact Date">Exact Date</option>
                <option value="Date Range">Date Range</option>
              </select>
            </div>

            {dateFilter === 'Exact Date' && (
              <div className="flex-1 min-w-[200px]">
                <label className="block text-sm font-medium text-gray-700 mb-1">Select Date</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiCalendar className="text-gray-400" />
                  </div>
                  <input
                    type="date"
                    className="pl-10 w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    value={exactDate}
                    onChange={e => setExactDate(e.target.value)}
                  />
                </div>
              </div>
            )}

            {dateFilter === 'Date Range' && (
              <>
                <div className="flex-1 min-w-[200px]">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiCalendar className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      className="pl-10 w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      value={startDate}
                      onChange={e => setStartDate(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex-1 min-w-[200px]">
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiCalendar className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      className="pl-10 w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      value={endDate}
                      onChange={e => setEndDate(e.target.value)}
                    />
                  </div>
                </div>
              </>
            )}

            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
              <select
                className="w-full border rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                value={paymentMethodFilter}
                onChange={e => setPaymentMethodFilter(e.target.value)}
              >
                <option value="All">All Methods</option>
                <option value="Cash">Cash</option>
                <option value="Card">Card</option>
                <option value="Online">Online</option>
                <option value="Cheque">Cheque</option>
                <option value="Credit">Credit</option>
              </select>
            </div>

            <button 
              onClick={() => {
                setDateFilter('All');
                setExactDate('');
                setStartDate('');
                setEndDate('');
                setPaymentMethodFilter('All');
                setSearchTerm('');
              }}
              className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-sm font-medium"
            >
              Clear Filters
            </button>
          </div>

          {loading && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">Error loading data: {error}</p>
                </div>
              </div>
            </div>
          )}

          {filteredRecords.length === 0 && !loading ? (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="mt-2 text-lg font-medium text-gray-900">No sales found</h3>
              <p className="mt-1 text-gray-500">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="overflow-x-auto rounded-lg border border-gray-200 print:border-0">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 print:bg-white">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill #</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider print:hidden">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRecords.map((record) => {
                    const rowKey = record.type + '-' + record.bill_number;
                    return (
                      <React.Fragment key={rowKey}>
                        <tr
                          className={`hover:bg-gray-50 ${expandedRow === rowKey ? 'bg-blue-50' : ''}`}
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              record.type === 'invoice' 
                                ? 'bg-purple-100 text-purple-800' 
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {record.type === 'invoice' ? 'Invoice' : 'Sale'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="font-medium text-gray-900">{getField(record, 'bill_number', 'invoice_no')}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-gray-900">{getField(record, 'customer_name', 'customer_name') || 'Walk-in Customer'}</div>
                            {getField(record, 'customer_phone') && (
                              <div className="text-gray-500 text-sm">{getField(record, 'customer_phone')}</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right">
                            <div className="font-semibold text-gray-900">{formatCurrency(getField(record, 'total_amount', 'total_amount'))}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-gray-900">{new Date(getField(record, 'invoice_date')).toLocaleDateString()}</div>
                            <div className="text-gray-500 text-sm">
                              {(() => {
                                // For invoices, try to get the actual time from created_at or updated_at
                                let timeSource = getField(record, 'invoice_date');
                                if (record.type === 'invoice') {
                                  // Use created_at or updated_at which should have the actual time
                                  timeSource = record.created_at || record.updated_at || record.invoice_date;
                                }
                                const date = new Date(timeSource);
                                return isNaN(date.getTime()) ? '' : date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true});
                              })()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            {getStatusBadge(getField(record, 'status', 'status'))}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center print:hidden">
                            <button
                              onClick={() => setExpandedRow(expandedRow === rowKey ? null : rowKey)}
                              className="text-blue-600 hover:text-blue-800 flex items-center justify-center w-full"
                            >
                              {expandedRow === rowKey ? (
                                <>
                                  <FiChevronUp className="mr-1" /> Hide
                                </>
                              ) : (
                                <>
                                  <FiChevronDown className="mr-1" /> View
                                </>
                              )}
                            </button>
                          </td>
                        </tr>
                        {expandedRow === rowKey && (
                          <tr>
                            <td colSpan={7} className="px-6 py-4 bg-gray-50 print:bg-white">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                                <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 print:shadow-none print:border-0">
                                  <h3 className="font-medium text-lg text-gray-800 mb-3 border-b pb-2 flex items-center">
                                    <FiUser className="mr-2" /> Customer Details
                                  </h3>
                                  <div className="space-y-3">
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Bill Number:</span>
                                      <span className="font-medium">{getField(record, 'bill_number', 'invoice_no')}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Date/Time:</span>
                                      <span className="font-medium">
                                        {(() => {
                                          // For invoices, try to get the actual time from created_at or updated_at
                                          let timeSource = getField(record, 'invoice_date');
                                          if (record.type === 'invoice') {
                                            // Use created_at or updated_at which should have the actual time
                                            timeSource = record.created_at || record.updated_at || record.invoice_date;
                                          }
                                          const date = new Date(timeSource);
                                          return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleString([], {
                                            year: 'numeric',
                                            month: 'short',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            hour12: true
                                          });
                                        })()}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Customer:</span>
                                      <span className="font-medium">{getField(record, 'customer_name', 'customer_name') || 'Walk-in Customer'}</span>
                                    </div>
                                    {getField(record, 'customer_phone') && (
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">Phone:</span>
                                        <span className="font-medium">{getField(record, 'customer_phone')}</span>
                                      </div>
                                    )}
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Payment Method:</span>
                                      <span className="font-medium inline-flex items-center">
                                        {getPaymentMethodIcon(getField(record, 'payment_method', 'payment_method'))}
                                        {getField(record, 'payment_method', 'payment_method') || 'N/A'}
                                      </span>
                                    </div>
                                    {getField(record, 'notes') && (
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">Notes:</span>
                                        <span className="font-medium">{getField(record, 'notes')}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 print:shadow-none print:border-0">
                                  <h3 className="font-medium text-lg text-gray-800 mb-3 border-b pb-2 flex items-center">
                                    <FiDollarSign className="mr-2" /> Financial Summary
                                  </h3>
                                  <div className="space-y-3">
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Subtotal:</span>
                                      <span className="font-medium">{formatCurrency(getField(record, 'subtotal', 'subtotal'))}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Discount:</span>
                                      <span className="font-medium text-red-600">-{formatCurrency(getInvoiceTotalDiscount(record))}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Tax:</span>
                                      <span className="font-medium">{formatCurrency(getField(record, 'tax_amount', 'tax_amount'))}</span>
                                    </div>
                                    <div className="flex justify-between border-t pt-2">
                                      <span className="text-gray-600 font-semibold">Grand Total:</span>
                                      <span className="font-bold text-blue-600">{formatCurrency(getField(record, 'total_amount', 'grand_total') || 0)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Amount Paid:</span>
                                      <span className="font-medium text-green-600">{formatCurrency(getField(record, 'purchase_amount', 'paid_amount') || 0)}</span>
                                    </div>
                                    <div className="flex justify-between border-t pt-2">
                                      <span className="text-gray-600">Balance:</span>
                                      <span className={`font-medium ${(getField(record, 'balance', 'balance_amount') || 0) > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                                        {formatCurrency(getField(record, 'balance', 'balance_amount') || 0)}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 print:shadow-none print:border-0">
                                <h3 className="font-medium text-lg text-gray-800 mb-3 border-b pb-2">
                                  {record.type === 'invoice' ? 'Invoice Items' : 'Sale Items'} ({getItems(record).length || 0})
                                </h3>
                                <div className="w-full overflow-x-auto">
                                  <table className="w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50 print:bg-white">
                                      <tr>
                                        <th className="py-3 px-4 text-left whitespace-nowrap min-w-[200px]">Product</th>
                                        <th className="py-3 px-4 text-center whitespace-nowrap min-w-[80px]">Qty</th>
                                        <th className="py-3 px-4 text-right whitespace-nowrap min-w-[100px]">Unit Price</th>
                                        <th className="py-3 px-4 text-right whitespace-nowrap min-w-[100px]">Discount</th>
                                        <th className="py-3 px-4 text-right whitespace-nowrap min-w-[100px]">Total</th>
                                      </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                      {getItems(record).map((item, idx) => (
                                        <tr key={idx} className="hover:bg-gray-50">
                                          <td className="py-3 px-4 whitespace-nowrap">
                                            <div className="text-gray-900 font-medium">
                                              {item.product_name || item.name || item.item_name || item.product || 'Unnamed Product'}
                                            </div>
                                            {item.batch_no && (
                                              <div className="text-xs text-gray-500">
                                                Batch: {item.batch_no}{item.expiry_date ? `, Exp: ${item.expiry_date}` : ''}
                                              </div>
                                            )}
                                          </td>
                                          <td className="py-3 px-4 text-center whitespace-nowrap">{item.quantity || item.qty || '-'}</td>
                                          <td className="py-3 px-4 text-right whitespace-nowrap">{formatCurrency(item.unit_price || item.sales_price || 0)}</td>
                                          <td className="py-3 px-4 text-right whitespace-nowrap">
                                            {getItemDiscount(item) ? (
                                              <span className="text-red-600">-{formatCurrency(getItemDiscount(item))}</span>
                                            ) : '-'}
                                          </td>
                                          <td className="py-3 px-4 text-right whitespace-nowrap font-medium">
                                            {formatCurrency(item.total || item.sub_total || 0)}
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DailySales;