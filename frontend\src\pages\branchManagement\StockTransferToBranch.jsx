import React, { useState, useEffect, useCallback } from "react";
import {
  FiSearch,
  FiRefreshCw,
  FiChevronDown,
  FiChevronUp,
  FiPrinter,
  FiEdit,
  FiTrash2,
} from "react-icons/fi";
import { FaFilter, FaFileExcel } from "react-icons/fa";
import * as XLSX from "xlsx";
import axios from "axios";
import { toast } from "react-toastify"; // Import react-toastify
import StockTransferForm from "./StockTransferForm";

const API_BASE_URL = "http://127.0.0.1:8000/api";

const StockTransferToBranch = () => {
  const today = new Date().toISOString().split("T")[0];
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const lastMonth = oneMonthAgo.toISOString().split("T")[0];

  const [fromDate, setFromDate] = useState(lastMonth);
  const [toDate, setToDate] = useState(today);
  const [transferData, setTransferData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedRow, setExpandedRow] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showTransferForm, setShowTransferForm] = useState(false);
  const [transferToEdit, setTransferToEdit] = useState(null);
  const [transferToView, setTransferToView] = useState(null);

  const calculateDaysSince = (transferDate) => {
    const today = new Date();
    const transfer = new Date(transferDate);
    const diffTime = Math.abs(today - transfer);
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  };

  const fetchTransferData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/stock-transfers`, {
        params: { from: fromDate, to: toDate },
        headers: { Accept: "application/json" },
      });

      const transfers = Array.isArray(response.data.data)
        ? response.data.data
        : Array.isArray(response.data)
        ? response.data
        : [];

      const processedTransfers = transfers.map((transfer) => ({
        ...transfer,
        transfer_id: transfer.id,
        transfer_number: transfer.transfer_number || `TRF-${transfer.id}`,
        branch_name: transfer.branch_name || "Unknown Branch",
        transfer_date: transfer.transfer_date || new Date().toISOString(),
        items: Array.isArray(transfer.items)
          ? transfer.items.map((item) => ({
              ...item,
              product_name: item.product_name || "Unknown Product",
              quantity: parseFloat(item.quantity) || 0,
              unit_price: parseFloat(item.unit_price) || 0,
              total: parseFloat(item.total) || 0,
            }))
          : [],
        total_quantity: parseFloat(transfer.total_quantity) || 0,
        total_value: parseFloat(transfer.total_value) || 0,
        status: transfer.status || "Pending",
        daysSinceTransfer: calculateDaysSince(transfer.transfer_date || new Date().toISOString()),
      }));

      setTransferData(processedTransfers.sort((a, b) => new Date(b.transfer_date) - new Date(a.transfer_date)));
    } catch (error) {
      console.error("Error fetching stock transfer data:", error.response || error);
      setTransferData([]);
      toast.error(`Error fetching data: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  }, [fromDate, toDate]);

  useEffect(() => {
    fetchTransferData();
  }, [fetchTransferData]);

  const handleCreateTransfer = async (transferData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/stock-transfers`, transferData, {
        headers: { "Content-Type": "application/json", Accept: "application/json" },
      });
      await fetchTransferData(); // Refresh data after creation
      setShowTransferForm(false);
      setTransferToEdit(null);
      toast.success("Stock transfer created successfully!"); // Use toast for success message
      return response.data;
    } catch (error) {
      console.error("Error creating stock transfer:", error.response || error);
      toast.error(`Error: ${error.response?.data?.message || "Failed to create stock transfer."}`);
      throw error;
    }
  };

  const handleUpdateTransfer = async (transferData, id) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/stock-transfers/${id}`, transferData, {
        headers: { "Content-Type": "application/json", Accept: "application/json" },
      });
      await fetchTransferData(); // Refresh data after update
      setShowTransferForm(false);
      setTransferToEdit(null);
      toast.success("Stock transfer updated successfully!"); // Use toast for success message
      return response.data;
    } catch (error) {
      console.error("Error updating stock transfer:", error.response || error);
      toast.error(`Error: ${error.response?.data?.message || "Failed to update stock transfer."}`);
      throw error;
    }
  };

  const handleDeleteTransfer = async (id) => {
    if (window.confirm(`Are you sure you want to delete Transfer ID: ${id}?`)) {
      setLoading(true);
      try {
        await axios.delete(`${API_BASE_URL}/stock-transfers/${id}`, {
          headers: { Accept: "application/json" },
        });
        toast.success(`Stock transfer ID: ${id} deleted successfully.`); // Use toast for success message
        await fetchTransferData();
      } catch (error) {
        toast.error(`Failed to delete stock transfer: ${error.response?.data?.message || error.message}`);
      } finally {
        setLoading(false);
      }
    }
  };

  const filteredData = transferData.filter((row) =>
    [
      row.transfer_number,
      row.branch_name,
      row.total_quantity?.toString(),
      row.total_value?.toString(),
      new Date(row.transfer_date).toLocaleDateString(),
      row.status,
    ].some((value) => value?.toString().toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const exportToExcel = () => {
    const dataToExport = filteredData.map((row) => ({
      "Transfer #": row.transfer_number,
      Branch: row.branch_name,
      Date: new Date(row.transfer_date).toLocaleDateString(),
      "Total Quantity": row.total_quantity,
      "Total Value": row.total_value,
      Status: row.status,
      "Days Since Transfer": row.daysSinceTransfer,
    }));
    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "StockTransfers");
    XLSX.writeFile(workbook, `Stock_Transfers_${fromDate}_to_${toDate}.xlsx`);
  };

  const toggleRow = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  const formatCurrency = (amount) => {
    const numericAmount = Number(amount);
    return isNaN(numericAmount)
      ? "LKR 0.00"
      : new Intl.NumberFormat("en-LK", {
          style: "currency",
          currency: "LKR",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(numericAmount);
  };

  const handleViewTransfer = (row) => {
    setTransferToView(row);
  };

  return (
    <div className="flex flex-col min-h-screen p-4 bg-transparent">
      <div className="py-3 mb-6 text-center text-white rounded-lg shadow-md bg-gradient-to-r from-blue-500 to-blue-800 dark:bg-gradient-to-r dark:from-blue-900 dark:to-slate-800">
        <h1 className="text-2xl font-bold">STOCK TRANSFER TO BRANCH</h1>
        <p className="text-sm opacity-90">Manage stock transfers to branches</p>
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="relative flex-grow max-w-md">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <FiSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by transfer number, branch, status..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-gray-900 dark:border-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex flex-wrap items-center justify-end gap-2">
          <button
            onClick={() => {
              setTransferToEdit(null);
              setShowTransferForm(true);
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            Add Stock Transfer
          </button>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-slate-800 dark:border-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            <FaFilter /> {showFilters ? "Hide" : "Show"} Filters
          </button>
          <button
            onClick={fetchTransferData}
            disabled={loading}
            title="Refresh Data"
            className={`flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <FiRefreshCw className={`${loading ? "animate-spin" : ""}`} /> Refresh
          </button>
          <button
            onClick={exportToExcel}
            disabled={filteredData.length === 0 || loading}
            title="Export to Excel"
            className={`flex items-center gap-2 px-4 py-2 text-sm text-white bg-green-600 rounded-lg shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${filteredData.length === 0 || loading ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <FaFileExcel /> Export
          </button>
        </div>
      </div>

      {showTransferForm && (
        <StockTransferForm
          initialData={transferToEdit}
          isEditMode={!!transferToEdit}
          onCreateTransfer={handleCreateTransfer}
          onUpdateTransfer={handleUpdateTransfer}
          onCancel={() => {
            setShowTransferForm(false);
            setTransferToEdit(null);
          }}
        />
      )}

      {showFilters && (
        <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-gray-600 dark:text-gray-300 animate-fade-in">
          <h3 className="flex items-center gap-2 mb-3 text-base font-medium">
            <FaFilter /> Transfer Filters
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <label htmlFor="fromDate" className="block mb-1 text-sm font-medium">
                From Date
              </label>
              <input
                id="fromDate"
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className="w-full p-2 bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="toDate" className="block mb-1 text-sm font-medium">
                To Date
              </label>
              <input
                id="toDate"
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                max={today}
                className="w-full p-2 bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={fetchTransferData}
                disabled={loading}
                className={`w-full px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                {loading ? "Applying..." : "Apply Filters"}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 gap-4 mb-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="p-4 bg-white border-l-4 border-blue-500 rounded-lg shadow-md dark:bg-slate-800 dark:border-blue-400">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Transfers
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {filteredData.length}
          </p>
        </div>
        <div className="p-4 bg-white border-l-4 border-green-500 rounded-lg shadow-md dark:bg-slate-800 dark:border-green-400">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Quantity
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {filteredData.reduce((sum, row) => sum + (row.total_quantity || 0), 0)}
          </p>
        </div>
        <div className="p-4 bg-white border-l-4 border-yellow-500 rounded-lg shadow-md dark:bg-slate-800 dark:border-yellow-400">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Value
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(filteredData.reduce((sum, row) => sum + (row.total_value || 0), 0))}
          </p>
        </div>
        <div className="p-4 bg-white border-l-4 border-purple-500 rounded-lg shadow-md dark:bg-slate-800 dark:border-purple-400">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Date Range
          </h3>
          <p className="text-base font-semibold text-gray-800 dark:text-gray-200">
            {new Date(fromDate).toLocaleDateString()} - {new Date(toDate).toLocaleDateString()}
          </p>
        </div>
      </div>

      <div className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-slate-700">
        {loading && transferData.length === 0 ? (
          <div className="flex items-center justify-center p-20">
            <div className="w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-600 dark:text-gray-400">Loading Transfer Data...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
              <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
                <tr>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Transfer #</th>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Branch</th>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Date</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Total Qty</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Total Value</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Status</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-600">
                {filteredData.length === 0 && !loading ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-10 text-center text-gray-500 dark:text-gray-400">
                      No stock transfer data found for the selected period or filters.
                    </td>
                  </tr>
                ) : (
                  filteredData.map((row, index) => (
                    <React.Fragment key={row.transfer_id}>
                      <tr
                        className={`hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors ${
                          expandedRow === index ? "bg-blue-50 dark:bg-slate-700" : ""
                        }`}
                      >
                        <td className="px-4 py-3 font-medium text-blue-600 dark:text-blue-400 whitespace-nowrap">
                          <button onClick={() => toggleRow(index)} className="hover:underline focus:outline-none">
                            #{row.transfer_number}
                          </button>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="font-semibold text-gray-900 dark:text-gray-100">{row.branch_name}</div>
                        </td>
                        <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">
                          {new Date(row.transfer_date).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-3 font-semibold text-right text-gray-800 dark:text-white whitespace-nowrap">
                          {row.total_quantity}
                        </td>
                        <td className="px-4 py-3 font-semibold text-right text-gray-800 dark:text-white whitespace-nowrap">
                          {formatCurrency(row.total_value)}
                        </td>
                        <td className="px-4 py-3 text-right whitespace-nowrap">
                          <span
                            className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${
                              row.status === "Pending"
                                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                            }`}
                          >
                            {row.status} {row.status === "Pending" && `(${row.daysSinceTransfer} days)`}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-right whitespace-nowrap">
                          <div className="flex items-center justify-end gap-x-3">
                            <button
                              onClick={() => handleViewTransfer(row)}
                              title="View Details"
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 focus:outline-none"
                            >
                              <FiPrinter size={16} />
                            </button>
                            <button
                              onClick={() => {
                                setTransferToEdit(row);
                                setShowTransferForm(true);
                              }}
                              title="Edit Transfer"
                              className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 focus:outline-none"
                            >
                              <FiEdit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteTransfer(row.transfer_id)}
                              title="Delete Transfer"
                              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                            >
                              <FiTrash2 size={16} />
                            </button>
                            <button
                              onClick={() => toggleRow(index)}
                              title={expandedRow === index ? "Collapse Details" : "Expand Details"}
                              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white focus:outline-none"
                            >
                              {expandedRow === index ? <FiChevronUp size={18} /> : <FiChevronDown size={18} />}
                            </button>
                          </div>
                        </td>
                      </tr>
                      {expandedRow === index && (
                        <tr className="bg-gray-50 dark:bg-slate-900/30">
                          <td colSpan={7} className="px-4 py-4 md:px-6 md:py-4">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                              <div className="p-3 border border-gray-200 rounded-md dark:border-slate-700">
                                <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase dark:text-gray-400">
                                  Transfer Summary
                                </h4>
                                <div className="grid grid-cols-2 text-sm gap-x-4 gap-y-1">
                                  <div className="text-gray-500 dark:text-gray-400">Total Quantity:</div>
                                  <div className="font-medium text-right text-gray-900 dark:text-white">{row.total_quantity}</div>
                                  <div className="text-gray-500 dark:text-gray-400">Total Value:</div>
                                  <div className="font-medium text-right text-gray-900 dark:text-white">{formatCurrency(row.total_value)}</div>
                                  <div className="text-gray-500 dark:text-gray-400">Status:</div>
                                  <div className="font-medium text-right text-gray-900 capitalize dark:text-white">
                                    {row.status} {row.status === "Pending" && `(${row.daysSinceTransfer} days)`}
                                  </div>
                                </div>
                              </div>
                              <div className="p-3 border border-gray-200 rounded-md dark:border-slate-700">
                                <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase dark:text-gray-400">
                                  Items Transferred ({row.items?.length || 0})
                                </h4>
                                {Array.isArray(row.items) && row.items.length > 0 ? (
                                  <div className="overflow-x-auto max-h-60">
                                    <table className="min-w-full text-xs divide-y divide-gray-200 dark:divide-slate-600">
                                      <thead className="sticky top-0 text-gray-700 bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
                                        <tr>
                                          <th className="px-2 py-1 font-medium text-left">Item</th>
                                          <th className="px-2 py-1 font-medium text-center">Qty</th>
                                          <th className="px-2 py-1 font-medium text-right">Unit Price</th>
                                          <th className="px-2 py-1 font-medium text-right">Total</th>
                                        </tr>
                                      </thead>
                                      <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-700">
                                        {row.items.map((item, i) => (
                                          <tr key={item.id || i}>
                                            <td className="px-2 py-1 font-medium text-gray-900 dark:text-white">{item.product_name}</td>
                                            <td className="px-2 py-1 text-center text-gray-600 dark:text-gray-300">{item.quantity}</td>
                                            <td className="px-2 py-1 text-right text-gray-600 dark:text-gray-300">{formatCurrency(item.unit_price)}</td>
                                            <td className="px-2 py-1 font-semibold text-right text-gray-900 dark:text-white">{formatCurrency(item.total)}</td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                ) : (
                                  <p className="text-sm text-center text-gray-500 dark:text-gray-400">No item details available.</p>
                                )}
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {transferToView && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm animate-fade-in">
          <div className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden bg-white rounded-lg shadow-xl dark:bg-gray-800 flex flex-col">
            <div className="flex items-center justify-between flex-shrink-0 p-4 border-b dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Transfer Preview (#{transferToView.transfer_number})
              </h3>
              <button
                onClick={() => setTransferToView(null)}
                className="p-1 text-gray-500 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 dark:text-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                aria-label="Close preview"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex-grow p-4 overflow-y-auto">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400">Transfer Details</h4>
                  <p><strong>Transfer Number:</strong> {transferToView.transfer_number}</p>
                  <p><strong>Branch:</strong> {transferToView.branch_name}</p>
                  <p><strong>Date:</strong> {new Date(transferToView.transfer_date).toLocaleDateString()}</p>
                  <p><strong>Total Quantity:</strong> {transferToView.total_quantity}</p>
                  <p><strong>Total Value:</strong> {formatCurrency(transferToView.total_value)}</p>
                  <p><strong>Status:</strong> {transferToView.status} {transferToView.status === "Pending" && `(${transferToView.daysSinceTransfer} days)`}</p>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400">Items Transferred</h4>
                  <table className="min-w-full text-sm border border-gray-200 dark:border-gray-600">
                    <thead className="bg-gray-100 dark:bg-gray-700">
                      <tr>
                        <th className="px-4 py-2 text-left">Item</th>
                        <th className="px-4 py-2 text-center">Quantity</th>
                        <th className="px-4 py-2 text-right">Unit Price</th>
                        <th className="px-4 py-2 text-right">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transferToView.items.map((item, i) => (
                        <tr key={i} className="border-t dark:border-gray-600">
                          <td className="px-4 py-2">{item.product_name}</td>
                          <td className="px-4 py-2 text-center">{item.quantity}</td>
                          <td className="px-4 py-2 text-right">{formatCurrency(item.unit_price)}</td>
                          <td className="px-4 py-2 text-right">{formatCurrency(item.total)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div className="flex justify-end flex-shrink-0 p-4 border-t dark:border-gray-700">
              <button
                onClick={() => {
                  const printWindow = window.open("", "_blank");
                  printWindow.document.write(`
                    <html>
                      <head>
                        <title>Print Stock Transfer</title>
                        <style>
                          body { font-family: Arial, sans-serif; margin: 20px; }
                          table { width: 100%; border-collapse: collapse; }
                          th, td { border: 1px solid #000; padding: 8px; }
                          th { background-color: #f2f2f2; }
                          .text-right { text-align: right; }
                          .text-center { text-align: center; }
                        </style>
                      </head>
                      <body>
                        <h2>Stock Transfer #${transferToView.transfer_number}</h2>
                        <p><strong>Branch:</strong> ${transferToView.branch_name}</p>
                        <p><strong>Date:</strong> ${new Date(transferToView.transfer_date).toLocaleDateString()}</p>
                        <p><strong>Total Quantity:</strong> ${transferToView.total_quantity}</p>
                        <p><strong>Total Value:</strong> ${formatCurrency(transferToView.total_value)}</p>
                        <p><strong>Status:</strong> ${transferToView.status} ${transferToView.status === "Pending" ? `(${transferToView.daysSinceTransfer} days)` : ""}</p>
                        <h3>Items Transferred</h3>
                        <table>
                          <thead>
                            <tr>
                              <th class="text-left">Item</th>
                              <th class="text-center">Quantity</th>
                              <th class="text-right">Unit Price</th>
                              <th class="text-right">Total</th>
                            </tr>
                          </thead>
                          <tbody>
                            ${transferToView.items.map((item) => `
                              <tr>
                                <td>${item.product_name}</td>
                                <td class="text-center">${item.quantity}</td>
                                <td class="text-right">${formatCurrency(item.unit_price)}</td>
                                <td class="text-right">${formatCurrency(item.total)}</td>
                              </tr>
                            `).join("")}
                          </tbody>
                        </table>
                      </body>
                    </html>
                  `);
                  printWindow.document.close();
                  printWindow.focus();
                  setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                  }, 500);
                }}
                className="flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              >
                <FiPrinter /> Print
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockTransferToBranch;