import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../context/NewAuthContext';
import { toast } from 'react-toastify';
import { FiDownload, FiFilter, FiEye, FiEyeOff, FiCalendar, FiUser } from 'react-icons/fi';
import * as XLSX from 'xlsx';

const BrokerReport = () => {
  const { user } = useAuth();
  const [reportData, setReportData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [brokers, setBrokers] = useState([]);
  const [expandedBrokers, setExpandedBrokers] = useState(new Set());

  // Filters
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [selectedBroker, setSelectedBroker] = useState('');
  const [showOnlyActive, setShowOnlyActive] = useState(false);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-LK', {
      style: 'currency',
      currency: 'LKR',
    }).format(value);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB');
  };

  // Fetch broker list
  const fetchBrokers = async () => {
    try {
      const response = await axios.get('/api/broker-list');
      if (response.data.success) {
        setBrokers(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching brokers:', error);
    }
  };

  // Fetch report data
  const fetchReportData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = {};
      if (fromDate) params.from_date = fromDate;
      if (toDate) params.to_date = toDate;
      if (selectedBroker) params.broker_name = selectedBroker;
      if (showOnlyActive) params.show_only_active = true;

      const response = await axios.get('/api/broker-reports', { params });
      
      if (response.data.success) {
        setReportData(response.data.data);
      } else {
        setError('Failed to fetch report data');
        toast.error('Failed to fetch report data');
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      setError('Failed to fetch report data');
      toast.error('Failed to fetch report data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBrokers();
  }, []);

  useEffect(() => {
    fetchReportData();
  }, [fromDate, toDate, selectedBroker, showOnlyActive]);

  const toggleBrokerExpansion = (brokerId) => {
    const newExpanded = new Set(expandedBrokers);
    if (newExpanded.has(brokerId)) {
      newExpanded.delete(brokerId);
    } else {
      newExpanded.add(brokerId);
    }
    setExpandedBrokers(newExpanded);
  };

  const exportToExcel = () => {
    const workbook = XLSX.utils.book_new();
    
    // Create summary sheet
    const summaryData = reportData.map(broker => ({
      'Broker Name': broker.broker.name,
      'Staff ID': broker.broker.staff_id,
      'Mobile': broker.broker.mobile_no || 'N/A',
      'WhatsApp': broker.broker.whatsapp_no || 'N/A',
      'Invoice Count': broker.invoices.count,
      'Invoice Total': broker.invoices.total_amount,
      'Quotation Count': broker.quotations.count,
      'Quotation Total': broker.quotations.total_amount,
      'Total Sales': broker.summary.total_sales,
      'Total Transactions': broker.summary.total_transactions,
    }));

    const summarySheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Broker Summary');

    // Create detailed invoice sheet
    const invoiceData = [];
    reportData.forEach(broker => {
      broker.invoices.data.forEach(invoice => {
        invoiceData.push({
          'Broker Name': broker.broker.name,
          'Invoice No': invoice.invoice_no,
          'Date': formatDate(invoice.invoice_date),
          'Customer': invoice.customer_name,
          'Amount': invoice.total_amount,
          'Status': invoice.status,
          'Approved By': invoice.approved_by || 'N/A',
        });
      });
    });

    if (invoiceData.length > 0) {
      const invoiceSheet = XLSX.utils.json_to_sheet(invoiceData);
      XLSX.utils.book_append_sheet(workbook, invoiceSheet, 'Invoices');
    }

    // Create detailed quotation sheet
    const quotationData = [];
    reportData.forEach(broker => {
      broker.quotations.data.forEach(quotation => {
        quotationData.push({
          'Broker Name': broker.broker.name,
          'Quotation No': quotation.quotation_no,
          'Date': formatDate(quotation.date),
          'Customer': quotation.customer_name,
          'Amount': quotation.total,
          'Approved By': quotation.approved_by || 'N/A',
        });
      });
    });

    if (quotationData.length > 0) {
      const quotationSheet = XLSX.utils.json_to_sheet(quotationData);
      XLSX.utils.book_append_sheet(workbook, quotationSheet, 'Quotations');
    }

    // Save the file
    const fileName = `Broker_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);
    toast.success('Report exported successfully!');
  };

  const clearFilters = () => {
    setFromDate('');
    setToDate('');
    setSelectedBroker('');
    setShowOnlyActive(false);
  };

  const totalSales = reportData.reduce((sum, broker) => sum + broker.summary.total_sales, 0);
  const totalTransactions = reportData.reduce((sum, broker) => sum + broker.summary.total_transactions, 0);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Broker Report</h1>
          <p className="text-gray-600">View sales and quotation performance by broker</p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FiFilter className="mr-2" />
              Filters
            </h2>
            <button
              onClick={clearFilters}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Clear All
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Broker
              </label>
              <select
                value={selectedBroker}
                onChange={(e) => setSelectedBroker(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Brokers</option>
                {brokers.map((broker) => (
                  <option key={broker.id} value={broker.name}>
                    {broker.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={showOnlyActive}
                  onChange={(e) => setShowOnlyActive(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  Show Only Active Brokers
                </span>
              </label>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiUser className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Brokers</p>
                <p className="text-2xl font-bold text-gray-900">{reportData.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Sales</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalSales)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Transactions</p>
                <p className="text-2xl font-bold text-gray-900">{totalTransactions}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Export Button */}
        <div className="flex justify-end mb-6">
          <button
            onClick={exportToExcel}
            disabled={loading || reportData.length === 0}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FiDownload className="mr-2" />
            Export to Excel
          </button>
        </div>

        {/* Report Data */}
        {loading ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading report data...</p>
          </div>
        ) : error ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-red-600">{error}</p>
          </div>
        ) : reportData.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-gray-600">No data found for the selected filters.</p>
          </div>
        ) : (
          <div className="space-y-6">
            {reportData.map((brokerData) => (
              <div key={brokerData.broker.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                {/* Broker Header */}
                <div className="bg-gray-50 px-6 py-4 border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <button
                        onClick={() => toggleBrokerExpansion(brokerData.broker.id)}
                        className="mr-3 text-gray-500 hover:text-gray-700"
                      >
                        {expandedBrokers.has(brokerData.broker.id) ? (
                          <FiEyeOff className="w-5 h-5" />
                        ) : (
                          <FiEye className="w-5 h-5" />
                        )}
                      </button>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {brokerData.broker.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          ID: {brokerData.broker.staff_id} | 
                          Mobile: {brokerData.broker.mobile_no || 'N/A'} | 
                          WhatsApp: {brokerData.broker.whatsapp_no || 'N/A'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-green-600">
                        {formatCurrency(brokerData.summary.total_sales)}
                      </p>
                      <p className="text-sm text-gray-600">
                        {brokerData.summary.total_transactions} transactions
                      </p>
                    </div>
                  </div>
                </div>

                {/* Broker Details */}
                {expandedBrokers.has(brokerData.broker.id) && (
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Invoices */}
                      <div>
                        <h4 className="text-md font-semibold text-gray-900 mb-3">
                          Invoices ({brokerData.invoices.count})
                        </h4>
                        {brokerData.invoices.count > 0 ? (
                          <div className="space-y-2">
                            {brokerData.invoices.data.map((invoice) => (
                              <div key={invoice.id} className="bg-gray-50 p-3 rounded-md">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <p className="font-medium text-gray-900">{invoice.invoice_no}</p>
                                    <p className="text-sm text-gray-600">{invoice.customer_name}</p>
                                    <p className="text-xs text-gray-500">{formatDate(invoice.invoice_date)}</p>
                                  </div>
                                  <div className="text-right">
                                    <p className="font-semibold text-gray-900">
                                      {formatCurrency(invoice.total_amount)}
                                    </p>
                                    <p className="text-xs text-gray-500 capitalize">{invoice.status}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-sm">No invoices found</p>
                        )}
                      </div>

                      {/* Quotations */}
                      <div>
                        <h4 className="text-md font-semibold text-gray-900 mb-3">
                          Quotations ({brokerData.quotations.count})
                        </h4>
                        {brokerData.quotations.count > 0 ? (
                          <div className="space-y-2">
                            {brokerData.quotations.data.map((quotation) => (
                              <div key={quotation.id} className="bg-gray-50 p-3 rounded-md">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <p className="font-medium text-gray-900">{quotation.quotation_no}</p>
                                    <p className="text-sm text-gray-600">{quotation.customer_name}</p>
                                    <p className="text-xs text-gray-500">{formatDate(quotation.date)}</p>
                                  </div>
                                  <div className="text-right">
                                    <p className="font-semibold text-gray-900">
                                      {formatCurrency(quotation.total)}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {quotation.approved_by ? 'Approved' : 'Pending'}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-sm">No quotations found</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BrokerReport;