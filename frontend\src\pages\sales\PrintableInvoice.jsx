import React from "react";

const PrintableInvoice = ({ invoiceData }) => {
  // Destructure from invoiceData with defaults
  const {
    customer = { name: "N/A", address: "N/A", phone: "N/A", email: "N/A" },
    items = [],
    footerDetails = {
      approvedBy: "N/A",
      nextApprovalTo: "N/A",
      dateTime: "N/A",
    },
    total = 0,
    invoice = { no: "N/A", date: "N/A", time: "N/A" },
  } = invoiceData || {};

  // Calculate totals from items
  const mrpTotal = items.reduce((sum, item) => {
    // MRP: use item.variant_mrp, item.mrp, or item.unit_price (for invoices)
    const mrp = Number(item.variant_mrp || item.mrp || item.unit_price || 0);
    const qty = Number(item.quantity || item.qty || 0);
    return sum + mrp * qty;
  }, 0);

  const salesTotal = items.reduce((sum, item) => {
    // Sales price: use item.sales_price, item.unit_price, or item.unitPrice
    const salesPrice = Number(item.sales_price || item.unit_price || item.unitPrice || 0);
    const qty = Number(item.quantity || item.qty || 0);
    return sum + salesPrice * qty;
  }, 0);

  const discountTotal = items.reduce((sum, item) => {
    return sum + Number(item.discountAmount || 0);
  }, 0);

  // Calculate overall discount percentage: (total discount amount/total mrp price) * 100
  const overallDiscountPercentage = mrpTotal > 0 ? (discountTotal / mrpTotal) * 100 : 0;

  return (
    <div className="p-2 text-gray-900 bg-white printable-invoice">
      {/* Added printable-invoice class */}
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold">FAREES RE BORING CENTER</h1>
        <p className="ml-2 text-sm text-gray-700">MAIN STREET MALIGAIKADU</p>
        <p className="ml-2 text-sm text-gray-700">Contact No: <b>0770470876 | 0757285249 | 0775021352</b></p>
        {/* <p className="ml-2 text-sm text-gray-700">Email: <b><EMAIL></b></p> */}
      </div>
      {/* Invoice & Customer Details */}
      <div className="flex justify-between px-2 pt-5 pb-2 just">
        <div>
          <p className="text-gray-700">
            Invoice To:<strong> {customer.name || "N/A"} </strong>
          </p>
          <p className="text-gray-700">
            Address:<strong> {customer.address || "N/A"} </strong>
          </p>
        </div>
        <div>
          <p className="text-gray-700">
            Invoice No:<strong> {invoice.no || "N/A"} </strong>
          </p>
          <p className="text-gray-700">
            Date & Time:<strong> {invoice.date || "N/A"} , {invoice.time || "N/A"} </strong>
          </p>
        </div>
      </div>
      {/* Items Table */}
      <div className="overflow-hidden">
        <table className="w-full mb-8">
          <thead className="border-t-2 border-gray-500">
            <tr className="text-sm text-black ">
              <th className="p-2 text-center ">
                Description
              </th>
              <th className="p-2 text-center ">Batch No</th>
              <th className="p-2 text-center ">Exp Date</th>
              <th className="p-2 text-center ">Qty</th>
              <th className="p-2 text-center ">
                MRP
              </th>
              <th className="p-2 text-center ">
                Price
              </th>
              <th className="p-2 text-center ">
                Dis
              </th>
              <th className="p-2 text-center ">
                Dis %
              </th>
              <th className="p-2 text-center ">
                Total
              </th>
              <th className="p-2 text-center ">Retail Price</th>
            </tr>
          </thead>
          {/* <p className="border-t-2 border-gray-500"></p> */}
          <tbody className="text-sm border-t-2 border-gray-500">
            {items.length > 0 ? (
              items.map((item, index) => (
                <React.Fragment key={item.id || index}>
                  <tr>
                    <td className="p-1 text-left text-gray-700 align-top" rowSpan={2}>
                      {item.description || "N/A"}
                    </td>
                  </tr>
                  <tr>
                    {/* Batch No */}
                    <td className="p-1 text-center text-gray-700 ">
                      {item.batch_number ||
                       (item.product_variant && item.product_variant.batch_number) ||
                       "-"}
                    </td>
                    {/* Exp Date */}
                    <td className="p-1 text-center text-gray-700 ">
                      {item.expiry_date
                        ? String(item.expiry_date).split("T")[0]
                        : (item.product_variant && item.product_variant.expiry_date
                          ? String(item.product_variant.expiry_date).split("T")[0]
                          : "-")}
                    </td>
                    {/* Qty */}
                    <td className="p-1 text-center text-gray-700 ">{item.quantity || item.qty || 0}</td>
                    {/* MRP */}
                    <td className="p-1 text-center text-gray-700 ">{Number(item.unit_price && item.sales_price ? item.unit_price : item.variant_mrp || item.mrp || 0).toFixed(2)}</td>
                    {/* Price */}
                    <td className="p-1 text-center text-gray-700 ">{Number(item.unit_price && item.sales_price ? item.sales_price : item.unit_price || item.unitPrice || 0).toFixed(2)}</td>
                    {/* Dis */}
                    <td className="p-1 text-center text-gray-700 ">{Number(item.discountAmount || 0).toFixed(2)}</td>
                    {/* Dis % */}
                    <td className="p-1 text-center text-gray-700 ">
                      {(() => {
                        // Calculate item-specific discount percentage: (item discount / item mrp total) * 100
                        const itemMrp = Number(item.unit_price && item.sales_price ? item.unit_price : item.variant_mrp || item.mrp || 0);
                        const itemQty = Number(item.quantity || item.qty || 0);
                        const itemMrpTotal = itemMrp * itemQty;
                        const itemDiscount = Number(item.discountAmount || 0);
                        const itemDiscountPercentage = itemMrpTotal > 0 ? (itemDiscount / itemMrpTotal) * 100 : 0;
                        return itemDiscountPercentage.toFixed(2);
                      })()}%
                    </td>
                    {/* Total */}
                    <td className="p-1 text-center text-gray-700 ">{Number(item.total || 0).toFixed(2)}</td>
                    {/* Retail Price */}
                    <td className="p-1 text-center text-gray-700">{Number((item.unit_price || 0) * (item.quantity || item.qty || 0)).toFixed(2)}</td>
                  </tr>
                </React.Fragment>
              ))
            ) : (
              <tr>
                <td colSpan={10} className="p-3 text-center text-gray-700">
                  No items available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {/* Total Amount */}
      <div className="m-3 text-right border-t-2 border-gray-500">
        <p className="text-sm">
          Mrp Total Value: <strong className="text-md">LKR {mrpTotal.toFixed(2)}</strong>
        </p>
        <p className="text-sm">
          Discount Value: <strong className="text-md">LKR {discountTotal.toFixed(2)}</strong>
        </p>
        <p className="text-sm">
          Discount %: <strong className="text-md">{overallDiscountPercentage.toFixed(2)}%</strong>
        </p>
        <p className="text-sm">
          Net Invoice Value: <strong className="text-md">LKR {salesTotal.toFixed(2)}</strong>
        </p>
      </div>
    </div>
  );
};

export default PrintableInvoice;
