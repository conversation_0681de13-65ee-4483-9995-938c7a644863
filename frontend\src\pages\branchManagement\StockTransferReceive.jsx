import React, { useState, useEffect, useCallback } from "react";
import {
  FiSearch,
  FiRefreshCw,
  FiChevronDown,
  FiChevronUp,
  FiCheck,
  FiInfo,
  FiFilter,
} from "react-icons/fi";
import { FaFileExcel } from "react-icons/fa";
import * as XLSX from "xlsx";
import axios from "axios";
import { toast } from "react-toastify";
import { useAuth } from "../../context/NewAuthContext";

const API_BASE_URL = "http://127.0.0.1:8000/api";

const StockTransferReceive = () => {
  const { user } = useAuth();
  
  // Extensive debugging of the user object
  console.log("Auth user object:", user);
  console.log("User ID field check:", {
    "user.id": user?.id,
    "user.user_id": user?.user_id,
    "user.id available": !!user?.id,
    "user.user_id available": !!user?.user_id,
    "typeof user": typeof user,
    "All user keys": user ? Object.keys(user) : "No user object" 
  });
  
  const today = new Date().toISOString().split("T")[0];
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const lastMonth = oneMonthAgo.toISOString().split("T")[0];

  const [fromDate, setFromDate] = useState(lastMonth);
  const [toDate, setToDate] = useState(today);
  const [transfers, setTransfers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedRow, setExpandedRow] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  // Initialize with null - we'll get the branch ID directly from the database
  const [userBranchId, setUserBranchId] = useState(null);
  const [processingId, setProcessingId] = useState(null);
  const [branchName, setBranchName] = useState("");

  // Get user's branch ID directly from branch_user table when component mounts
  useEffect(() => {
    const fetchUserBranchFromDB = async () => {
      // Don't do anything if we already have a branch ID
      if (userBranchId) {
        console.log("Already have branch ID:", userBranchId);
        return;
      }
      
      // Check authentication and user ID (could be stored as 'id' or 'user_id')
      const userId = user?.id || user?.user_id;
      if (!user || !user.token || !userId) {
        console.log("User not authenticated or missing user ID");
        return;
      }
      
      console.log("Fetching branch assignment for user ID:", userId);
      setLoading(true);
      
      try {
        console.log(`Attempting to fetch branch assignment for user ID: ${userId}`);
        console.log("Authorization token:", user.token ? `${user.token.substring(0, 15)}...` : "Missing");
        
        // First try the branch-users endpoint
        let response;
        try {
          console.log(`Making API call to: ${API_BASE_URL}/branch-users/${userId}`);
          response = await axios.get(`${API_BASE_URL}/branch-users/${userId}`, {
            headers: { Authorization: `Bearer ${user.token}` },
          });
        } catch (primaryError) {
          console.log("First endpoint failed, trying alternate endpoint");
          // If the first endpoint fails, try the users/{id}/branch endpoint as fallback
          response = await axios.get(`${API_BASE_URL}/users/${userId}/branch`, {
            headers: { Authorization: `Bearer ${user.token}` },
          });
        }
        
        console.log("Branch-user API response:", response.data);
        
        console.log("Parsing branch response:", response.data);
        
        // Handle response.data.data structure (common in Laravel API responses)
        const responseData = response.data?.data || response.data;
        
        if (responseData && responseData.branch_id) {
          // Single branch assignment object
          console.log("Found branch assignment in branch_user table:", responseData);
          setUserBranchId(responseData.branch_id);
          setBranchName(responseData.branch_name || 'Your Branch');
          toast.success(`Connected to branch: ${responseData.branch_name || 'Your Branch'}`);
        } else if (responseData && Array.isArray(responseData) && responseData.length > 0) {
          // Array of branch assignments - use the first one
          const primaryBranch = responseData[0];
          console.log("Found branch assignment in array:", primaryBranch);
          setUserBranchId(primaryBranch.branch_id);
          setBranchName(primaryBranch.branch_name || 'Your Branch');
          toast.success(`Connected to branch: ${primaryBranch.branch_name || 'Your Branch'}`);
          
          // If multiple branches, show info
          if (responseData.length > 1) {
            toast.info(`You have access to ${responseData.length} branches. Using primary branch.`);
          }
        } else {
          console.log("No branch assignment found in branch_user table");
          toast.warning("You don't have any branch assignments. Please contact your administrator.");
        }
      } catch (error) {
        console.error("Error fetching branch assignment:", error);
        toast.error("Could not retrieve your branch assignment. Please try again later.");
        
        // Fallback for development/testing only
        if (process.env.NODE_ENV === 'development') {
          const userId = user?.id || user?.user_id;
          // Check if this is a known user (Sharvaksha with ID 2)
          if (userId === 2) {
            console.log("Development fallback: Setting branch ID for Sharvaksha user");
            setUserBranchId("IMSSKA001");
            toast.info("DEV MODE: Automatically connected to Kalmunai branch");
          }
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUserBranchFromDB();
  }, [user]);

  const fetchPendingTransfers = useCallback(async () => {
    if (!userBranchId) {
      console.log("Cannot fetch transfers - no branch ID available");
      toast.warning("Please select a branch first");
      return;
    }
    
    if (!user?.token) {
      console.log("Cannot fetch transfers - no auth token available");
      toast.error("Authentication token missing. Please try logging in again.");
      return;
    }
    
    console.log("Fetching pending transfers for branch ID:", userBranchId);
    setLoading(true);
    
    try {
      // Use a try-catch to handle potential API errors
      const response = await axios.get(`${API_BASE_URL}/stock-transfers/pending/${userBranchId}`, {
        params: { from: fromDate, to: toDate },
        headers: { 
          Accept: "application/json",
          Authorization: `Bearer ${user.token}` 
        },
      });
      
      console.log("Pending transfers response:", response.data);

      // Handle the response data safely
      let rawTransfers = [];
      if (response.data?.data && Array.isArray(response.data.data)) {
        rawTransfers = response.data.data;
      } else if (Array.isArray(response.data)) {
        rawTransfers = response.data;
      }

      // Process the transfers with safety checks
      const processedTransfers = rawTransfers.map((transfer) => ({
        ...transfer,
        transfer_id: transfer.id,
        transfer_number: transfer.transfer_number || `TRF-${transfer.id || 'unknown'}`,
        branch_name: transfer.branch_name || "Unknown Branch",
        from_branch_name: transfer.from_branch_name || "Main Warehouse",
        transfer_date: transfer.transfer_date || new Date().toISOString(),
        items: Array.isArray(transfer.items)
          ? transfer.items.map((item) => ({
              ...item,
              product_name: item.product_name || "Unknown Product",
              quantity: parseFloat(item.quantity) || 0,
              unit_price: parseFloat(item.unit_price) || 0,
              total: parseFloat(item.total) || 0,
            }))
          : [],
        total_quantity: parseFloat(transfer.total_quantity) || 0,
        total_value: parseFloat(transfer.total_value) || 0,
        status: transfer.status || "Pending",
        daysSinceSent: calculateDaysSince(transfer.transfer_date || new Date().toISOString()),
      }));

      // Sort by date and update state
      setTransfers(processedTransfers.sort((a, b) => new Date(b.transfer_date) - new Date(a.transfer_date)));
      
      // Show a toast if no transfers found
      if (processedTransfers.length === 0) {
        toast.info("No pending transfers found for your branch");
      }
    } catch (error) {
      console.error("Error fetching pending stock transfers:", error);
      
      setTransfers([]);
      toast.error(`Error: ${error.response?.data?.message || error.message || "Failed to fetch transfers"}`);
      
      // If server returns 404, it might mean no transfers or branch not found
      if (error.response?.status === 404) {
        toast.info("No transfers found for this branch");
      }
    } finally {
      setLoading(false);
    }
  }, [userBranchId, fromDate, toDate, user?.token]);

  useEffect(() => {
    if (userBranchId) {
      console.log("Branch ID is now available:", userBranchId);
      console.log("Triggering fetchPendingTransfers after branch ID set");
      fetchPendingTransfers();
    }
  }, [fetchPendingTransfers, userBranchId]);
  
  // Separate useEffect to detect when branch ID changes specifically
  useEffect(() => {
    if (userBranchId) {
      if (branchName) {
        toast.info(`Working with branch: ${branchName} (${userBranchId})`);
      } else {
        toast.info(`Working with branch ID: ${userBranchId}`);
      }
    }
  }, [userBranchId, branchName]);

  const calculateDaysSince = (transferDate) => {
    const today = new Date();
    const transfer = new Date(transferDate);
    const diffTime = Math.abs(today - transfer);
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  };

  const handleReceiveTransfer = async (transferId, transferNumber) => {
    if (!userBranchId) {
      toast.error("No branch assigned. Cannot receive transfers.");
      return;
    }
    
    if (!window.confirm(`Are you sure you want to receive transfer #${transferNumber}? This will update stock in ${branchName || 'your branch'}.`)) {
      return;
    }
    
    console.log(`Receiving transfer ID ${transferId} for branch ID ${userBranchId}`);
    setProcessingId(transferId);
    
    try {
      const userId = user?.id || user?.user_id;
      const response = await axios.patch(
        `${API_BASE_URL}/stock-transfers/${transferId}/receive`,
        { 
          branch_id: userBranchId,
          received_by: userId,
          notes: `Received by ${user.name} on ${new Date().toLocaleString()}`
        },
        {
          headers: { 
            "Content-Type": "application/json",
            Authorization: `Bearer ${user.token}` 
          },
        }
      );
      
      console.log("Receive transfer API response:", response.data);
      
      // Success message with transfer details
      toast.success(`Transfer #${transferNumber} received successfully!`);
      
      // Refresh the list to update status
      fetchPendingTransfers();
    } catch (error) {
      console.error("Error receiving stock transfer:", error.response || error);
      
      // More specific error messages based on status codes
      if (error.response?.status === 403) {
        toast.error("You don't have permission to receive transfers for this branch.");
      } else if (error.response?.status === 404) {
        toast.error("Transfer not found or already processed.");
      } else if (error.response?.status === 409) {
        toast.error("Transfer cannot be received. It may have already been processed.");
      } else if (error.response?.status === 422) {
        toast.error("Validation error: " + (error.response?.data?.message || "Please check the transfer data."));
      } else {
        toast.error(`Error: ${error.response?.data?.message || error.message || "Failed to receive stock transfer."}`);
      }
    } finally {
      setProcessingId(null);
    }
  };

  const filteredTransfers = transfers.filter((transfer) =>
    [
      transfer.transfer_number,
      transfer.from_branch_name,
      transfer.total_quantity?.toString(),
      transfer.total_value?.toString(),
      new Date(transfer.transfer_date).toLocaleDateString(),
    ].some((value) => value?.toString().toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const exportToExcel = () => {
    const dataToExport = filteredTransfers.map((transfer) => ({
      "Transfer #": transfer.transfer_number,
      "From": transfer.from_branch_name,
      "To": transfer.branch_name,
      "Date": new Date(transfer.transfer_date).toLocaleDateString(),
      "Total Quantity": transfer.total_quantity,
      "Total Value": formatCurrency(transfer.total_value).replace("LKR ", ""),
      "Days Pending": transfer.daysSinceSent,
    }));
    
    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "PendingTransfers");
    XLSX.writeFile(workbook, `Pending_Stock_Transfers_${fromDate}_to_${toDate}.xlsx`);
  };

  const toggleRow = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  const formatCurrency = (amount) => {
    const numericAmount = Number(amount);
    return isNaN(numericAmount)
      ? "LKR 0.00"
      : new Intl.NumberFormat("en-LK", {
          style: "currency",
          currency: "LKR",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(numericAmount);
  };

  if (!userBranchId) {
    // User doesn't have a branch assigned
    console.log("No branch ID available, showing no access screen");
    
    return (
      <div className="flex flex-col min-h-screen p-4 bg-transparent">
        <div className="py-3 mb-6 text-center text-white rounded-lg shadow-md bg-gradient-to-r from-blue-500 to-blue-800 dark:bg-gradient-to-r dark:from-blue-900 dark:to-slate-800">
          <h1 className="text-2xl font-bold">RECEIVE STOCK TRANSFERS</h1>
          <p className="text-sm opacity-90">Branch access required</p>
        </div>
        
        <div className="p-8 bg-white rounded-lg shadow-md dark:bg-gray-800">
          <div className="flex flex-col items-center text-center mb-6">
            <div className="w-20 h-20 flex items-center justify-center bg-red-100 rounded-full mb-4 dark:bg-red-900/30">
              <FiInfo className="w-10 h-10 text-red-500 dark:text-red-400" />
            </div>
            <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">No Branch Access</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
              You are not assigned to any branch. Stock transfers can only be received by users with branch assignments.
            </p>
            <div className="w-full max-w-md p-4 mt-2 bg-amber-50 border border-amber-200 rounded-lg dark:bg-amber-900/20 dark:border-amber-800">
              <p className="text-amber-800 dark:text-amber-400 text-sm">
                Please contact your administrator to get assigned to a branch. Once assigned, you'll be able to receive stock transfers for that branch.
              </p>
            </div>
          </div>
          
          {loading && (
            <div className="flex items-center justify-center p-6">
              <div className="w-6 h-6 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin mr-3"></div>
              <p className="text-gray-600 dark:text-gray-400">Checking branch assignments...</p>
            </div>
          )}
          
          <div className="mt-8">
            <details className="text-sm">
              <summary className="cursor-pointer text-blue-600 dark:text-blue-400 mb-2 inline-block">Technical Details</summary>
              <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 text-xs font-mono">
                <p><strong>User ID:</strong> {user?.id || user?.user_id || 'Not available'}</p>
                <p><strong>Name:</strong> {user?.name || 'Not available'}</p>
                <p><strong>Email:</strong> {user?.email || 'Not available'}</p>
                <p><strong>API Endpoints Tried:</strong></p>
                <ul className="list-disc pl-5 mt-1 mb-1">
                  <li>{API_BASE_URL}/branch-users/{user?.id || user?.user_id || "unknown"}</li>
                  <li>{API_BASE_URL}/users/{user?.id || user?.user_id || "unknown"}/branch</li>
                </ul>
                <p><strong>Branch Assignment:</strong> None found</p>
                <p className="mt-2 text-amber-600 dark:text-amber-400">If branch assignment is not being detected, please check that your user ID is correctly set up in the branch_user table.</p>
              </div>
            </details>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen p-4 bg-transparent">
      <div className="py-3 mb-6 text-center text-white rounded-lg shadow-md bg-gradient-to-r from-blue-500 to-blue-800 dark:bg-gradient-to-r dark:from-blue-900 dark:to-slate-800">
        <h1 className="text-2xl font-bold">RECEIVE STOCK TRANSFERS</h1>
        <p className="text-sm opacity-90">
          {branchName ? `${branchName} (${userBranchId})` : `Branch ID: ${userBranchId}`}
        </p>
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="relative flex-grow max-w-md">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <FiSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by transfer number, branch..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-gray-900 dark:border-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex flex-wrap items-center justify-end gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-slate-800 dark:border-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            <FiFilter /> {showFilters ? "Hide" : "Show"} Filters
          </button>
          <button
            onClick={fetchPendingTransfers}
            disabled={loading}
            title="Refresh Data"
            className={`flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <FiRefreshCw className={`${loading ? "animate-spin" : ""}`} /> Refresh
          </button>
          <button
            onClick={exportToExcel}
            disabled={filteredTransfers.length === 0 || loading}
            title="Export to Excel"
            className={`flex items-center gap-2 px-4 py-2 text-sm text-white bg-green-600 rounded-lg shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${filteredTransfers.length === 0 || loading ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <FaFileExcel /> Export
          </button>
        </div>
      </div>

      {showFilters && (
        <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-gray-600 dark:text-gray-300 animate-fade-in">
          <h3 className="flex items-center gap-2 mb-3 text-base font-medium">
            <FiFilter /> Date Filters
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <label htmlFor="fromDate" className="block mb-1 text-sm font-medium">
                From Date
              </label>
              <input
                id="fromDate"
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className="w-full p-2 bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="toDate" className="block mb-1 text-sm font-medium">
                To Date
              </label>
              <input
                id="toDate"
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                max={today}
                className="w-full p-2 bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={fetchPendingTransfers}
                disabled={loading}
                className={`w-full px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                {loading ? "Applying..." : "Apply Filters"}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 gap-4 mb-6 sm:grid-cols-2 lg:grid-cols-3">
        <div className="p-4 bg-white border-l-4 border-blue-500 rounded-lg shadow-md dark:bg-slate-800 dark:border-blue-400">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Pending Transfers
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {filteredTransfers.length}
          </p>
        </div>
        <div className="p-4 bg-white border-l-4 border-green-500 rounded-lg shadow-md dark:bg-slate-800 dark:border-green-400">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Items to Receive
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {filteredTransfers.reduce((sum, transfer) => sum + (transfer.total_quantity || 0), 0)}
          </p>
        </div>
        <div className="p-4 bg-white border-l-4 border-yellow-500 rounded-lg shadow-md dark:bg-slate-800 dark:border-yellow-400">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Value
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(filteredTransfers.reduce((sum, transfer) => sum + (transfer.total_value || 0), 0))}
          </p>
        </div>
      </div>

      <div className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-slate-700">
        {loading && transfers.length === 0 ? (
          <div className="flex items-center justify-center p-20">
            <div className="w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-600 dark:text-gray-400">Loading Transfer Data...</p>
          </div>
        ) :
          (
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
              <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
                <tr>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Transfer #</th>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">From Branch</th>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Date Sent</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Total Qty</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Total Value</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Days Pending</th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-600">
                {filteredTransfers.length === 0 && !loading ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-16 h-16 mb-4 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <p className="mb-2 text-lg font-medium text-gray-500 dark:text-gray-400">No Pending Transfers</p>
                        <p className="text-sm text-gray-400 dark:text-gray-500 max-w-sm">
                          There are no pending stock transfers for your branch at this time. 
                          Try changing the date range or check back later.
                        </p>
                        <button 
                          onClick={fetchPendingTransfers}
                          className="px-4 py-2 mt-4 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/40 transition-colors"
                        >
                          <FiRefreshCw className="inline mr-2" />
                          Refresh
                        </button>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredTransfers.map((transfer, index) => (
                    <React.Fragment key={transfer.transfer_id}>
                      <tr
                        className={`hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors ${
                          expandedRow === index ? "bg-blue-50 dark:bg-slate-700" : ""
                        }`}
                      >
                        <td className="px-4 py-3 font-medium text-blue-600 dark:text-blue-400 whitespace-nowrap">
                          <button onClick={() => toggleRow(index)} className="hover:underline focus:outline-none">
                            #{transfer.transfer_number}
                          </button>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="font-semibold text-gray-900 dark:text-gray-100">{transfer.from_branch_name}</div>
                        </td>
                        <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">
                          {new Date(transfer.transfer_date).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-3 font-semibold text-right text-gray-800 dark:text-white whitespace-nowrap">
                          {transfer.total_quantity}
                        </td>
                        <td className="px-4 py-3 font-semibold text-right text-gray-800 dark:text-white whitespace-nowrap">
                          {formatCurrency(transfer.total_value)}
                        </td>
                        <td className="px-4 py-3 text-right whitespace-nowrap">
                          <span className="inline-block px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full dark:bg-yellow-900 dark:text-yellow-200">
                            {transfer.daysSinceSent} days
                          </span>
                        </td>
                        <td className="px-4 py-3 text-right whitespace-nowrap">
                          <div className="flex items-center justify-end gap-x-3">
                            <button
                              onClick={() => handleReceiveTransfer(transfer.transfer_id, transfer.transfer_number)}
                              disabled={loading || processingId === transfer.transfer_id}
                              title="Receive Transfer"
                              className="flex items-center justify-center px-3 py-1 text-sm font-medium text-white bg-green-600 rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {processingId === transfer.transfer_id ? (
                                <div className="w-4 h-4 mr-2 border-2 border-t-transparent border-white rounded-full animate-spin"></div>
                              ) : (
                                <FiCheck className="mr-1" />
                              )}
                              Receive
                            </button>
                            <button
                              onClick={() => toggleRow(index)}
                              title={expandedRow === index ? "Collapse Details" : "Expand Details"}
                              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white focus:outline-none"
                            >
                              {expandedRow === index ? <FiChevronUp size={18} /> : <FiChevronDown size={18} />}
                            </button>
                          </div>
                        </td>
                      </tr>
                      {expandedRow === index && (
                        <tr className="bg-gray-50 dark:bg-slate-900/30">
                          <td colSpan={7} className="px-4 py-4 md:px-6">
                            <div>
                              <h4 className="mb-3 text-sm font-semibold text-gray-600 dark:text-gray-400">
                                Items to Receive ({transfer.items?.length || 0})
                              </h4>
                              {Array.isArray(transfer.items) && transfer.items.length > 0 ? (
                                <div className="overflow-x-auto">
                                  <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
                                    <thead className="bg-gray-100 dark:bg-slate-700">
                                      <tr>
                                        <th className="px-3 py-2 font-medium text-left">Item</th>
                                        <th className="px-3 py-2 font-medium text-center">Qty</th>
                                        <th className="px-3 py-2 font-medium text-right">Unit Price</th>
                                        <th className="px-3 py-2 font-medium text-right">Total</th>
                                      </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 dark:divide-slate-600">
                                      {transfer.items.map((item, i) => (
                                        <tr key={item.id || i}>
                                          <td className="px-3 py-2 font-medium text-gray-900 dark:text-white">{item.product_name}</td>
                                          <td className="px-3 py-2 text-center text-gray-600 dark:text-gray-300">{item.quantity}</td>
                                          <td className="px-3 py-2 text-right text-gray-600 dark:text-gray-300">{formatCurrency(item.unit_price)}</td>
                                          <td className="px-3 py-2 font-semibold text-right text-gray-900 dark:text-white">{formatCurrency(item.total)}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              ) : (
                                <p className="text-sm text-center text-gray-500 dark:text-gray-400">No item details available.</p>
                              )}
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockTransferReceive;
