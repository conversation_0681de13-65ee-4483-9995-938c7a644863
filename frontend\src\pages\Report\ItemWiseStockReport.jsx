import React, { useState, useEffect } from "react";
import axios from "axios";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input, Select } from "@/components/ui/input";
import { formatNumberWithCommas } from "../../utils/numberformat";
import { Table, Thead, Tbody, Tr, Th, Td } from "@/components/ui/table";
import { ExportToExcel, ExportToPDF } from "@/components/ui/export";

const ItemWiseStockReportForm = () => {
  const [filters, setFilters] = useState({
    barcode: "",
    itemName: "",
    category: "",
    supplier: "",
  });
  const [stockReports, setStockReports] = useState([]);
  const [categories, setCategories] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [showBatchDetails, setShowBatchDetails] = useState(true);
  const [products, setProducts] = useState([]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFilters({ ...filters, [name]: value });
    
    // Auto-generate report when barcode is scanned (if barcode has reasonable length)
    if (name === 'barcode' && value.length >= 8) {
      // Small delay to allow user to finish typing/scanning
      setTimeout(() => {
        fetchStockReports();
      }, 500);
    }
  };

  // Fetch categories, suppliers, and products from backend
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [categoriesResponse, suppliersResponse, productsResponse] =
          await Promise.all([
            axios.get("http://127.0.0.1:8000/api/categories"),
            axios.get("http://127.0.0.1:8000/api/suppliers"),
            axios.get("http://127.0.0.1:8000/api/products"),
          ]);
        setCategories(categoriesResponse.data);
        setSuppliers(suppliersResponse.data);

        // Process products to include variants
        const productsData =
          productsResponse.data.data || productsResponse.data;
        setProducts(productsData);
        console.log("Products with variants loaded:", productsData);
      } catch (error) {
        console.error("Error fetching initial data:", error);
        setError("Failed to fetch initial data.");
      }
    };

    fetchInitialData();
  }, []);

  const fetchStockReports = async () => {
    setLoading(true);
    setError(null);
    console.log("Fetching stock reports with filters:", filters);

    try {
      // Fetch regular stock reports
      const response = await axios.get(
        "http://127.0.0.1:8000/api/stock-reports",
        { params: filters }
      );

      console.log("Stock reports fetched:", response.data);

      if (response.data.error) {
        setError(response.data.error);
        return;
      }

      let enhancedReports = response.data;

      // If batch details are enabled, enhance reports with batch information
      if (showBatchDetails && products.length > 0) {
        enhancedReports = response.data.map((report) => {
          // Find the corresponding product with variants
          const product = products.find(
            (p) =>
              p.product_name === report.itemName ||
              p.product_id === report.itemCode ||
              p.product_id.toString() === report.itemCode
          );

          if (product && product.variants && product.variants.length > 0) {
            // Create batch information from variants with closing stock
            const batches = product.variants.map((variant) => {
              // Use the data from backend which now includes proper opening and closing stock
              const openingStock = parseFloat(variant.opening_stock_quantity || 0);
              const closingStock = parseFloat(variant.closing_stock_quantity || variant.opening_stock_quantity || 0);
              
              return {
                batchNumber: variant.batch_number || "N/A",
                expiryDate: variant.expiry_date || null,
                quantity: closingStock, // Current stock (closing stock)
                openingQuantity: openingStock,
                closingQuantity: closingStock,
                unitPrice: parseFloat(variant.sales_price || variant.mrp || 0),
                mrp: parseFloat(variant.mrp || 0),
                salesPrice: parseFloat(variant.sales_price || 0),
              };
            });

            // Calculate total stock quantity from all batches
            const totalBatchQuantity = batches.reduce(
              (sum, batch) => sum + batch.quantity,
              0
            );

            // Calculate total stock value from all batches
            const totalBatchValue = batches.reduce(
              (sum, batch) => sum + batch.quantity * batch.unitPrice,
              0
            );

            return {
              ...report,
              stockQuantity: totalBatchQuantity, // Override with sum of all batches
              stockValue: totalBatchValue, // Override with calculated value from batches
              originalStockQuantity: report.stockQuantity, // Keep original for reference
              originalStockValue: report.stockValue, // Keep original for reference
              batches: batches,
              hasBatches: true,
            };
          }

          return {
            ...report,
            batches: [],
            hasBatches: false,
          };
        });

        console.log("Enhanced reports with batch data:", enhancedReports);
      }

      setStockReports(enhancedReports);
      
      // Show success message if barcode search found results
      if (filters.barcode && enhancedReports.length > 0) {
        console.log(`Barcode search found ${enhancedReports.length} item(s)`);
        // Clear barcode field after successful search to prepare for next scan
        setTimeout(() => {
          setFilters(prev => ({ ...prev, barcode: '' }));
        }, 2000);
      }
    } catch (error) {
      console.error("Error fetching stock reports:", error);
      setError("Failed to fetch stock reports.");
    } finally {
      setLoading(false);
    }
  };

  // Toggle row expansion
  const toggleRowExpansion = (index) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(index)) {
      newExpandedRows.delete(index);
    } else {
      newExpandedRows.add(index);
    }
    setExpandedRows(newExpandedRows);
  };

  // Calculate totals and low stock items
  const {
    totalItems,
    totalStockQuantity,
    totalStockValue,
    lowStockItems,
    totalBatches,
  } = stockReports.reduce(
    (acc, report) => {
      const itemStockQuantity = report.stockQuantity || 0;
      const itemStockValue = report.stockValue || 0;
      const batchCount = report.batches ? report.batches.length : 0;

      return {
        totalItems: acc.totalItems + 1,
        totalStockQuantity: acc.totalStockQuantity + itemStockQuantity,
        totalStockValue: acc.totalStockValue + itemStockValue,
        totalBatches: acc.totalBatches + batchCount,
        lowStockItems:
          itemStockQuantity < 10 ? acc.lowStockItems + 1 : acc.lowStockItems,
      };
    },
    {
      totalItems: 0,
      totalStockQuantity: 0,
      totalStockValue: 0,
      totalBatches: 0,
      lowStockItems: 0,
    }
  );

  const handleViewDetails = (item) => {
    setSelectedItem(item);
    setShowDetails(true);
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="p-6 min-h-screen w-full relative bg-gray-50 dark:bg-gray-900">
      {/* Report Form */}
      <Card className="mb-6 shadow-lg dark:bg-gray-800">
        <CardContent>
          <h1 className="text-2xl font-bold text-gray-700 dark:text-gray-200 mb-4">
            Item Wise Stock Report
          </h1>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <Input
                type="text"
                name="barcode"
                placeholder="Scan Barcode (shows item details)"
                value={filters.barcode}
                onChange={handleInputChange}
                className="dark:bg-gray-700 dark:text-gray-200"
                autoFocus
              />
              <p className="text-xs text-gray-500 mt-1">Scan barcode to find item details</p>
            </div>
            <div>
            <Input
              type="text"
              name="itemName"
              placeholder="Item Name"
              value={filters.itemName}
              onChange={handleInputChange}
              className="dark:bg-gray-700 dark:text-gray-200"
            />
            </div>
            <div>
            <Select
              name="category"
              value={filters.category}
              onChange={handleInputChange}
              className="dark:bg-gray-700 dark:text-gray-200"
            >
              <option value="">Select Category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </Select>

            </div>
            <div>
            <Select
              name="supplier"
              value={filters.supplier}
              onChange={handleInputChange}
              className="dark:bg-gray-700 dark:text-gray-200"
            >
              <option value="">Select Supplier</option>
              {suppliers.map((supplier) => (
                <option key={supplier.id} value={supplier.supplier_name}>
                  {supplier.supplier_name}
                </option>
              ))}
            </Select>

            </div>
            
          </div>
          {/* Batch Details Toggle */}
          <div className="mt-4 flex items-center justify-between">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showBatchDetails}
                onChange={(e) => setShowBatchDetails(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Include Batch-wise Details
              </span>
            </label>

            {/* Debug Info */}
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Products loaded: {products.length} | Total Variants:{" "}
              {
                products.reduce((total, p) => total + (p.variants ? p.variants.length : 0), 0)
              }
            </div>
          </div>

          {/* Export and Print Options */}
          <div className="mt-4 flex justify-end space-x-2">
            <Button
              onClick={fetchStockReports}
              className="w-full md:w-auto bg-blue-600 hover:bg-blue-700 text-white"
              disabled={loading}
            >
              {loading ? "Generating Report..." : "Generate Report"}
            </Button>
            {error && <p className="text-red-500 mt-2">{error}</p>}
            <ExportToExcel data={stockReports} fileName="StockReport" />
            <ExportToPDF data={stockReports} fileName="StockReport" />
            <Button
              onClick={handlePrint}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Print Report
            </Button>

            {/* Debug Button */}
            <Button
              onClick={() => {
                console.log("Current products:", products);
                console.log("Current stock reports:", stockReports);
                console.log("Show batch details:", showBatchDetails);
              }}
              className="bg-yellow-600 hover:bg-yellow-700 text-white text-xs"
            >
              Debug
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Table Container */}
      <div className="relative overflow-hidden border border-gray-300 rounded-lg shadow-md bg-white dark:bg-gray-800 mt-6">
        <div className="max-h-[60vh] overflow-auto">
          <Table className="w-full">
            <Thead className="sticky top-0 bg-blue-500 text-white border radius-b-none">
              <Tr>
                {showBatchDetails && (
                  <Th className="px-4 py-2 text-center w-12">Expand</Th>
                )}
                <Th className="px-4 py-2 text-left">Item Code</Th>
                <Th className="px-4 py-2 text-left">Barcode</Th>
                <Th className="px-4 py-2 text-left">Item Name</Th>
                <Th className="px-4 py-2 text-left">Category</Th>
                <Th className="px-4 py-2 text-left">Supplier</Th>
                <Th className="px-4 py-2 text-right">Stock Quantity (Current/Opening)</Th>
                <Th className="px-4 py-2 text-right">Stock Value</Th>
                {showBatchDetails && (
                  <Th className="px-4 py-2 text-center">Batches</Th>
                )}
                <Th className="px-4 py-2 text-right">Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {stockReports.length > 0 ? (
                stockReports.map((report, index) => (
                  <React.Fragment key={index}>
                    <Tr
                      className={`border text-gray-700 dark:text-gray-200 odd:bg-gray-100 dark:odd:bg-gray-700 ${
                        report.stockQuantity < 10
                          ? "text-red-800 dark:text-red-400 animate-blink"
                          : ""
                      }`}
                    >
                      {showBatchDetails && (
                        <Td className="px-4 py-2 text-center">
                          {report.batches && report.batches.length > 0 ? (
                            <Button
                              onClick={() => toggleRowExpansion(index)}
                              className="bg-gray-500 hover:bg-gray-600 text-white text-xs px-2 py-1"
                            >
                              {expandedRows.has(index) ? "−" : "+"}
                            </Button>
                          ) : (
                            <span className="text-xs text-gray-400">
                              No batches
                            </span>
                          )}
                        </Td>
                      )}
                      <Td className="px-4 py-2 text-left">{report.itemCode}</Td>
                      <Td className="px-4 py-2 text-left">
                        {report.barcodes && report.barcodes.length > 0 
                          ? report.barcodes.join(', ') 
                          : 'N/A'}
                      </Td>
                      <Td className="px-4 py-2 text-left">{report.itemName}</Td>
                      <Td className="px-4 py-2 text-left">{report.category}</Td>
                      <Td className="px-4 py-2 text-left">{report.supplier}</Td>
                      <Td className="px-4 py-2 text-right">
                        <div className="flex flex-col items-end">
                          <span className="font-semibold">{report.stockQuantity}</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            Opening: {report.batches && report.batches.length > 0 
                              ? report.batches.reduce((sum, batch) => sum + (batch.openingQuantity || 0), 0)
                              : report.stockQuantity}
                          </span>
                          {showBatchDetails && report.hasBatches && (
                            <span className="text-xs text-blue-600 dark:text-blue-400">
                              (Sum of {report.batches.length} batches)
                            </span>
                          )}
                        </div>
                      </Td>
                      <Td className="px-4 py-2 text-right">
                        <div className="flex flex-col items-end">
                          <span>
                            Rs.{" "}
                            {formatNumberWithCommas(
                              report.stockValue.toFixed(2)
                            )}
                          </span>
                          {showBatchDetails && report.hasBatches && (
                            <span className="text-xs text-blue-600 dark:text-blue-400">
                              (Calculated from batches)
                            </span>
                          )}
                        </div>
                      </Td>
                      {showBatchDetails && (
                        <Td className="px-4 py-2 text-center">
                          {report.batches ? report.batches.length : 0}
                        </Td>
                      )}
                      <Td className="px-4 py-2 text-right">
                        <Button
                          onClick={() => handleViewDetails(report)}
                          className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1"
                        >
                          View Details
                        </Button>
                      </Td>
                    </Tr>

                    {/* Expanded batch details row */}
                    {showBatchDetails &&
                      expandedRows.has(index) &&
                      report.batches &&
                      report.batches.length > 0 && (
                        <Tr>
                          <Td
                            colSpan={showBatchDetails ? "9" : "7"}
                            className="px-4 py-2 bg-gray-50 dark:bg-gray-700"
                          >
                            <div className="pl-8">
                              <h4 className="font-semibold mb-2 text-gray-800 dark:text-gray-200">
                                Batch Details for {report.itemName}
                              </h4>
                              <div className="overflow-x-auto">
                                <table className="w-full text-sm">
                                                                     <thead>
                                     <tr className="bg-gray-200 dark:bg-gray-600">
                                       <th className="px-3 py-2 text-left">
                                         Batch Number
                                       </th>
                                       <th className="px-3 py-2 text-left">
                                         Expiry Date
                                       </th>
                                       <th className="px-3 py-2 text-right">
                                         Opening Stock
                                       </th>
                                       <th className="px-3 py-2 text-right">
                                         Current Stock
                                       </th>
                                       <th className="px-3 py-2 text-right">
                                         Unit Price
                                       </th>
                                       <th className="px-3 py-2 text-right">
                                         Total Value
                                       </th>
                                       <th className="px-3 py-2 text-center">
                                         Status
                                       </th>
                                     </tr>
                                   </thead>
                                  <tbody>
                                    {report.batches.map((batch, batchIndex) => (
                                      <tr
                                        key={batchIndex}
                                        className="border-b border-gray-200 dark:border-gray-600"
                                      >
                                        <td className="px-3 py-2">
                                          {batch.batchNumber || "N/A"}
                                        </td>
                                        <td className="px-3 py-2">
                                          {batch.expiryDate
                                            ? new Date(
                                                batch.expiryDate
                                              ).toLocaleDateString()
                                            : "N/A"}
                                        </td>
                                        <td className="px-3 py-2 text-right">
                                          {batch.openingQuantity || 0}
                                        </td>
                                        <td className="px-3 py-2 text-right">
                                          {batch.quantity || 0}
                                        </td>
                                        <td className="px-3 py-2 text-right">
                                          Rs.{" "}
                                          {formatNumberWithCommas(
                                            (batch.unitPrice || 0).toFixed(2)
                                          )}
                                        </td>
                                        <td className="px-3 py-2 text-right">
                                          Rs.{" "}
                                          {formatNumberWithCommas(
                                            (
                                              (batch.quantity || 0) *
                                              (batch.unitPrice || 0)
                                            ).toFixed(2)
                                          )}
                                        </td>
                                        <td className="px-3 py-2 text-center">
                                          <span
                                            className={`px-2 py-1 rounded text-xs ${
                                              batch.quantity > 0
                                                ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                                                : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                                            }`}
                                          >
                                            {batch.quantity > 0
                                              ? "In Stock"
                                              : "Out of Stock"}
                                          </span>
                                        </td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>

                                {/* Batch Summary */}
                                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
                                  <h5 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                                    Batch Summary
                                  </h5>
                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                      <span className="text-blue-700 dark:text-blue-300">
                                        Total Quantity:
                                      </span>
                                      <span className="font-semibold ml-2">
                                        {report.batches.reduce(
                                          (sum, batch) => sum + batch.quantity,
                                          0
                                        )}
                                      </span>
                                    </div>
                                    <div>
                                      <span className="text-blue-700 dark:text-blue-300">
                                        Total Value:
                                      </span>
                                      <span className="font-semibold ml-2">
                                        Rs.{" "}
                                        {formatNumberWithCommas(
                                          report.batches
                                            .reduce(
                                              (sum, batch) =>
                                                sum +
                                                batch.quantity *
                                                  batch.unitPrice,
                                              0
                                            )
                                            .toFixed(2)
                                        )}
                                      </span>
                                    </div>
                                    <div>
                                      <span className="text-blue-700 dark:text-blue-300">
                                        Active Batches:
                                      </span>
                                      <span className="font-semibold ml-2">
                                        {
                                          report.batches.filter(
                                            (batch) => batch.quantity > 0
                                          ).length
                                        }{" "}
                                        / {report.batches.length}
                                      </span>
                                    </div>
                                    <div>
                                      <span className="text-blue-700 dark:text-blue-300">
                                        Average Price:
                                      </span>
                                      <span className="font-semibold ml-2">
                                        Rs.{" "}
                                        {formatNumberWithCommas(
                                          (
                                            report.batches.reduce(
                                              (sum, batch) =>
                                                sum + batch.unitPrice,
                                              0
                                            ) / report.batches.length
                                          ).toFixed(2)
                                        )}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Td>
                        </Tr>
                      )}
                  </React.Fragment>
                ))
              ) : (
                <Tr>
                  <Td
                    colSpan={showBatchDetails ? "9" : "7"}
                    className="text-center py-4 text-gray-500 dark:text-gray-400"
                  >
                    No data available
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </div>
      </div>

      {/* Summary Section at the Bottom */}
      <div className="mt-6 bg-transparent rounded-lg shadow-lg text-center p-4">
        <h2 className="text-xl font-bold mb-4 dark:text-gray-200">
          Item Wise Stock Summary
        </h2>
        <div
          className={`grid grid-cols-1 ${showBatchDetails ? "md:grid-cols-5" : "md:grid-cols-4"} gap-4`}
        >
          <div className="bg-cyan-800 p-4 rounded-lg">
            <p className="text-sm text-cyan-500">Total Items</p>
            <p className="text-2xl text-cyan-300 font-bold">{totalItems}</p>
          </div>
          <div className="bg-rose-800 p-4 rounded-lg">
            <p className="text-sm text-pink-500">Total Stock Quantity</p>
            <p className="text-2xl text-pink-300 font-bold">
              {totalStockQuantity}
            </p>
          </div>
          <div className="bg-lime-800 p-4 rounded-lg">
            <p className="text-sm text-lime-500">Total Stock Value</p>
            <p className="text-2xl text-lime-300 font-bold">
              Rs. {formatNumberWithCommas(totalStockValue.toFixed(2))}
            </p>
          </div>
          {showBatchDetails && (
            <div className="bg-purple-800 p-4 rounded-lg">
              <p className="text-sm text-purple-500">Total Batches</p>
              <p className="text-2xl text-purple-300 font-bold">
                {totalBatches}
              </p>
            </div>
          )}
          <div className="bg-fuchsia-800 p-4 rounded-lg">
            <p className="text-sm text-fuchsia-500">Low Stock Items</p>
            <p className="text-2xl text-fuchsia-300 font-bold">
              {lowStockItems}
            </p>
          </div>
        </div>
      </div>

      {/* Detail View Popup */}
      {showDetails && selectedItem && (
        <div className="fixed inset-0 z-20 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-11/12 max-w-4xl relative max-h-[80vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4 dark:text-gray-200">
              Item Details - {selectedItem.itemName}
            </h2>

            {/* Basic Item Information */}
            <div className="space-y-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 border-b pb-2">
                Basic Information
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Item Code
                  </label>
                  <p className="mt-1 text-lg font-semibold dark:text-gray-200">
                    {selectedItem.itemCode}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Item Name
                  </label>
                  <p className="mt-1 text-lg font-semibold dark:text-gray-200">
                    {selectedItem.itemName}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Category
                  </label>
                  <p className="mt-1 text-lg font-semibold dark:text-gray-200">
                    {selectedItem.category}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Supplier
                  </label>
                  <p className="mt-1 text-lg font-semibold dark:text-gray-200">
                    {selectedItem.supplier}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Current Stock Quantity
                  </label>
                  <p className="mt-1 text-lg font-semibold dark:text-gray-200">
                    {selectedItem.stockQuantity}
                    {selectedItem.hasBatches && (
                      <span className="text-sm text-blue-600 dark:text-blue-400 block">
                        (Sum of {selectedItem.batches?.length || 0} batches)
                      </span>
                    )}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Opening Stock Quantity
                  </label>
                  <p className="mt-1 text-lg font-semibold dark:text-gray-200">
                    {selectedItem.batches && selectedItem.batches.length > 0 
                      ? selectedItem.batches.reduce((sum, batch) => sum + (batch.openingQuantity || 0), 0)
                      : selectedItem.stockQuantity}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Stock Value
                  </label>
                  <p className="mt-1 text-lg font-semibold dark:text-gray-200">
                    Rs.{" "}
                    {formatNumberWithCommas(selectedItem.stockValue.toFixed(2))}
                    {selectedItem.hasBatches && (
                      <span className="text-sm text-blue-600 dark:text-blue-400 block">
                        (Calculated from batches)
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Batch Details Section */}
            {showBatchDetails &&
              selectedItem.hasBatches &&
              selectedItem.batches &&
              selectedItem.batches.length > 0 && (
                <div className="space-y-4 mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 border-b pb-2">
                    Batch Details ({selectedItem.batches.length} batches)
                  </h3>

                  {/* Batch Summary Cards */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg">
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Total Quantity
                      </p>
                      <p className="text-xl font-bold text-blue-800 dark:text-blue-200">
                        {selectedItem.batches.reduce(
                          (sum, batch) => sum + batch.quantity,
                          0
                        )}
                      </p>
                    </div>
                    <div className="bg-green-100 dark:bg-green-900 p-3 rounded-lg">
                      <p className="text-sm text-green-700 dark:text-green-300">
                        Total Value
                      </p>
                      <p className="text-xl font-bold text-green-800 dark:text-green-200">
                        Rs.{" "}
                        {formatNumberWithCommas(
                          selectedItem.batches
                            .reduce(
                              (sum, batch) =>
                                sum + batch.quantity * batch.unitPrice,
                              0
                            )
                            .toFixed(2)
                        )}
                      </p>
                    </div>
                    <div className="bg-purple-100 dark:bg-purple-900 p-3 rounded-lg">
                      <p className="text-sm text-purple-700 dark:text-purple-300">
                        Active Batches
                      </p>
                      <p className="text-xl font-bold text-purple-800 dark:text-purple-200">
                        {
                          selectedItem.batches.filter(
                            (batch) => batch.quantity > 0
                          ).length
                        }{" "}
                        / {selectedItem.batches.length}
                      </p>
                    </div>
                    <div className="bg-orange-100 dark:bg-orange-900 p-3 rounded-lg">
                      <p className="text-sm text-orange-700 dark:text-orange-300">
                        Avg. Price
                      </p>
                      <p className="text-xl font-bold text-orange-800 dark:text-orange-200">
                        Rs.{" "}
                        {formatNumberWithCommas(
                          (
                            selectedItem.batches.reduce(
                              (sum, batch) => sum + batch.unitPrice,
                              0
                            ) / selectedItem.batches.length
                          ).toFixed(2)
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Batch Details Table */}
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm border border-gray-200 dark:border-gray-600">
                      <thead className="bg-gray-100 dark:bg-gray-700">
                        <tr>
                          <th className="px-3 py-2 text-left border-b">
                            Batch Number
                          </th>
                          <th className="px-3 py-2 text-left border-b">
                            Expiry Date
                          </th>
                          <th className="px-3 py-2 text-right border-b">
                            Opening Stock
                          </th>
                          <th className="px-3 py-2 text-right border-b">
                            Current Stock
                          </th>
                          <th className="px-3 py-2 text-right border-b">
                            Unit Price
                          </th>
                          <th className="px-3 py-2 text-right border-b">
                            Total Value
                          </th>
                          <th className="px-3 py-2 text-center border-b">
                            Status
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedItem.batches.map((batch, index) => (
                          <tr
                            key={index}
                            className="border-b border-gray-200 dark:border-gray-600"
                          >
                            <td className="px-3 py-2">
                              {batch.batchNumber || "N/A"}
                            </td>
                            <td className="px-3 py-2">
                              {batch.expiryDate
                                ? new Date(
                                    batch.expiryDate
                                  ).toLocaleDateString()
                                : "N/A"}
                            </td>
                            <td className="px-3 py-2 text-right">
                              {batch.openingQuantity || 0}
                            </td>
                            <td className="px-3 py-2 text-right">
                              {batch.quantity || 0}
                            </td>
                            <td className="px-3 py-2 text-right">
                              Rs.{" "}
                              {formatNumberWithCommas(
                                (batch.unitPrice || 0).toFixed(2)
                              )}
                            </td>
                            <td className="px-3 py-2 text-right">
                              Rs.{" "}
                              {formatNumberWithCommas(
                                (
                                  (batch.quantity || 0) * (batch.unitPrice || 0)
                                ).toFixed(2)
                              )}
                            </td>
                            <td className="px-3 py-2 text-center">
                              <span
                                className={`px-2 py-1 rounded text-xs ${
                                  batch.quantity > 0
                                    ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                                    : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                                }`}
                              >
                                {batch.quantity > 0
                                  ? "In Stock"
                                  : "Out of Stock"}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

            <Button
              onClick={() => setShowDetails(false)}
              className="mt-6 w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              Close
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ItemWiseStockReportForm;
