import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "../../context/NewAuthContext";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import * as XLSX from "xlsx";
import { jsPDF } from "jspdf";
import "jspdf-autotable";
import { Book, FileText } from "lucide-react";
import {
  FiSearch,
  FiChevronDown,
  FiChevronUp,
  FiPrinter,
} from "react-icons/fi";
import { FaFileExcel, FaFilePdf } from "react-icons/fa";

const API_BASE_URL = "http://127.0.0.1:8000/api";

// Configure axios with default timeout
// axios.defaults.timeout = 10000; // Disabled to allow unlimited wait for API responses

const BillDetailsTable = ({ items }) => {
  return (
    <div className="overflow-x-auto mt-4">
      <table className="min-w-full bg-white dark:bg-gray-800 text-black dark:text-gray-300">
        <thead className="bg-gray-100 dark:bg-gray-700">
          <tr>
            <th className="px-4 py-2 text-left">Product Name</th>
            <th className="px-4 py-2 text-center">Quantity</th>
            <th className="px-4 py-2 text-right">Unit Price</th>
            <th className="px-4 py-2 text-right">Discount</th>
            <th className="px-4 py-2 text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, index) => (
            <tr key={index} className="border-b dark:border-gray-600">
              <td className="px-4 py-2">
                {item.product_name || item.description || "Unknown Product"}
              </td>
              <td className="px-4 py-2 text-center">
                {item.quantity || item.qty || 0}
              </td>
              <td className="px-4 py-2 text-right">
                {formatCurrency(item.unit_price || item.sales_price || 0)}
              </td>
              <td className="px-4 py-2 text-right">
                {formatCurrency(item.discount || item.discount_amount || 0)}
              </td>
              <td className="px-4 py-2 text-right">
                {formatCurrency(item.total || 0)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const BillCollectionTable = ({ data, columns, renderCell }) => {
  const [expandedRows, setExpandedRows] = useState({});

  const toggleExpand = (billNo) => {
    setExpandedRows((prev) => ({
      ...prev,
      [billNo]: !prev[billNo],
    }));
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white dark:bg-gray-800 text-black dark:text-gray-300">
        <thead className="bg-gray-100 dark:bg-gray-700">
          <tr>
            {columns.map((column, index) => (
              <th key={index} className="px-4 py-2 text-left">
                {column}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, rowIndex) => (
            <React.Fragment key={`${row.type}-${row.id}`}>
              <tr className="border-b dark:border-gray-600">
                {columns.map((column, colIndex) => (
                  <td key={colIndex} className="px-4 py-2">
                    {renderCell(
                      row,
                      column,
                      () => toggleExpand(row.bill_number),
                      expandedRows[row.bill_number],
                      rowIndex
                    )}
                  </td>
                ))}
              </tr>
              {expandedRows[row.bill_number] && (
                <tr>
                  <td colSpan={columns.length}>
                    <BillDetailsTable items={row.items} />
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const formatCurrency = (amount) => {
  const numericAmount = Number(amount);
  if (isNaN(numericAmount)) {
    return "LKR 0.00";
  }
  return new Intl.NumberFormat("en-LK", {
    style: "currency",
    currency: "LKR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numericAmount);
};

const calculateDaysOutstanding = (invoiceDate) => {
  try {
    const invoice = new Date(invoiceDate);
    const today = new Date();
    // Ensure dates are valid
    if (isNaN(invoice.getTime())) {
      return "N/A";
    }
    // Calculate difference in milliseconds and convert to days
    const diffTime = today - invoice;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return diffDays >= 0 ? diffDays : "N/A";
  } catch (error) {
    console.warn("Error calculating days outstanding:", error);
    return "N/A";
  }
};

const BillByBillCollection = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const today = new Date().toISOString().split("T")[0];
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const lastMonth = oneMonthAgo.toISOString().split("T")[0];

  const [fromDate, setFromDate] = useState(lastMonth);
  const [toDate, setToDate] = useState(today);
  const [searchQuery, setSearchQuery] = useState("");
  const [reportData, setReportData] = useState([]);
  const [summary, setSummary] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Redirect unauthorized users
  useEffect(() => {
    if (!user || !["admin", "manager"].includes(user.role)) {
      navigate("/unauthorized");
    }
  }, [user, navigate]);

  const fetchReportData = useCallback(async () => {
    setIsLoading(true);
    try {
      const invoiceResponse = await axios.get(`${API_BASE_URL}/invoices`, {
        params: { from: fromDate, to: toDate, payment_method: "credit" },
        headers: { Accept: "application/json" },
      });

      const salesResponse = await axios.get(`${API_BASE_URL}/sales`, {
        params: { from: fromDate, to: toDate, payment_method: "credit" },
        headers: { Accept: "application/json" },
      });

      let invoices = [];
      if (invoiceResponse.data && Array.isArray(invoiceResponse.data.data)) {
        invoices = invoiceResponse.data.data;
      } else if (Array.isArray(invoiceResponse.data)) {
        invoices = invoiceResponse.data;
      } else {
        console.warn(
          "Received invoice data is not in expected format:",
          invoiceResponse.data
        );
        throw new Error("Invalid invoice data format from API");
      }

      const processedInvoices = invoices.map((invoice) => ({
        ...invoice,
        type: "invoice",
        bill_number: invoice.invoice_no || `INV-${invoice.id}`,
        customer_name: invoice.customer_name || "Unknown Customer",
        customer_phone: invoice.customer_phone || "",
        items: Array.isArray(invoice.items) ? invoice.items : [],
        total_amount: parseFloat(invoice.total_amount) || 0,
        purchase_amount: parseFloat(invoice.purchase_amount) || 0,
        balance: parseFloat(invoice.balance) || 0,
        invoice_date:
          invoice.invoice_date ||
          invoice.created_at ||
          new Date().toISOString(),
        payment_method: invoice.payment_method || "Credit",
        days_outstanding: calculateDaysOutstanding(
          invoice.invoice_date || invoice.created_at
        ),
      }));

      let sales = [];
      if (salesResponse.data && Array.isArray(salesResponse.data.data)) {
        sales = salesResponse.data.data;
      } else if (Array.isArray(salesResponse.data)) {
        sales = salesResponse.data;
      } else {
        console.warn(
          "Received sales data is not in expected format:",
          salesResponse.data
        );
        throw new Error("Invalid sales data format from API");
      }

      const processedSales = sales.map((sale) => ({
        ...sale,
        type: "sale",
        bill_number: sale.bill_number || `SALE-${sale.id}`,
        customer_name: sale.customer_name || "Walk-in Customer",
        customer_phone: sale.customer_phone || "",
        items: Array.isArray(sale.items)
          ? sale.items.map((item) => ({
              ...item,
              product_name: item.product_name || "Unknown Product",
              quantity: item.quantity || 0,
              unit_price: parseFloat(item.unit_price) || 0,
              discount: parseFloat(item.discount) || 0,
              total: parseFloat(item.total) || 0,
            }))
          : [],
        total_amount: parseFloat(sale.total) || 0,
        purchase_amount: parseFloat(sale.received_amount) || 0,
        balance: parseFloat(sale.balance_amount) || 0,
        invoice_date: sale.created_at || new Date().toISOString(),
        payment_method: sale.payment_type || "Credit",
        days_outstanding: calculateDaysOutstanding(sale.created_at),
      }));

      const combinedData = [...processedInvoices, ...processedSales]
        .filter((row) => row.payment_method.toLowerCase().includes("credit"))
        .sort((a, b) => new Date(b.invoice_date) - new Date(a.invoice_date));

      setReportData(combinedData);

      const summaryData = {
        totalAmount: combinedData.reduce(
          (sum, row) => sum + (parseFloat(row.total_amount) || 0),
          0
        ),
        totalPaid: combinedData.reduce(
          (sum, row) => sum + (parseFloat(row.purchase_amount) || 0),
          0
        ),
        totalOutstanding: combinedData.reduce(
          (sum, row) => sum + (parseFloat(row.balance) || 0),
          0
        ),
        totalEntries: combinedData.length,
      };
      setSummary(summaryData);
      setError("");
    } catch (error) {
      console.error(
        "Error fetching bill collection data:",
        error.response || error
      );
      setError(
        `Error fetching data: ${error.response?.data?.message || error.message}`
      );
      setReportData([]);
    } finally {
      setIsLoading(false);
    }
  }, [fromDate, toDate]);

  useEffect(() => {
    fetchReportData();
  }, [fetchReportData]);

  const filteredData = reportData.filter((row) => {
    const searchableFields = [
      row.bill_number,
      row.customer_name,
      row.customer_phone,
      row.total_amount?.toString(),
      row.payment_method,
      new Date(row.invoice_date).toLocaleDateString(),
      row.days_outstanding.toString(),
    ];
    return searchableFields.some(
      (value) =>
        value &&
        value.toString().toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const flattenDataForExport = () => {
    return filteredData.flatMap((row) => [
      {
        "Invoice #": row.bill_number,
        Type: row.type === "invoice" ? "Invoice" : "Sale",
        Customer: row.customer_name,
        "Invoice Date": new Date(row.invoice_date).toLocaleDateString(),
        Amount: row.total_amount,
        Paid: row.purchase_amount,
        Outstanding: Math.abs(row.balance), // Use absolute value for export
        "Days Outstanding": row.days_outstanding,
        "Payment Method": row.payment_method,
      },
      ...row.items.map((item) => ({
        "Item Name": item.product_name || item.description || "Unknown Product",
        Quantity: item.quantity || item.qty || 0,
        "Unit Price": item.unit_price || item.sales_price || 0,
        Discount: item.discount || item.discount_amount || 0,
        Total: item.total || 0,
      })),
    ]);
  };

  const exportToExcel = () => {
    const flatData = flattenDataForExport();
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(flatData);
    XLSX.utils.book_append_sheet(wb, ws, "BillByBillCollection");
    XLSX.writeFile(wb, `Bill_By_Bill_Collection_${fromDate}_to_${toDate}.xlsx`);
  };

  const exportToPDF = () => {
    const flatData = flattenDataForExport();
    const doc = new jsPDF();
    doc.setFontSize(18);
    doc.text("Bill by Bill Collection Report", 10, 10);
    const columns = [
      "Invoice #",
      "Type",
      "Customer",
      "Invoice Date",
      "Amount",
      "Paid",
      "Outstanding",
      "Days Outstanding",
      "Payment Method",
    ];
    const rows = flatData.map((row) => [
      row["Invoice #"],
      row.Type,
      row.Customer,
      row["Invoice Date"],
      row.Amount,
      row.Paid,
      row.Outstanding,
      row["Days Outstanding"],
      row["Payment Method"],
    ]);
    doc.autoTable({
      head: [columns],
      body: rows,
      startY: 20,
    });
    doc.save(`Bill_By_Bill_Collection_${fromDate}_to_${toDate}.pdf`);
  };

  return (
    <div className="p-6 min-h-screen bg-white dark:bg-gray-800 dark:text-gray-300">
      <div className="flex items-center mb-6">
        <Book size={28} className="mr-2 text-blue-700 dark:text-amber-600" />
        <h1 className="text-2xl font-bold">Bill by Bill Collection</h1>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <label className="flex flex-col">
          <span className="font-medium mb-1">From Date</span>
          <input
            type="date"
            value={fromDate}
            onChange={(e) => setFromDate(e.target.value)}
            className="border border-gray-300 dark:border-gray-600 rounded p-2 bg-white dark:bg-gray-900 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </label>
        <label className="flex flex-col">
          <span className="font-medium mb-1">To Date</span>
          <input
            type="date"
            value={toDate}
            onChange={(e) => setToDate(e.target.value)}
            max={today}
            className="border border-gray-300 dark:border-gray-600 rounded p-2 bg-white dark:bg-gray-900 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </label>
        <label className="flex flex-col">
          <span className="font-medium mb-1">Search</span>
          <div className="relative">
            <FiSearch className="absolute top-3 left-3 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search by invoice #, customer, days..."
              className="pl-10 border border-gray-300 dark:border-gray-600 rounded p-2 w-full bg-white dark:bg-gray-900 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </label>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4 mb-6">
        <button
          onClick={exportToExcel}
          disabled={filteredData.length === 0 || isLoading}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white ${
            filteredData.length === 0 || isLoading
              ? "bg-green-300 cursor-not-allowed"
              : "bg-green-500 hover:bg-green-600"
          }`}
        >
          <FaFileExcel /> Export to Excel
        </button>
        <button
          onClick={exportToPDF}
          disabled={filteredData.length === 0 || isLoading}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white ${
            filteredData.length === 0 || isLoading
              ? "bg-red-300 cursor-not-allowed"
              : "bg-red-500 hover:bg-red-600"
          }`}
        >
          <FaFilePdf /> Export to PDF
        </button>
        <button
          onClick={() => window.print()}
          disabled={filteredData.length === 0 || isLoading}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white ${
            filteredData.length === 0 || isLoading
              ? "bg-blue-300 cursor-not-allowed"
              : "bg-blue-500 hover:bg-blue-600"
          }`}
        >
          <FiPrinter /> Print
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="p-4 bg-white dark:bg-gray-700 border-l-4 border-blue-500 rounded-lg shadow-md">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Entries
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {summary.totalEntries || 0}
          </p>
        </div>
        <div className="p-4 bg-white dark:bg-gray-700 border-l-4 border-green-500 rounded-lg shadow-md">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Amount
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(summary.totalAmount || 0)}
          </p>
        </div>
        <div className="p-4 bg-white dark:bg-gray-700 border-l-4 border-yellow-500 rounded-lg shadow-md">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Paid
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(summary.totalPaid || 0)}
          </p>
        </div>
        <div className="p-4 bg-white dark:bg-gray-700 border-l-4 border-red-500 rounded-lg shadow-md">
          <h3 className="mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Outstanding
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(Math.abs(summary.totalOutstanding) || 0)}
          </p>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2">Loading collection data...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="text-center py-4 text-red-600 dark:text-red-400">
          {error}
        </div>
      )}

      {/* Report Table */}
      {!isLoading && !error && (
        <BillCollectionTable
          data={filteredData}
          columns={[
            "No.",
            "Invoice #",
            "Customer",
            "Invoice Date",
            "Amount",
            "Paid",
            "Outstanding",
            "Days Outstanding",
            "Payment Method",
            "Items",
          ]}
          renderCell={(row, column, toggleExpand, isExpanded, rowIndex) => {
            if (column === "No.") {
              return rowIndex + 1;
            }
            if (column === "Invoice #") {
              return row.bill_number;
            }
            if (column === "Customer") {
              return (
                <div>
                  <div className="font-semibold">{row.customer_name}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {row.customer_phone}
                  </div>
                </div>
              );
            }
            if (column === "Invoice Date") {
              return new Date(row.invoice_date).toLocaleDateString();
            }
            if (column === "Amount") {
              return formatCurrency(row.total_amount);
            }
            if (column === "Paid") {
              return formatCurrency(row.purchase_amount);
            }
            if (column === "Outstanding") {
              return (
                <span
                  className={
                    row.balance > 0
                      ? "text-red-600 dark:text-red-400"
                      : "text-green-600 dark:text-green-400"
                  }
                >
                  {formatCurrency(Math.abs(row.balance))}
                </span>
              );
            }
            if (column === "Days Outstanding") {
              return row.days_outstanding === "N/A"
                ? "N/A"
                : `${row.days_outstanding} day${row.days_outstanding !== 1 ? "s" : ""}`;
            }
            if (column === "Payment Method") {
              return (
                <span className="capitalize">
                  {row.payment_method || "Credit"}
                </span>
              );
            }
            if (column === "Items") {
              return (
                <button
                  onClick={toggleExpand}
                  className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  {isExpanded ? "▲ Hide" : "▼ Show"} Items
                </button>
              );
            }
          }}
        />
      )}
    </div>
  );
};

export default BillByBillCollection;
