<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Quotation;
use App\Models\StaffLedger;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BrokerReportController extends Controller
{
    public function index(Request $request)
    {
        try {
            $fromDate = $request->get('from_date');
            $toDate = $request->get('to_date');
            $brokerName = $request->get('broker_name');

            // Get all broker names from staff_ledger table
            $brokers = StaffLedger::where('account_group', 'broker')
                ->select('id', 'name', 'staff_id', 'mobile_no', 'whatsapp_no')
                ->orderBy('name')
                ->get();

            $reportData = [];

            foreach ($brokers as $broker) {
                // Skip if specific broker filter is applied and doesn't match
                if ($brokerName && $broker->name !== $brokerName) {
                    continue;
                }

                // Get invoices for this broker
                $invoiceQuery = Invoice::where('prepared_by', $broker->name);
                if ($fromDate) {
                    $invoiceQuery->where('invoice_date', '>=', $fromDate);
                }
                if ($toDate) {
                    $invoiceQuery->where('invoice_date', '<=', $toDate);
                }
                $invoices = $invoiceQuery->get();

                // Get quotations for this broker
                $quotationQuery = Quotation::where('agent_name', $broker->name);
                if ($fromDate) {
                    $quotationQuery->where('date', '>=', $fromDate);
                }
                if ($toDate) {
                    $quotationQuery->where('date', '<=', $toDate);
                }
                $quotations = $quotationQuery->get();

                // Calculate totals
                $totalInvoiceAmount = $invoices->sum('total_amount');
                $totalQuotationAmount = $quotations->sum('total');
                $totalInvoiceCount = $invoices->count();
                $totalQuotationCount = $quotations->count();

                $reportData[] = [
                    'broker' => [
                        'id' => $broker->id,
                        'name' => $broker->name,
                        'staff_id' => $broker->staff_id,
                        'mobile_no' => $broker->mobile_no,
                        'whatsapp_no' => $broker->whatsapp_no,
                    ],
                    'invoices' => [
                        'count' => $totalInvoiceCount,
                        'total_amount' => $totalInvoiceAmount,
                        'data' => $invoices->map(function ($invoice) {
                            return [
                                'id' => $invoice->id,
                                'invoice_no' => $invoice->invoice_no,
                                'invoice_date' => $invoice->invoice_date,
                                'customer_name' => $invoice->customer_name,
                                'total_amount' => $invoice->total_amount,
                                'status' => $invoice->status,
                                'approved_by' => $invoice->approved_by,
                            ];
                        }),
                    ],
                    'quotations' => [
                        'count' => $totalQuotationCount,
                        'total_amount' => $totalQuotationAmount,
                        'data' => $quotations->map(function ($quotation) {
                            return [
                                'id' => $quotation->id,
                                'quotation_no' => $quotation->quotation_no,
                                'date' => $quotation->date,
                                'customer_name' => $quotation->customer->customer_name ?? 'N/A',
                                'total' => $quotation->total,
                                'approved_by' => $quotation->approved_by,
                            ];
                        }),
                    ],
                    'summary' => [
                        'total_sales' => $totalInvoiceAmount + $totalQuotationAmount,
                        'total_transactions' => $totalInvoiceCount + $totalQuotationCount,
                    ],
                ];
            }

            // Filter out brokers with no data if requested
            if ($request->get('show_only_active', false)) {
                $reportData = array_filter($reportData, function ($data) {
                    return $data['invoices']['count'] > 0 || $data['quotations']['count'] > 0;
                });
            }

            return response()->json([
                'success' => true,
                'data' => $reportData,
                'filters' => [
                    'from_date' => $fromDate,
                    'to_date' => $toDate,
                    'broker_name' => $brokerName,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate broker report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getBrokerList()
    {
        try {
            $brokers = StaffLedger::where('account_group', 'broker')
                ->select('id', 'name', 'staff_id')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $brokers,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch broker list',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
} 