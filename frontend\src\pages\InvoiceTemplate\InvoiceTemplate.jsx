import React, { useState, useEffect, useRef, useCallback } from "react";
import { saveAs } from "file-saver";
import html2canvas from "html2canvas";
import axios from "axios";
import { DndContext, useDraggable, closestCenter } from "@dnd-kit/core";
import { useRegister } from "../../context/RegisterContext";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/NewAuthContext";
import "./InvoiceTemplate.css";

const DraggableElement = React.memo(
  ({
    id,
    type,
    content,
    position,
    size,
    style,
    onDelete,
    onResize,
    children,
  }) => {
    const { attributes, listeners, setNodeRef, transform } = useDraggable({
      id,
    });
    const [isResizing, setIsResizing] = useState(false);
    const [resizeHandle, setResizeHandle] = useState(null);

    const elementStyle = {
      position: "absolute",
      left: `${position.x}px`,
      top: `${position.y}px`,
      width: `${size.width}px`,
      height: `${size.height}px`,
      transform: transform
        ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
        : undefined,
      ...style,
      backgroundColor:
        type === "placeholder" ? "rgba(200, 230, 255, 0.5)" : undefined,
      cursor: isResizing ? "none" : "move",
    };

    const handleDoubleClick = (e) => {
      e.stopPropagation();
      e.preventDefault();
    };

    const handleMouseDown = (e, handle) => {
      e.stopPropagation();
      setIsResizing(true);
      setResizeHandle(handle);
    };

    const handleMouseMove = (e) => {
      if (!isResizing) return;

      const element = document.querySelector(`[data-id="${id}"]`);
      if (!element) return;

      let newWidth = size.width;
      let newHeight = size.height;
      let newX = position.x;
      let newY = position.y;

      if (resizeHandle === "bottom-right") {
        newWidth = Math.max(20, Math.min(500, size.width + e.movementX));
        newHeight = Math.max(20, Math.min(500, size.height + e.movementY));
      } else if (resizeHandle === "bottom-left") {
        newWidth = Math.max(20, Math.min(500, size.width - e.movementX));
        newX = position.x + (size.width - newWidth);
      } else if (resizeHandle === "top-right") {
        newHeight = Math.max(20, Math.min(500, size.height - e.movementY));
        newY = position.y + (size.height - newHeight);
      } else if (resizeHandle === "top-left") {
        newWidth = Math.max(20, Math.min(500, size.width - e.movementX));
        newHeight = Math.max(20, Math.min(500, size.height - e.movementY));
        newX = position.x + (size.width - newWidth);
        newY = position.y + (size.height - newHeight);
      }

      onResize(type, id, newWidth, newHeight);

      if (
        resizeHandle === "top-left" ||
        resizeHandle === "bottom-left" ||
        resizeHandle === "top-right"
      ) {
        const index = parseInt(id.split("-")[1], 10);
        window.dispatchEvent(
          new CustomEvent("updatePosition", {
            detail: { id, type, index, x: newX, y: newY },
          })
        );
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setResizeHandle(null);
    };

    React.useEffect(() => {
      if (isResizing) {
        window.addEventListener("mousemove", handleMouseMove);
        window.addEventListener("mouseup", handleMouseUp);
      }
      return () => {
        window.removeEventListener("mousemove", handleMouseMove);
        window.removeEventListener("mouseup", handleMouseUp);
      };
    }, [isResizing, resizeHandle, size, position, type, id, onResize]);

    return (
      <div
        ref={setNodeRef}
        className={`draggable-element ${type}`}
        style={elementStyle}
        {...listeners}
        {...attributes}
        role="region"
        aria-label={`${type} element`}
        onDoubleClick={handleDoubleClick}
        data-id={id}
      >
        {/* Delete button positioned outside other controls for better visibility */}
        <button
          className="delete-btn"
          onClick={(e) => {
            e.stopPropagation();
            if (
              window.confirm("Are you sure you want to delete this element?")
            ) {
              onDelete(id);
            }
          }}
          title="Delete element"
        >
          ×
        </button>

        <div className="element-label">
          {type.charAt(0).toUpperCase() + type.slice(1)}
        </div>
        <div className="element-content">{content || children}</div>
        <div className="element-controls">
          <div className="resize-controls">
            <span>Size:</span>
            <input
              type="number"
              min="20"
              max="500"
              value={size.width}
              onChange={(e) => {
                e.stopPropagation();
                onResize(type, id, parseInt(e.target.value), size.height);
              }}
              className="size-input"
              onDoubleClick={(e) => e.stopPropagation()}
            />
            ×
            <input
              type="number"
              min="20"
              max="500"
              value={size.height}
              onChange={(e) => {
                e.stopPropagation();
                onResize(type, id, size.width, parseInt(e.target.value));
              }}
              className="size-input"
              onDoubleClick={(e) => e.stopPropagation()}
            />
          </div>
          <div
            className="resize-handle top-left"
            onMouseDown={(e) => handleMouseDown(e, "top-left")}
            role="button"
            aria-label="Resize top-left corner"
          />
          <div
            className="resize-handle top-right"
            onMouseDown={(e) => handleMouseDown(e, "top-right")}
            role="button"
            aria-label="Resize top-right corner"
          />
          <div
            className="resize-handle bottom-left"
            onMouseDown={(e) => handleMouseDown(e, "bottom-left")}
            role="button"
            aria-label="Resize bottom-left corner"
          />
          <div
            className="resize-handle bottom-right"
            onMouseDown={(e) => handleMouseDown(e, "bottom-right")}
            role="button"
            aria-label="Resize bottom-right corner"
          />
        </div>
      </div>
    );
  }
);

const InvoiceTemplateGenerator = ({
  billNumber = null,
  receivedAmount = null,
  balanceAmount = null,
}) => {
  const { getAuthHeaders } = useRegister();
  const { user } = useAuth();
  const navigate = useNavigate();
  const templateTypes = {
    POS: { name: "POS Receipt", width: 80, height: 297, fontSize: 10 },
    "Touch POS": {
      name: "Touch POS Receipt",
      width: 80,
      height: 297,
      fontSize: 12,
    },
    A4: { name: "A4 Invoice", width: 210, height: 297, fontSize: 12 },
    A5: { name: "A5 Invoice", width: 148, height: 210, fontSize: 12 },
    Custom: { name: "Custom Size", width: 150, height: 200, fontSize: 12 },
  };

  const [companyDetails, setCompanyDetails] = useState({
    company_name: "MUNSI TEX",
    business_address: "MOSQUE BUILDING, POLICE ROAD, KALMUNAI",
    contact_number: "076 731 78 51, 074 301 43 57",
  });

  const [customers, setCustomers] = useState([]);
  const [sampleProducts, setSampleProducts] = useState([
    {
      product_id: 1,
      product_name: "perfactil",
      qty: 3,
      unit_type: "Pcs",
      mrp: 221.86,
      discount: 10.0,
    },
    {
      product_id: 2,
      product_name: "froceval capsules",
      qty: 2,
      unit_type: "Pcs",
      mrp: 109.0,
      discount: 10.0,
    },
  ]);
  const [itemListColumns, setItemListColumns] = useState([
    { id: "no", label: "No", visible: true },
    { id: "name", label: "Name", visible: true },
    { id: "qty", label: "Qty", visible: true },
    { id: "mrp", label: "MRP", visible: true },
    { id: "price", label: "Price", visible: true },
    { id: "discount", label: "Discount", visible: true },
    { id: "total", label: "Total", visible: true },
  ]);

  const [dynamicPlaceholders, setDynamicPlaceholders] = useState({
    c1: { label: "Customer Name", value: "{{customer_name}}" },
    t1: { label: "Address", value: "{{address}}" },
    t2: { label: "Date", value: "{{bill_date}}" },
    t3: { label: "Bill Number", value: "{{bill_number}}" },
    t4: { label: "Total Amount", value: "{{bill_amount}}" },
    t5: { label: "Payment Type", value: "{{payment_type}}" },
    t6: { label: "Company Name", value: companyDetails.company_name },
    t7: { label: "Company Contact", value: companyDetails.contact_number },
    t8: { label: "Item List", value: "{{item_list}}" },
    t9: { label: "Subtotal", value: "{{subtotal}}" },
    t10: { label: "Tax", value: "{{tax_amount}}" },
    t11: { label: "Discount", value: "{{discount_amount}}" },
    t12: { label: "Grand Total", value: "{{grand_total}}" },
    t13: { label: "Thank You Message", value: "{{thank_you_message}}" },
    t14: { label: "Terms", value: "{{terms_and_conditions}}" },
    t15: { label: "Received Amount", value: "{{received_amount}}" },
    t16: { label: "Balance", value: "{{balance_amount}}" },
    t17: { label: "Time", value: "{{bill_time}}" },
    t18: {
      label: "Cashier",
      value: user?.name || user?.username || "{{cashier_name}}",
    },
    t19: { label: "Current Time", value: new Date().toLocaleString() },
  });

  const [selectedInvoiceData, setSelectedInvoiceData] = useState(null);

  const fetchInvoiceOrSaleData = async (billNumber) => {
    try {
      let response = await axios.get(
        `http://127.0.0.1:8000/api/invoices/${billNumber}`,
        {
          headers: getAuthHeaders().headers,
        }
      );
      if (response.data) {
        setSelectedInvoiceData(response.data);
        updateDynamicPlaceholders(response.data);
        return;
      }
    } catch (error) {
      try {
        let response = await axios.get(
          `http://127.0.0.1:8000/api/sales/${billNumber}`,
          {
            headers: getAuthHeaders().headers,
          }
        );
        if (response.data) {
          setSelectedInvoiceData(response.data);
          updateDynamicPlaceholders(response.data);
          return;
        }
      } catch (err) {
        console.error("Failed to fetch invoice or sale data:", err);
      }
    }
    setSelectedInvoiceData(null);
    resetDynamicPlaceholders();
  };

  const updateDynamicPlaceholders = (data) => {
    setDynamicPlaceholders((prev) => ({
      ...prev,
      c1: { ...prev.c1, value: data.customer_name || "{{customer_name}}" },
      t3: {
        ...prev.t3,
        value: data.invoice_no || data.bill_number || "{{bill_number}}",
      },
      t9: { ...prev.t9, value: data.subtotal?.toString() || "{{subtotal}}" },
      t12: {
        ...prev.t12,
        value:
          data.total_amount?.toString() ||
          data.grand_total?.toString() ||
          "{{grand_total}}",
      },
      t15: {
        ...prev.t15,
        value:
          data.paid_amount?.toString() ||
          data.purchase_amount?.toString() ||
          data.received_amount?.toString() ||
          "{{received_amount}}",
      },
      t16: {
        ...prev.t16,
        value:
          data.balance_amount?.toString() ||
          data.balance?.toString() ||
          "{{balance_amount}}",
      },
      t17: {
        ...prev.t17,
        value:
          data.bill_time ||
          data.invoice_time ||
          new Date().toLocaleTimeString() ||
          "{{bill_time}}",
      },
      t18: {
        ...prev.t18,
        value: user?.name || user?.username || "{{cashier_name}}",
      },
      t19: {
        ...prev.t19,
        value: new Date().toLocaleString(),
      },
    }));
  };

  const resetDynamicPlaceholders = () => {
    setDynamicPlaceholders((prev) => ({
      ...prev,
      c1: { ...prev.c1, value: "{{customer_name}}" },
      t3: { ...prev.t3, value: "{{bill_number}}" },
      t9: { ...prev.t9, value: "{{subtotal}}" },
      t12: { ...prev.t12, value: "{{grand_total}}" },
      t15: { ...prev.t15, value: "{{received_amount}}" },
      t16: { ...prev.t16, value: "{{balance_amount}}" },
      t17: { ...prev.t17, value: "{{bill_time}}" },
      t18: { ...prev.t18, value: "{{cashier_name}}" },
      t19: { ...prev.t19, value: "{{current_time}}" },
    }));
  };

  const [templateType, setTemplateType] = useState("POS");
  const [customWidth, setCustomWidth] = useState(150);
  const [customHeight, setCustomHeight] = useState(200);
  const [textInput, setTextInput] = useState("");
  const [imageInput, setImageInput] = useState("");
  const [elements, setElements] = useState({
    text: [],
    textPositions: [],
    textStyles: [],
    textSizes: [],
    image: [],
    imagePositions: [],
    imageSizes: [],
    placeholder: [],
    placeholderPositions: [],
    placeholderSizes: [],
  });
  const [tempStyle, setTempStyle] = useState({
    fontSize: templateTypes.POS.fontSize,
    color: "#000000",
    fontWeight: "normal",
    fontStyle: "normal",
    textAlign: "left",
    fontFamily: "Arial, sans-serif",
  });
  const [showGrid, setShowGrid] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [gridSize, setGridSize] = useState(10);
  const [activeTab, setActiveTab] = useState("text");
  const [templateName, setTemplateName] = useState("My Template");
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [savedTemplates, setSavedTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const dropAreaRef = useRef(null);

  const calculateDynamicHeight = useCallback(() => {
    let maxY = 0;
    elements.textPositions.forEach((pos, i) => {
      maxY = Math.max(maxY, pos.y + (elements.textSizes[i]?.height || 0));
    });
    elements.imagePositions.forEach((pos, i) => {
      maxY = Math.max(maxY, pos.y + (elements.imageSizes[i]?.height || 0));
    });
    elements.placeholderPositions.forEach((pos, i) => {
      maxY = Math.max(
        maxY,
        pos.y + (elements.placeholderSizes[i]?.height || 0)
      );
    });
    return Math.max(
      templateType === "Custom"
        ? customHeight
        : templateTypes[templateType].height,
      maxY * 1.2
    );
  }, [elements, templateType, customHeight]);

  useEffect(() => {
    const newHeight = calculateDynamicHeight();
    if (templateType === "Custom") {
      setCustomHeight(Math.min(500, Math.max(50, newHeight)));
    }
  }, [elements, calculateDynamicHeight, templateType]);

  useEffect(() => {
    if (billNumber) {
      fetchInvoiceOrSaleData(billNumber);
    }
    if (receivedAmount !== null && balanceAmount !== null) {
      setDynamicPlaceholders((prev) => ({
        ...prev,
        t15: { ...prev.t15, value: receivedAmount.toString() },
        t16: { ...prev.t16, value: balanceAmount.toString() },
      }));
    }
  }, [billNumber, receivedAmount, balanceAmount]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const customersResponse = await axios.get(
          "http://127.0.0.1:8000/api/customers",
          { headers: getAuthHeaders().headers }
        );
        setCustomers(customersResponse.data.data || []);

        setDynamicPlaceholders((prev) => ({
          ...prev,
          t6: { ...prev.t6, value: companyDetails.company_name },
          t7: { ...prev.t7, value: companyDetails.contact_number },
        }));

        const templatesResponse = await axios.get(
          "http://127.0.0.1:8000/api/invoice-templates",
          { headers: getAuthHeaders().headers }
        );
        setSavedTemplates(templatesResponse.data.data || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        alert("Failed to fetch data. Please try again.");
      }
    };

    fetchData();
  }, [companyDetails, getAuthHeaders]);

  useEffect(() => {
    if (templateType !== "Custom") {
      setTempStyle((prev) => ({
        ...prev,
        fontSize: templateTypes[templateType].fontSize,
      }));
    }
  }, [templateType]);

  useEffect(() => {
    const handlePositionUpdate = (event) => {
      const { id, type, index, x, y } = event.detail;
      setElements((prev) => {
        const newElements = { ...prev };
        switch (type) {
          case "text":
            newElements.textPositions = [...newElements.textPositions];
            newElements.textPositions[index] = { x, y };
            break;
          case "image":
            newElements.imagePositions = [...newElements.imagePositions];
            newElements.imagePositions[index] = { x, y };
            break;
          case "placeholder":
            newElements.placeholderPositions = [
              ...newElements.placeholderPositions,
            ];
            newElements.placeholderPositions[index] = { x, y };
            break;
          default:
            break;
        }
        return newElements;
      });
    };

    window.addEventListener("updatePosition", handlePositionUpdate);
    return () => {
      window.removeEventListener("updatePosition", handlePositionUpdate);
    };
  }, []);

  const getCenterPosition = () => {
    if (dropAreaRef.current) {
      const { width, height } = dropAreaRef.current.getBoundingClientRect();
      return { x: width / 2, y: height / 2 };
    }
    return { x: 0, y: 0 };
  };

  const renderManageTemplatesButton = () => (
    <button
      onClick={() => navigate("/template-manager")}
      className="px-6 py-2 mb-4 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
    >
      Manage Templates
    </button>
  );

  const handleAddText = () => {
    if (!textInput.trim()) {
      alert("Text content is required");
      return;
    }
    const centerPos = getCenterPosition();
    setElements((prev) => ({
      ...prev,
      text: [...prev.text, textInput],
      textPositions: [...prev.textPositions, centerPos],
      textStyles: [...prev.textStyles, { ...tempStyle }],
      textSizes: [...prev.textSizes, { width: 150, height: 50 }],
    }));
    setTextInput("");
  };

  const handleAddImage = () => {
    if (!imageInput.trim()) {
      alert("Please provide an image URL or upload an image.");
      return;
    }

    if (imageInput.startsWith("http://") || imageInput.startsWith("https://")) {
      if (!/\.(png|jpg|jpeg|gif)$/i.test(imageInput)) {
        alert("Please provide a valid image URL (png, jpg, jpeg, or gif).");
        return;
      }
    } else if (imageInput.startsWith("data:image/")) {
      if (!/^data:image\/(png|jpg|jpeg|gif);base64,/.test(imageInput)) {
        const mimeType = imageInput.match(/^data:image\/([^;]+);base64,/);
        const detectedType = mimeType ? mimeType[1] : "unknown";
        alert(
          `Invalid base64 image format. Detected format: ${detectedType}. Supported formats: png, jpg, jpeg, gif.`
        );
        return;
      }
      const base64 = imageInput.split(",")[1];
      try {
        atob(base64);
      } catch (e) {
        console.error("Base64 decode error:", e);
        alert("Invalid base64 image data. Please upload a valid image.");
        return;
      }
    } else {
      alert("Invalid image input. Use a valid URL or upload an image.");
      return;
    }

    const centerPos = getCenterPosition();
    setElements((prev) => ({
      ...prev,
      image: [...prev.image, imageInput],
      imagePositions: [...prev.imagePositions, centerPos],
      imageSizes: [...prev.imageSizes, { width: 100, height: 100 }],
    }));
    setImageInput("");
  };

  const handleFileUpload = (e) => {
    if (e.target.files[0]) {
      const file = e.target.files[0];
      const fileType = file.type;
      if (!/image\/(png|jpg|jpeg|gif)/.test(fileType)) {
        alert(
          `Unsupported file type: ${fileType.split("/")[1] || "unknown"}. Please upload a png, jpg, jpeg, or gif image.`
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = (event) => {
        setImageInput(event.target.result);
      };
      reader.onerror = (error) => {
        console.error("FileReader error:", error);
        alert("Error reading the image file. Please try another image.");
        e.target.value = "";
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddPlaceholder = (key) => {
    const centerPos = getCenterPosition();
    const isItemList = key === "t8";

    setElements((prev) => ({
      ...prev,
      placeholder: [...prev.placeholder, key],
      placeholderPositions: [...prev.placeholderPositions, centerPos],
      placeholderSizes: [
        ...prev.placeholderSizes,
        isItemList ? { width: 226, height: 200 } : { width: 150, height: 50 },
      ],
    }));
  };

  const handleDragEnd = (event) => {
    const { active, delta } = event;
    const idParts = active.id.split("-");
    const type = idParts[0];
    const index = parseInt(idParts[1], 10);

    setElements((prev) => {
      const newElements = { ...prev };
      let newPositions;
      switch (type) {
        case "text":
          newPositions = [...newElements.textPositions];
          newPositions[index] = {
            x: newPositions[index].x + delta.x,
            y: newPositions[index].y + delta.y,
          };
          if (snapToGrid) {
            newPositions[index].x =
              Math.round(newPositions[index].x / gridSize) * gridSize;
            newPositions[index].y =
              Math.round(newPositions[index].y / gridSize) * gridSize;
          }
          newElements.textPositions = newPositions;
          break;
        case "image":
          newPositions = [...newElements.imagePositions];
          newPositions[index] = {
            x: newPositions[index].x + delta.x,
            y: newPositions[index].y + delta.y,
          };
          if (snapToGrid) {
            newPositions[index].x =
              Math.round(newPositions[index].x / gridSize) * gridSize;
            newPositions[index].y =
              Math.round(newPositions[index].y / gridSize) * gridSize;
          }
          newElements.imagePositions = newPositions;
          break;
        case "placeholder":
          newPositions = [...newElements.placeholderPositions];
          newPositions[index] = {
            x: newPositions[index].x + delta.x,
            y: newPositions[index].y + delta.y,
          };
          if (snapToGrid) {
            newPositions[index].x =
              Math.round(newPositions[index].x / gridSize) * gridSize;
            newPositions[index].y =
              Math.round(newPositions[index].y / gridSize) * gridSize;
          }
          newElements.placeholderPositions = newPositions;
          break;
        default:
          break;
      }
      return newElements;
    });
  };

  const handleDeleteItem = (id) => {
    const [type, indexStr] = id.split("-");
    const index = parseInt(indexStr, 10);

    setElements((prev) => {
      const newElements = { ...prev };
      switch (type) {
        case "text":
          newElements.text = newElements.text.filter((_, i) => i !== index);
          newElements.textPositions = newElements.textPositions.filter(
            (_, i) => i !== index
          );
          newElements.textStyles = newElements.textStyles.filter(
            (_, i) => i !== index
          );
          newElements.textSizes = newElements.textSizes.filter(
            (_, i) => i !== index
          );
          break;
        case "image":
          newElements.image = newElements.image.filter((_, i) => i !== index);
          newElements.imagePositions = newElements.imagePositions.filter(
            (_, i) => i !== index
          );
          newElements.imageSizes = newElements.imageSizes.filter(
            (_, i) => i !== index
          );
          break;
        case "placeholder":
          newElements.placeholder = newElements.placeholder.filter(
            (_, i) => i !== index
          );
          newElements.placeholderPositions =
            newElements.placeholderPositions.filter((_, i) => i !== index);
          newElements.placeholderSizes = newElements.placeholderSizes.filter(
            (_, i) => i !== index
          );
          break;
        default:
          break;
      }
      return newElements;
    });
  };

  const handleResizeElement = (type, id, width, height) => {
    const index = parseInt(id.split("-")[1], 10);

    if (width < 20 || width > 500 || height < 20 || height > 500) {
      alert("Size must be between 20 and 500 pixels");
      return;
    }

    setElements((prev) => {
      const newElements = { ...prev };
      const newSize = { width, height };

      switch (type) {
        case "text":
          newElements.textSizes = [...newElements.textSizes];
          newElements.textSizes[index] = newSize;
          break;
        case "image":
          newElements.imageSizes = [...newElements.imageSizes];
          newElements.imageSizes[index] = newSize;
          break;
        case "placeholder":
          newElements.placeholderSizes = [...newElements.placeholderSizes];
          newElements.placeholderSizes[index] = newSize;
          break;
        default:
          break;
      }
      return newElements;
    });
  };

  const toggleItemListColumn = (columnId) => {
    setItemListColumns((columns) =>
      columns.map((column) =>
        column.id === columnId
          ? { ...column, visible: !column.visible }
          : column
      )
    );
  };

  // const renderItemListPreview = ({ products = [] }) => {
  //   const renderItemList = () => {
  //     if (!products.length) {
  //       return <div>No products to display</div>;
  //     }
  //     return (
  //       <div style={{ fontFamily: "Arial, sans-serif", fontSize: "12px" }}>
  //         {products.map((item, index) => {
  //           const price = item.mrp - (item.discount || 0);
  //           const total = price * item.qty;
  //           return (
  //             <div key={index} style={{ display: "flex", marginBottom: "2px" }}>
  //               <span style={{ width: "20px" }}>{index + 1}</span>
  //               <span style={{ width: "100px", marginLeft: "5px" }}>
  //                 {item.product_name}
  //               </span>
  //               <span
  //                 style={{
  //                   width: "40px",
  //                   textAlign: "right",
  //                   marginLeft: "5px",
  //                 }}
  //               >
  //                 {item.qty} {item.unit_type}
  //               </span>
  //               <span
  //                 style={{
  //                   width: "50px",
  //                   textAlign: "right",
  //                   marginLeft: "5px",
  //                 }}
  //               >
  //                 {price.toFixed(2)}
  //               </span>
  //               <span
  //                 style={{
  //                   width: "50px",
  //                   textAlign: "right",
  //                   marginLeft: "5px",
  //                 }}
  //               >
  //                 {(item.discount || 0).toFixed(2)}
  //               </span>
  //               <span
  //                 style={{
  //                   width: "50px",
  //                   textAlign: "right",
  //                   marginLeft: "5px",
  //                 }}
  //               >
  //                 {total.toFixed(2)}
  //               </span>
  //             </div>
  //           );
  //         })}
  //         <div
  //           style={{
  //             marginTop: "10px",
  //             fontWeight: "bold",
  //             textAlign: "right",
  //           }}
  //         >
  //           Total:{" "}
  //           {products
  //             .reduce(
  //               (sum, item) =>
  //                 sum + (item.mrp - (item.discount || 0)) * item.qty,
  //               0
  //             )
  //             .toFixed(2)}
  //         </div>
  //       </div>
  //     );
  //   };

  //   return (
  //     <div>
  //       <div style={{ fontWeight: "bold", marginBottom: "5px" }}>
  //         No Name Qty Price Discount Total
  //       </div>
  //       {renderItemList()}
  //       <div style={{ marginTop: "15px" }}>Billing Summary</div>
  //     </div>
  //   );
  // };

  const renderItemListPreview = ({ products = [] }) => {
    const cleanProductName = (name) => {
      // Remove expiry and batch details (e.g., "perfactil [Exp: 12/25, Batch: XYZ]" -> "perfactil")
      return name.replace(/\s*\[.*\]/, "").trim();
    };

    const renderItemList = () => {
      if (!products.length) {
        return <div>No products to display</div>;
      }
      return (
        <div style={{ fontFamily: "Arial, sans-serif", fontSize: "12px" }}>
          {products.map((item, index) => {
            const price = item.mrp - (item.discount || 0);
            const total = price * item.qty;
            return (
              <div key={index} style={{ display: "flex", marginBottom: "2px" }}>
                <span style={{ width: "30px", textAlign: "left" }}>
                  {index + 1}
                </span>
                <span style={{ width: "100px", marginLeft: "5px" }}>
                  {cleanProductName(item.product_name)}
                </span>
                <span
                  style={{
                    width: "40px",
                    textAlign: "right",
                    marginLeft: "5px",
                  }}
                >
                  {item.qty} {item.unit_type}
                </span>
                <span
                  style={{
                    width: "50px",
                    textAlign: "right",
                    marginLeft: "5px",
                  }}
                >
                  {item.mrp.toFixed(2)}
                </span>
                <span
                  style={{
                    width: "50px",
                    textAlign: "right",
                    marginLeft: "5px",
                  }}
                >
                  {(item.discount || 0).toFixed(2)}
                </span>
                <span
                  style={{
                    width: "50px",
                    textAlign: "right",
                    marginLeft: "5px",
                  }}
                >
                  {price.toFixed(2)}
                </span>
                <span
                  style={{
                    width: "50px",
                    textAlign: "right",
                    marginLeft: "5px",
                  }}
                >
                  {total.toFixed(2)}
                </span>
              </div>
            );
          })}
          <div
            style={{
              marginTop: "10px",
              fontWeight: "bold",
              textAlign: "right",
            }}
          >
            Total:{" "}
            {products
              .reduce(
                (sum, item) =>
                  sum + (item.mrp - (item.discount || 0)) * item.qty,
                0
              )
              .toFixed(2)}
          </div>
        </div>
      );
    };

    return (
      <div>
        <div style={{ fontWeight: "bold", marginBottom: "5px" }}>
          NO ITEM QTY MRP DISC PRICE TOTAL
        </div>
        {renderItemList()}
        <div style={{ marginTop: "15px" }}>Billing Summary</div>
      </div>
    );
  };
  const validateTemplate = (template) => {
    const errors = [];

    const isValidPosition = (pos) =>
      pos &&
      typeof pos.x === "number" &&
      pos.x >= 0 &&
      typeof pos.y === "number" &&
      pos.y >= 0;

    const isValidSize = (size) =>
      size &&
      typeof size.width === "number" &&
      size.width >= 20 &&
      size.width <= 500 &&
      typeof size.height === "number" &&
      size.height >= 20 &&
      size.height <= 500;

    const validateArray = (elements, positions, sizes, name) => {
      if (
        !Array.isArray(elements) ||
        !Array.isArray(positions) ||
        !Array.isArray(sizes)
      ) {
        errors.push(`${name} elements, positions, and sizes must be arrays.`);
        return;
      }
      if (
        elements.length !== positions.length ||
        elements.length !== sizes.length
      ) {
        errors.push(
          `${name} elements, positions, and sizes arrays must have the same length.`
        );
      }
      for (let i = 0; i < elements.length; i++) {
        if (!isValidPosition(positions[i])) {
          errors.push(`${name} position at index ${i} is invalid.`);
        }
        if (!isValidSize(sizes[i])) {
          errors.push(`${name} size at index ${i} is invalid.`);
        }
      }
    };

    validateArray(
      template.text_elements,
      template.text_positions,
      template.text_sizes,
      "Text"
    );
    validateArray(
      template.image_elements,
      template.image_positions,
      template.image_sizes,
      "Image"
    );
    validateArray(
      template.placeholder_elements,
      template.placeholder_positions,
      template.placeholder_sizes,
      "Placeholder"
    );

    if (!Array.isArray(template.item_list_columns)) {
      errors.push("Item list columns must be an array.");
    } else {
      template.item_list_columns.forEach((col, idx) => {
        if (typeof col.id !== "string" || col.id.trim() === "") {
          errors.push(
            `Item list column at index ${idx} has invalid or missing id.`
          );
        }
        if (typeof col.label !== "string" || col.label.trim() === "") {
          errors.push(
            `Item list column at index ${idx} has invalid or missing label.`
          );
        }
        if (typeof col.visible !== "boolean") {
          errors.push(
            `Item list column at index ${idx} has invalid or missing visible flag.`
          );
        }
      });
    }

    return errors;
  };

  const handleSaveTemplate = async () => {
    if (!templateName.trim()) {
      alert("Template name is required");
      return;
    }

    const cleanArray = (arr) =>
      Array.isArray(arr)
        ? arr.map((item) => ({
            ...item,
            x: Number.isFinite(item.x) ? item.x : 0,
            y: Number.isFinite(item.y) ? item.y : 0,
          }))
        : [];

    const template = {
      name: templateName,
      type: templateType,
      width:
        templateType === "Custom"
          ? Number.isFinite(customWidth) &&
            customWidth >= 50 &&
            customWidth <= 500
            ? customWidth
            : 150
          : templateTypes[templateType].width,
      height:
        templateType === "Custom"
          ? Number.isFinite(customHeight) &&
            customHeight >= 50 &&
            customHeight <= 500
            ? customHeight
            : 200
          : templateTypes[templateType].height,
      text_elements: elements.text,
      text_positions: cleanArray(elements.textPositions),
      text_styles: elements.textStyles,
      text_sizes: cleanArray(elements.textSizes),
      image_elements: elements.image,
      image_positions: cleanArray(elements.imagePositions),
      image_sizes: cleanArray(elements.imageSizes),
      placeholder_elements: elements.placeholder,
      placeholder_positions: cleanArray(elements.placeholderPositions),
      placeholder_sizes: cleanArray(elements.placeholderSizes),
      item_list_columns: itemListColumns,
    };

    const validationErrors = validateTemplate(template);
    if (validationErrors.length > 0) {
      alert("Validation errors:\n" + validationErrors.join("\n"));
      return;
    }

    try {
      const response = await axios.post(
        "http://127.0.0.1:8000/api/invoice-templates",
        template,
        { headers: getAuthHeaders().headers }
      );
      setSavedTemplates([...savedTemplates, response.data.data]);
      setShowSaveModal(false);
      alert("Template saved successfully!");
    } catch (error) {
      console.error("Error saving template:", error);
      const errorMessage = error.response?.data?.details
        ? Object.values(error.response.data.details).join(", ")
        : error.response?.data?.error ||
          "Failed to save template. Please try again.";
      alert(errorMessage);
    }
  };

  const handleLoadTemplate = (template) => {
    setTemplateType(template.type);
    setElements({
      text: template.text_elements || [],
      textPositions: template.text_positions || [],
      textStyles: template.text_styles || [],
      textSizes: template.text_sizes || [],
      image: template.image_elements || [],
      imagePositions: template.image_positions || [],
      imageSizes: template.image_sizes || [],
      placeholder: template.placeholder_elements || [],
      placeholderPositions: template.placeholder_positions || [],
      placeholderSizes: template.placeholder_sizes || [],
    });
    setItemListColumns(
      template.item_list_columns || [
        { id: "no", label: "No", visible: true },
        { id: "name", label: "Name", visible: true },
        { id: "qty", label: "Qty", visible: true },
        { id: "price", label: "Price", visible: true },
        { id: "total", label: "Total", visible: true },
      ]
    );
    setSelectedTemplate(template);
    setShowLoadModal(false);
  };

  const handleDeleteTemplate = async (id) => {
    try {
      await axios.delete(`http://127.0.0.1:8000/api/invoice-templates/${id}`);
      setSavedTemplates(
        savedTemplates.filter((template) => template.id !== id)
      );
    } catch (error) {
      console.error("Error deleting template:", error);
      alert("Failed to delete template.");
    }
  };

  const setDefaultTemplate = async (id) => {
    try {
      await axios.post(
        `http://127.0.0.1:8000/api/invoice-templates/${id}/set-default`,
        {},
        { headers: getAuthHeaders().headers }
      );
      const response = await axios.get(
        "http://127.0.0.1:8000/api/invoice-templates",
        { headers: getAuthHeaders().headers }
      );
      setSavedTemplates(response.data.data || []);
      alert("Template set as default successfully!");
    } catch (error) {
      console.error("Error setting default template:", error);
      alert("Failed to set default template. Please try again.");
    }
  };

  const removeDefaultTemplate = async (id) => {
    try {
      await axios.post(
        `http://127.0.0.1:8000/api/invoice-templates/${id}/remove-default`,
        {},
        { headers: getAuthHeaders().headers }
      );
      const response = await axios.get(
        "http://127.0.0.1:8000/api/invoice-templates",
        { headers: getAuthHeaders().headers }
      );
      setSavedTemplates(response.data.data || []);
      alert("Default status removed successfully!");
    } catch (error) {
      console.error("Error removing default template:", error);
      alert("Failed to remove default template. Please try again.");
    }
  };

  const handleExportPNG = () => {
    if (dropAreaRef.current) {
      html2canvas(dropAreaRef.current).then((canvas) => {
        canvas.toBlob((blob) => {
          saveAs(blob, `invoice-template-${Date.now()}.png`);
        });
      });
    }
  };

  const handleExportPDF = () => {
    alert("PDF export is not implemented. Use PNG export for now.");
  };

  const getTemplateDimensions = () => {
    if (templateType === "Custom") {
      return { width: customWidth, height: customHeight };
    }
    return {
      width: templateTypes[templateType].width,
      height: templateTypes[templateType].height,
    };
  };

  const renderGrid = () => {
    if (!showGrid) return null;
    const { width, height } = getTemplateDimensions();
    return (
      <div
        className="grid-overlay"
        style={{
          backgroundImage: `
            linear-gradient(to right, #e0e0e0 1px, transparent 1px),
            linear-gradient(to bottom, #e0e0e0 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`,
          width: `${width}mm`,
          height: `${height}mm`,
        }}
      />
    );
  };

  const handleClosePage = () => {
    if (
      window.confirm(
        "Are you sure you want to leave? Any unsaved changes will be lost."
      )
    ) {
      navigate(-1); // Go back to previous page
    }
  };

  const handleClearAll = () => {
    const totalElements =
      elements.text.length +
      elements.image.length +
      elements.placeholder.length;
    if (totalElements === 0) {
      alert("No elements to clear.");
      return;
    }

    if (
      window.confirm(
        `Are you sure you want to clear all ${totalElements} elements? This action cannot be undone.`
      )
    ) {
      setElements({
        text: [],
        textPositions: [],
        textSizes: [],
        textStyles: [],
        image: [],
        imagePositions: [],
        imageSizes: [],
        placeholder: [],
        placeholderPositions: [],
        placeholderSizes: [],
      });
      alert("All elements cleared successfully!");
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+Q or Cmd+Q to close
      if ((e.ctrlKey || e.metaKey) && e.key === "q") {
        e.preventDefault();
        handleClosePage();
      }
      // Ctrl+Shift+C or Cmd+Shift+C to clear all
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === "C") {
        e.preventDefault();
        handleClearAll();
      }
      // Escape key to close
      if (e.key === "Escape") {
        handleClosePage();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [elements]);

  return (
    <DndContext onDragEnd={handleDragEnd} collisionDetection={closestCenter}>
      <div className="template-generator-container">
        <div className="sidebar">
          <div className="header-section">
            <h2>Invoice Template Designer</h2>
            <button
              onClick={handleClosePage}
              className="close-btn"
              title="Close Template Designer"
            >
              ✕
            </button>
          </div>
          {renderManageTemplatesButton()}
          <div className="settings-section">
            <h3>Template Settings</h3>
            <label>Template Type:</label>
            <select
              value={templateType}
              onChange={(e) => setTemplateType(e.target.value)}
              className="setting-select"
            >
              {Object.keys(templateTypes).map((type) => (
                <option key={type} value={type}>
                  {templateTypes[type].name}
                </option>
              ))}
            </select>
            {templateType === "Custom" && (
              <>
                <label>Width (mm):</label>
                <input
                  type="number"
                  min="50"
                  max="500"
                  value={customWidth}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (value >= 50 && value <= 500) {
                      setCustomWidth(value);
                    } else {
                      alert("Width must be between 50 and 500 mm");
                    }
                  }}
                  className="setting-input"
                />
                <label>Height (mm):</label>
                <input
                  type="number"
                  min="50"
                  max="500"
                  value={customHeight}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (value >= 50 && value <= 500) {
                      setCustomHeight(value);
                    } else {
                      alert("Height must be between 50 and 500 mm");
                    }
                  }}
                  className="setting-input"
                />
              </>
            )}
            <div className="grid-controls">
              <label>
                <input
                  type="checkbox"
                  checked={showGrid}
                  onChange={() => setShowGrid(!showGrid)}
                />
                Show Grid
              </label>
              <label>
                <input
                  type="checkbox"
                  checked={snapToGrid}
                  onChange={() => setSnapToGrid(!snapToGrid)}
                />
                Snap to Grid
              </label>
              {showGrid && (
                <>
                  <label>Grid Size (px):</label>
                  <input
                    type="number"
                    min="5"
                    max="50"
                    value={gridSize}
                    onChange={(e) => {
                      const value = parseInt(e.target.value);
                      if (value >= 5 && value <= 50) {
                        setGridSize(value);
                      } else {
                        alert("Grid size must be between 5 and 50 pixels");
                      }
                    }}
                    className="setting-input"
                  />
                </>
              )}
            </div>
          </div>
          <div className="tabs">
            <button
              className={`tab ${activeTab === "text" ? "active" : ""}`}
              onClick={() => setActiveTab("text")}
            >
              Text
            </button>
            <button
              className={`tab ${activeTab === "images" ? "active" : ""}`}
              onClick={() => setActiveTab("images")}
            >
              Images
            </button>
            <button
              className={`tab ${activeTab === "placeholders" ? "active" : ""}`}
              onClick={() => setActiveTab("placeholders")}
            >
              Placeholders
            </button>
            <button
              className={`tab ${activeTab === "items" ? "active" : ""}`}
              onClick={() => setActiveTab("items")}
            >
              Item List
            </button>
          </div>
          {activeTab === "text" && (
            <div className="controls-section">
              <h3>Add Text</h3>
              <textarea
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="Enter text content"
                className="text-input"
                rows={3}
              />
              <div className="style-controls">
                <label>Font Size:</label>
                <input
                  type="number"
                  min="8"
                  max="72"
                  value={tempStyle.fontSize}
                  onChange={(e) =>
                    setTempStyle({
                      ...tempStyle,
                      fontSize: parseInt(e.target.value),
                    })
                  }
                  className="style-input"
                />
                <label>Color:</label>
                <input
                  type="color"
                  value={tempStyle.color}
                  onChange={(e) =>
                    setTempStyle({ ...tempStyle, color: e.target.value })
                  }
                  className="style-input"
                />
                <label>Font Family:</label>
                <select
                  value={tempStyle.fontFamily}
                  onChange={(e) =>
                    setTempStyle({ ...tempStyle, fontFamily: e.target.value })
                  }
                  className="style-input"
                >
                  <option value="Arial, sans-serif">Arial</option>
                  <option value="'Times New Roman', serif">
                    Times New Roman
                  </option>
                  <option value="'Courier New', monospace">Courier New</option>
                  <option value="'Brush Script MT', cursive">
                    Brush Script
                  </option>
                </select>
                <label>Text Align:</label>
                <select
                  value={tempStyle.textAlign}
                  onChange={(e) =>
                    setTempStyle({ ...tempStyle, textAlign: e.target.value })
                  }
                  className="style-input"
                >
                  <option value="left">Left</option>
                  <option value="center">Center</option>
                  <option value="right">Right</option>
                </select>
                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={tempStyle.fontWeight === "bold"}
                      onChange={(e) =>
                        setTempStyle({
                          ...tempStyle,
                          fontWeight: e.target.checked ? "bold" : "normal",
                        })
                      }
                    />
                    Bold
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={tempStyle.fontStyle === "italic"}
                      onChange={(e) =>
                        setTempStyle({
                          ...tempStyle,
                          fontStyle: e.target.checked ? "italic" : "normal",
                        })
                      }
                    />
                    Italic
                  </label>
                </div>
              </div>
              <button onClick={handleAddText} className="add-btn">
                Add Text Element
              </button>
            </div>
          )}
          {activeTab === "images" && (
            <div className="controls-section">
              <h3>Add Image</h3>
              <input
                type="text"
                value={imageInput}
                onChange={(e) => setImageInput(e.target.value)}
                placeholder="Enter image URL"
                className="text-input"
              />
              <small>Or upload from device:</small>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="file-input"
              />
              <button onClick={handleAddImage} className="add-btn">
                Add Image Element
              </button>
            </div>
          )}
          {activeTab === "placeholders" && (
            <div className="controls-section">
              <h3>Dynamic Placeholders</h3>
              <div className="placeholders-grid">
                {Object.entries(dynamicPlaceholders).map(([key, { label }]) => (
                  <div
                    key={key}
                    className="draggable-placeholder"
                    onClick={() => handleAddPlaceholder(key)}
                    title={label}
                  >
                    {key}: {label}
                  </div>
                ))}
              </div>
            </div>
          )}
          {activeTab === "items" && (
            <div className="controls-section">
              <h3>Item List Settings</h3>
              <div className="item-list-settings">
                <h4>Visible Columns</h4>
                <div className="column-checkboxes">
                  {itemListColumns.map((column) => (
                    <label key={column.id} className="column-checkbox">
                      <input
                        type="checkbox"
                        checked={column.visible}
                        onChange={() => toggleItemListColumn(column.id)}
                      />
                      {column.label}
                    </label>
                  ))}
                </div>
                <h4>Preview</h4>
                {/* <div className="item-list-preview">
                  {renderItemListPreview()}
                </div> */}

                <div className="item-list-preview">
                  {renderItemListPreview({ products: sampleProducts })}
                </div>
                <button
                  onClick={() => handleAddPlaceholder("t8")}
                  className="add-btn"
                >
                  Add Item List to Template
                </button>
              </div>
            </div>
          )}
          <div className="actions-section">
            <h3>Template Actions</h3>
            <button
              onClick={() => setShowSaveModal(true)}
              className="action-btn save"
            >
              Save Template
            </button>
            <button
              onClick={() => setShowLoadModal(true)}
              className="action-btn load"
            >
              Load Template
            </button>
            <button onClick={handleExportPNG} className="action-btn export">
              Export as PNG
            </button>
            <button onClick={handleExportPDF} className="action-btn export">
              Export as PDF
            </button>
            <button onClick={handleClearAll} className="action-btn clear">
              Clear All Elements
            </button>
          </div>
          <div className="help-section">
            <h3>Quick Help</h3>
            <div className="help-content">
              <p>
                <strong>Delete Elements:</strong>
              </p>
              <ul>
                <li>Hover over any element and click the red × button</li>
                <li>Each deletion requires confirmation</li>
              </ul>
              <p>
                <strong>Keyboard Shortcuts:</strong>
              </p>
              <ul>
                <li>
                  <kbd>Escape</kbd> or <kbd>Ctrl+Q</kbd> - Close designer
                </li>
                <li>
                  <kbd>Ctrl+Shift+C</kbd> - Clear all elements
                </li>
              </ul>
              <p>
                <strong>Tips:</strong>
              </p>
              <ul>
                <li>Drag elements to reposition them</li>
                <li>Use resize handles to adjust size</li>
                <li>Enable grid for precise alignment</li>
              </ul>
            </div>
          </div>
        </div>
        <div className="design-area">
          <h2>Design Canvas</h2>
          <div
            ref={dropAreaRef}
            className="drop-area"
            style={{
              width: `${getTemplateDimensions().width}mm`,
              height: `${getTemplateDimensions().height}mm`,
              position: "relative",
              minHeight: "200mm",
            }}
          >
            {renderGrid()}
            {elements.placeholder.map((key, index) => (
              <DraggableElement
                key={`placeholder-${index}`}
                id={`placeholder-${index}`}
                type="placeholder"
                content={dynamicPlaceholders[key].value}
                position={elements.placeholderPositions[index]}
                size={
                  elements.placeholderSizes[index] ||
                  (key === "t8"
                    ? { width: 226, height: 200 }
                    : { width: 150, height: 50 })
                }
                style={{
                  fontSize: `${tempStyle.fontSize}px`,
                  textAlign: tempStyle.textAlign,
                  fontFamily: tempStyle.fontFamily,
                  color: tempStyle.color,
                  fontWeight: tempStyle.fontWeight,
                  fontStyle: tempStyle.fontStyle,
                }}
                onDelete={handleDeleteItem}
                onResize={handleResizeElement}
              >
                {/* {key === "t8" && renderItemListPreview()} */}
                {key === "t8" &&
                  renderItemListPreview({ products: sampleProducts })}
              </DraggableElement>
            ))}
            {elements.text.map((text, index) => (
              <DraggableElement
                key={`text-${index}`}
                id={`text-${index}`}
                type="text"
                content={text}
                position={elements.textPositions[index]}
                size={elements.textSizes[index]}
                style={elements.textStyles[index]}
                onDelete={handleDeleteItem}
                onResize={handleResizeElement}
              />
            ))}
            {elements.image.map((src, index) => (
              <DraggableElement
                key={`image-${index}`}
                id={`image-${index}`}
                type="image"
                position={elements.imagePositions[index]}
                size={elements.imageSizes[index]}
                onDelete={handleDeleteItem}
                onResize={handleResizeElement}
              >
                <img
                  src={src}
                  alt={`img-${index}`}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                  }}
                />
              </DraggableElement>
            ))}
          </div>
        </div>
        {showSaveModal && (
          <div className="modal-overlay">
            <div className="modal">
              <h3>Save Template</h3>
              <label>Template Name:</label>
              <input
                type="text"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                placeholder="Enter template name"
                className="modal-input"
              />
              <div className="modal-actions">
                <button
                  onClick={() => setShowSaveModal(false)}
                  className="modal-btn cancel"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveTemplate}
                  className="modal-btn confirm"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        )}
        {showLoadModal && (
          <div className="modal-overlay">
            <div className="modal">
              <h3>Load Template</h3>
              {savedTemplates.length === 0 ? (
                <p>No saved templates found.</p>
              ) : (
                <div className="templates-list">
                  {savedTemplates.map((template) => (
                    <div
                      key={template.id}
                      className={`template-item ${
                        selectedTemplate?.id === template.id ? "selected" : ""
                      }`}
                      onClick={() => setSelectedTemplate(template)}
                    >
                      <div className="template-name">{template.name}</div>
                      <div className="template-meta">
                        {template.type} •{" "}
                        {new Date(template.created_at).toLocaleDateString()}
                      </div>
                      <div className="template-actions">
                        <button
                          className="delete-template-btn"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTemplate(template.id);
                          }}
                        >
                          Delete
                        </button>
                        <button
                          className="toggle-default-btn ml-2 px-3 py-1 text-sm text-white bg-indigo-600 rounded hover:bg-indigo-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (template.is_default) {
                              removeDefaultTemplate(template.id);
                            } else {
                              setDefaultTemplate(template.id);
                            }
                          }}
                        >
                          {template.is_default
                            ? "Remove Default"
                            : "Set as Default"}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <div className="modal-actions">
                <button
                  onClick={() => setShowLoadModal(false)}
                  className="modal-btn cancel"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleLoadTemplate(selectedTemplate)}
                  disabled={!selectedTemplate}
                  className="modal-btn confirm"
                >
                  Load
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DndContext>
  );
};

export default InvoiceTemplateGenerator;
