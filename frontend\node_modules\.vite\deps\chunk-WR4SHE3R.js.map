{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/inheritsLoose.js", "../../react-transition-group/esm/CSSTransition.js", "../../dom-helpers/esm/hasClass.js", "../../dom-helpers/esm/addClass.js", "../../dom-helpers/esm/removeClass.js", "../../react-transition-group/esm/Transition.js", "../../react-transition-group/esm/config.js", "../../react-transition-group/esm/utils/PropTypes.js", "../../react-transition-group/esm/TransitionGroupContext.js", "../../react-transition-group/esm/utils/reflow.js", "../../react-transition-group/esm/ReplaceTransition.js", "../../react-transition-group/esm/TransitionGroup.js", "../../react-transition-group/esm/utils/ChildMapping.js", "../../react-transition-group/esm/SwitchTransition.js"], "sourcesContent": ["import setPrototypeOf from \"./setPrototypeOf.js\";\r\nfunction _inheritsLoose(t, o) {\r\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\r\n}\r\nexport { _inheritsLoose as default };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\r\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\r\nimport PropTypes from 'prop-types';\r\nimport addOneClass from 'dom-helpers/addClass';\r\nimport removeOneClass from 'dom-helpers/removeClass';\r\nimport React from 'react';\r\nimport Transition from './Transition';\r\nimport { classNamesShape } from './utils/PropTypes';\r\nimport { forceReflow } from './utils/reflow';\r\n\r\nvar _addClass = function addClass(node, classes) {\r\n  return node && classes && classes.split(' ').forEach(function (c) {\r\n    return addOneClass(node, c);\r\n  });\r\n};\r\n\r\nvar removeClass = function removeClass(node, classes) {\r\n  return node && classes && classes.split(' ').forEach(function (c) {\r\n    return removeOneClass(node, c);\r\n  });\r\n};\r\n/**\r\n * A transition component inspired by the excellent\r\n * [ng-animate](https://docs.angularjs.org/api/ngAnimate) library, you should\r\n * use it if you're using CSS transitions or animations. It's built upon the\r\n * [`Transition`](https://reactcommunity.org/react-transition-group/transition)\r\n * component, so it inherits all of its props.\r\n *\r\n * `CSSTransition` applies a pair of class names during the `appear`, `enter`,\r\n * and `exit` states of the transition. The first class is applied and then a\r\n * second `*-active` class in order to activate the CSS transition. After the\r\n * transition, matching `*-done` class names are applied to persist the\r\n * transition state.\r\n *\r\n * ```jsx\r\n * function App() {\r\n *   const [inProp, setInProp] = useState(false);\r\n *   return (\r\n *     <div>\r\n *       <CSSTransition in={inProp} timeout={200} classNames=\"my-node\">\r\n *         <div>\r\n *           {\"I'll receive my-node-* classes\"}\r\n *         </div>\r\n *       </CSSTransition>\r\n *       <button type=\"button\" onClick={() => setInProp(true)}>\r\n *         Click to Enter\r\n *       </button>\r\n *     </div>\r\n *   );\r\n * }\r\n * ```\r\n *\r\n * When the `in` prop is set to `true`, the child component will first receive\r\n * the class `example-enter`, then the `example-enter-active` will be added in\r\n * the next tick. `CSSTransition` [forces a\r\n * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)\r\n * between before adding the `example-enter-active`. This is an important trick\r\n * because it allows us to transition between `example-enter` and\r\n * `example-enter-active` even though they were added immediately one after\r\n * another. Most notably, this is what makes it possible for us to animate\r\n * _appearance_.\r\n *\r\n * ```css\r\n * .my-node-enter {\r\n *   opacity: 0;\r\n * }\r\n * .my-node-enter-active {\r\n *   opacity: 1;\r\n *   transition: opacity 200ms;\r\n * }\r\n * .my-node-exit {\r\n *   opacity: 1;\r\n * }\r\n * .my-node-exit-active {\r\n *   opacity: 0;\r\n *   transition: opacity 200ms;\r\n * }\r\n * ```\r\n *\r\n * `*-active` classes represent which styles you want to animate **to**, so it's\r\n * important to add `transition` declaration only to them, otherwise transitions\r\n * might not behave as intended! This might not be obvious when the transitions\r\n * are symmetrical, i.e. when `*-enter-active` is the same as `*-exit`, like in\r\n * the example above (minus `transition`), but it becomes apparent in more\r\n * complex transitions.\r\n *\r\n * **Note**: If you're using the\r\n * [`appear`](http://reactcommunity.org/react-transition-group/transition#Transition-prop-appear)\r\n * prop, make sure to define styles for `.appear-*` classes as well.\r\n */\r\n\r\n\r\nvar CSSTransition = /*#__PURE__*/function (_React$Component) {\r\n  _inheritsLoose(CSSTransition, _React$Component);\r\n\r\n  function CSSTransition() {\r\n    var _this;\r\n\r\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n      args[_key] = arguments[_key];\r\n    }\r\n\r\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\r\n    _this.appliedClasses = {\r\n      appear: {},\r\n      enter: {},\r\n      exit: {}\r\n    };\r\n\r\n    _this.onEnter = function (maybeNode, maybeAppearing) {\r\n      var _this$resolveArgument = _this.resolveArguments(maybeNode, maybeAppearing),\r\n          node = _this$resolveArgument[0],\r\n          appearing = _this$resolveArgument[1];\r\n\r\n      _this.removeClasses(node, 'exit');\r\n\r\n      _this.addClass(node, appearing ? 'appear' : 'enter', 'base');\r\n\r\n      if (_this.props.onEnter) {\r\n        _this.props.onEnter(maybeNode, maybeAppearing);\r\n      }\r\n    };\r\n\r\n    _this.onEntering = function (maybeNode, maybeAppearing) {\r\n      var _this$resolveArgument2 = _this.resolveArguments(maybeNode, maybeAppearing),\r\n          node = _this$resolveArgument2[0],\r\n          appearing = _this$resolveArgument2[1];\r\n\r\n      var type = appearing ? 'appear' : 'enter';\r\n\r\n      _this.addClass(node, type, 'active');\r\n\r\n      if (_this.props.onEntering) {\r\n        _this.props.onEntering(maybeNode, maybeAppearing);\r\n      }\r\n    };\r\n\r\n    _this.onEntered = function (maybeNode, maybeAppearing) {\r\n      var _this$resolveArgument3 = _this.resolveArguments(maybeNode, maybeAppearing),\r\n          node = _this$resolveArgument3[0],\r\n          appearing = _this$resolveArgument3[1];\r\n\r\n      var type = appearing ? 'appear' : 'enter';\r\n\r\n      _this.removeClasses(node, type);\r\n\r\n      _this.addClass(node, type, 'done');\r\n\r\n      if (_this.props.onEntered) {\r\n        _this.props.onEntered(maybeNode, maybeAppearing);\r\n      }\r\n    };\r\n\r\n    _this.onExit = function (maybeNode) {\r\n      var _this$resolveArgument4 = _this.resolveArguments(maybeNode),\r\n          node = _this$resolveArgument4[0];\r\n\r\n      _this.removeClasses(node, 'appear');\r\n\r\n      _this.removeClasses(node, 'enter');\r\n\r\n      _this.addClass(node, 'exit', 'base');\r\n\r\n      if (_this.props.onExit) {\r\n        _this.props.onExit(maybeNode);\r\n      }\r\n    };\r\n\r\n    _this.onExiting = function (maybeNode) {\r\n      var _this$resolveArgument5 = _this.resolveArguments(maybeNode),\r\n          node = _this$resolveArgument5[0];\r\n\r\n      _this.addClass(node, 'exit', 'active');\r\n\r\n      if (_this.props.onExiting) {\r\n        _this.props.onExiting(maybeNode);\r\n      }\r\n    };\r\n\r\n    _this.onExited = function (maybeNode) {\r\n      var _this$resolveArgument6 = _this.resolveArguments(maybeNode),\r\n          node = _this$resolveArgument6[0];\r\n\r\n      _this.removeClasses(node, 'exit');\r\n\r\n      _this.addClass(node, 'exit', 'done');\r\n\r\n      if (_this.props.onExited) {\r\n        _this.props.onExited(maybeNode);\r\n      }\r\n    };\r\n\r\n    _this.resolveArguments = function (maybeNode, maybeAppearing) {\r\n      return _this.props.nodeRef ? [_this.props.nodeRef.current, maybeNode] // here `maybeNode` is actually `appearing`\r\n      : [maybeNode, maybeAppearing];\r\n    };\r\n\r\n    _this.getClassNames = function (type) {\r\n      var classNames = _this.props.classNames;\r\n      var isStringClassNames = typeof classNames === 'string';\r\n      var prefix = isStringClassNames && classNames ? classNames + \"-\" : '';\r\n      var baseClassName = isStringClassNames ? \"\" + prefix + type : classNames[type];\r\n      var activeClassName = isStringClassNames ? baseClassName + \"-active\" : classNames[type + \"Active\"];\r\n      var doneClassName = isStringClassNames ? baseClassName + \"-done\" : classNames[type + \"Done\"];\r\n      return {\r\n        baseClassName: baseClassName,\r\n        activeClassName: activeClassName,\r\n        doneClassName: doneClassName\r\n      };\r\n    };\r\n\r\n    return _this;\r\n  }\r\n\r\n  var _proto = CSSTransition.prototype;\r\n\r\n  _proto.addClass = function addClass(node, type, phase) {\r\n    var className = this.getClassNames(type)[phase + \"ClassName\"];\r\n\r\n    var _this$getClassNames = this.getClassNames('enter'),\r\n        doneClassName = _this$getClassNames.doneClassName;\r\n\r\n    if (type === 'appear' && phase === 'done' && doneClassName) {\r\n      className += \" \" + doneClassName;\r\n    } // This is to force a repaint,\r\n    // which is necessary in order to transition styles when adding a class name.\r\n\r\n\r\n    if (phase === 'active') {\r\n      if (node) forceReflow(node);\r\n    }\r\n\r\n    if (className) {\r\n      this.appliedClasses[type][phase] = className;\r\n\r\n      _addClass(node, className);\r\n    }\r\n  };\r\n\r\n  _proto.removeClasses = function removeClasses(node, type) {\r\n    var _this$appliedClasses$ = this.appliedClasses[type],\r\n        baseClassName = _this$appliedClasses$.base,\r\n        activeClassName = _this$appliedClasses$.active,\r\n        doneClassName = _this$appliedClasses$.done;\r\n    this.appliedClasses[type] = {};\r\n\r\n    if (baseClassName) {\r\n      removeClass(node, baseClassName);\r\n    }\r\n\r\n    if (activeClassName) {\r\n      removeClass(node, activeClassName);\r\n    }\r\n\r\n    if (doneClassName) {\r\n      removeClass(node, doneClassName);\r\n    }\r\n  };\r\n\r\n  _proto.render = function render() {\r\n    var _this$props = this.props,\r\n        _ = _this$props.classNames,\r\n        props = _objectWithoutPropertiesLoose(_this$props, [\"classNames\"]);\r\n\r\n    return /*#__PURE__*/React.createElement(Transition, _extends({}, props, {\r\n      onEnter: this.onEnter,\r\n      onEntered: this.onEntered,\r\n      onEntering: this.onEntering,\r\n      onExit: this.onExit,\r\n      onExiting: this.onExiting,\r\n      onExited: this.onExited\r\n    }));\r\n  };\r\n\r\n  return CSSTransition;\r\n}(React.Component);\r\n\r\nCSSTransition.defaultProps = {\r\n  classNames: ''\r\n};\r\nCSSTransition.propTypes = process.env.NODE_ENV !== \"production\" ? _extends({}, Transition.propTypes, {\r\n  /**\r\n   * The animation classNames applied to the component as it appears, enters,\r\n   * exits or has finished the transition. A single name can be provided, which\r\n   * will be suffixed for each stage, e.g. `classNames=\"fade\"` applies:\r\n   *\r\n   * - `fade-appear`, `fade-appear-active`, `fade-appear-done`\r\n   * - `fade-enter`, `fade-enter-active`, `fade-enter-done`\r\n   * - `fade-exit`, `fade-exit-active`, `fade-exit-done`\r\n   *\r\n   * A few details to note about how these classes are applied:\r\n   *\r\n   * 1. They are _joined_ with the ones that are already defined on the child\r\n   *    component, so if you want to add some base styles, you can use\r\n   *    `className` without worrying that it will be overridden.\r\n   *\r\n   * 2. If the transition component mounts with `in={false}`, no classes are\r\n   *    applied yet. You might be expecting `*-exit-done`, but if you think\r\n   *    about it, a component cannot finish exiting if it hasn't entered yet.\r\n   *\r\n   * 2. `fade-appear-done` and `fade-enter-done` will _both_ be applied. This\r\n   *    allows you to define different behavior for when appearing is done and\r\n   *    when regular entering is done, using selectors like\r\n   *    `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply\r\n   *    an epic entrance animation when element first appears in the DOM using\r\n   *    [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can\r\n   *    simply use `fade-enter-done` for defining both cases.\r\n   *\r\n   * Each individual classNames can also be specified independently like:\r\n   *\r\n   * ```js\r\n   * classNames={{\r\n   *  appear: 'my-appear',\r\n   *  appearActive: 'my-active-appear',\r\n   *  appearDone: 'my-done-appear',\r\n   *  enter: 'my-enter',\r\n   *  enterActive: 'my-active-enter',\r\n   *  enterDone: 'my-done-enter',\r\n   *  exit: 'my-exit',\r\n   *  exitActive: 'my-active-exit',\r\n   *  exitDone: 'my-done-exit',\r\n   * }}\r\n   * ```\r\n   *\r\n   * If you want to set these classes using CSS Modules:\r\n   *\r\n   * ```js\r\n   * import styles from './styles.css';\r\n   * ```\r\n   *\r\n   * you might want to use camelCase in your CSS file, that way could simply\r\n   * spread them instead of listing them one by one:\r\n   *\r\n   * ```js\r\n   * classNames={{ ...styles }}\r\n   * ```\r\n   *\r\n   * @type {string | {\r\n   *  appear?: string,\r\n   *  appearActive?: string,\r\n   *  appearDone?: string,\r\n   *  enter?: string,\r\n   *  enterActive?: string,\r\n   *  enterDone?: string,\r\n   *  exit?: string,\r\n   *  exitActive?: string,\r\n   *  exitDone?: string,\r\n   * }}\r\n   */\r\n  classNames: classNamesShape,\r\n\r\n  /**\r\n   * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is\r\n   * applied.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement, isAppearing: bool)\r\n   */\r\n  onEnter: PropTypes.func,\r\n\r\n  /**\r\n   * A `<Transition>` callback fired immediately after the 'enter-active' or\r\n   * 'appear-active' class is applied.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement, isAppearing: bool)\r\n   */\r\n  onEntering: PropTypes.func,\r\n\r\n  /**\r\n   * A `<Transition>` callback fired immediately after the 'enter' or\r\n   * 'appear' classes are **removed** and the `done` class is added to the DOM node.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement, isAppearing: bool)\r\n   */\r\n  onEntered: PropTypes.func,\r\n\r\n  /**\r\n   * A `<Transition>` callback fired immediately after the 'exit' class is\r\n   * applied.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\r\n   *\r\n   * @type Function(node: HtmlElement)\r\n   */\r\n  onExit: PropTypes.func,\r\n\r\n  /**\r\n   * A `<Transition>` callback fired immediately after the 'exit-active' is applied.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\r\n   *\r\n   * @type Function(node: HtmlElement)\r\n   */\r\n  onExiting: PropTypes.func,\r\n\r\n  /**\r\n   * A `<Transition>` callback fired immediately after the 'exit' classes\r\n   * are **removed** and the `exit-done` class is added to the DOM node.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\r\n   *\r\n   * @type Function(node: HtmlElement)\r\n   */\r\n  onExited: PropTypes.func\r\n}) : {};\r\nexport default CSSTransition;", "/**\r\n * Checks if a given element has a CSS class.\r\n * \r\n * @param element the element\r\n * @param className the CSS class name\r\n */\r\nexport default function hasClass(element, className) {\r\n  if (element.classList) return !!className && element.classList.contains(className);\r\n  return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\r\n}", "import hasClass from './hasClass';\r\n/**\r\n * Adds a CSS class to a given element.\r\n * \r\n * @param element the element\r\n * @param className the CSS class name\r\n */\r\n\r\nexport default function addClass(element, className) {\r\n  if (element.classList) element.classList.add(className);else if (!hasClass(element, className)) if (typeof element.className === 'string') element.className = element.className + \" \" + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + \" \" + className);\r\n}", "function replaceClassName(origClass, classToRemove) {\r\n  return origClass.replace(new RegExp(\"(^|\\\\s)\" + classToRemove + \"(?:\\\\s|$)\", 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\r\n}\r\n/**\r\n * Removes a CSS class from a given element.\r\n * \r\n * @param element the element\r\n * @param className the CSS class name\r\n */\r\n\r\n\r\nexport default function removeClass(element, className) {\r\n  if (element.classList) {\r\n    element.classList.remove(className);\r\n  } else if (typeof element.className === 'string') {\r\n    element.className = replaceClassName(element.className, className);\r\n  } else {\r\n    element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\r\n  }\r\n}", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\r\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\r\nimport PropTypes from 'prop-types';\r\nimport React from 'react';\r\nimport ReactDOM from 'react-dom';\r\nimport config from './config';\r\nimport { timeoutsShape } from './utils/PropTypes';\r\nimport TransitionGroupContext from './TransitionGroupContext';\r\nimport { forceReflow } from './utils/reflow';\r\nexport var UNMOUNTED = 'unmounted';\r\nexport var EXITED = 'exited';\r\nexport var ENTERING = 'entering';\r\nexport var ENTERED = 'entered';\r\nexport var EXITING = 'exiting';\r\n/**\r\n * The Transition component lets you describe a transition from one component\r\n * state to another _over time_ with a simple declarative API. Most commonly\r\n * it's used to animate the mounting and unmounting of a component, but can also\r\n * be used to describe in-place transition states as well.\r\n *\r\n * ---\r\n *\r\n * **Note**: `Transition` is a platform-agnostic base component. If you're using\r\n * transitions in CSS, you'll probably want to use\r\n * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)\r\n * instead. It inherits all the features of `Transition`, but contains\r\n * additional features necessary to play nice with CSS transitions (hence the\r\n * name of the component).\r\n *\r\n * ---\r\n *\r\n * By default the `Transition` component does not alter the behavior of the\r\n * component it renders, it only tracks \"enter\" and \"exit\" states for the\r\n * components. It's up to you to give meaning and effect to those states. For\r\n * example we can add styles to a component when it enters or exits:\r\n *\r\n * ```jsx\r\n * import { Transition } from 'react-transition-group';\r\n *\r\n * const duration = 300;\r\n *\r\n * const defaultStyle = {\r\n *   transition: `opacity ${duration}ms ease-in-out`,\r\n *   opacity: 0,\r\n * }\r\n *\r\n * const transitionStyles = {\r\n *   entering: { opacity: 1 },\r\n *   entered:  { opacity: 1 },\r\n *   exiting:  { opacity: 0 },\r\n *   exited:  { opacity: 0 },\r\n * };\r\n *\r\n * const Fade = ({ in: inProp }) => (\r\n *   <Transition in={inProp} timeout={duration}>\r\n *     {state => (\r\n *       <div style={{\r\n *         ...defaultStyle,\r\n *         ...transitionStyles[state]\r\n *       }}>\r\n *         I'm a fade Transition!\r\n *       </div>\r\n *     )}\r\n *   </Transition>\r\n * );\r\n * ```\r\n *\r\n * There are 4 main states a Transition can be in:\r\n *  - `'entering'`\r\n *  - `'entered'`\r\n *  - `'exiting'`\r\n *  - `'exited'`\r\n *\r\n * Transition state is toggled via the `in` prop. When `true` the component\r\n * begins the \"Enter\" stage. During this stage, the component will shift from\r\n * its current transition state, to `'entering'` for the duration of the\r\n * transition and then to the `'entered'` stage once it's complete. Let's take\r\n * the following example (we'll use the\r\n * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):\r\n *\r\n * ```jsx\r\n * function App() {\r\n *   const [inProp, setInProp] = useState(false);\r\n *   return (\r\n *     <div>\r\n *       <Transition in={inProp} timeout={500}>\r\n *         {state => (\r\n *           // ...\r\n *         )}\r\n *       </Transition>\r\n *       <button onClick={() => setInProp(true)}>\r\n *         Click to Enter\r\n *       </button>\r\n *     </div>\r\n *   );\r\n * }\r\n * ```\r\n *\r\n * When the button is clicked the component will shift to the `'entering'` state\r\n * and stay there for 500ms (the value of `timeout`) before it finally switches\r\n * to `'entered'`.\r\n *\r\n * When `in` is `false` the same thing happens except the state moves from\r\n * `'exiting'` to `'exited'`.\r\n */\r\n\r\nvar Transition = /*#__PURE__*/function (_React$Component) {\r\n  _inheritsLoose(Transition, _React$Component);\r\n\r\n  function Transition(props, context) {\r\n    var _this;\r\n\r\n    _this = _React$Component.call(this, props, context) || this;\r\n    var parentGroup = context; // In the context of a TransitionGroup all enters are really appears\r\n\r\n    var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;\r\n    var initialStatus;\r\n    _this.appearStatus = null;\r\n\r\n    if (props.in) {\r\n      if (appear) {\r\n        initialStatus = EXITED;\r\n        _this.appearStatus = ENTERING;\r\n      } else {\r\n        initialStatus = ENTERED;\r\n      }\r\n    } else {\r\n      if (props.unmountOnExit || props.mountOnEnter) {\r\n        initialStatus = UNMOUNTED;\r\n      } else {\r\n        initialStatus = EXITED;\r\n      }\r\n    }\r\n\r\n    _this.state = {\r\n      status: initialStatus\r\n    };\r\n    _this.nextCallback = null;\r\n    return _this;\r\n  }\r\n\r\n  Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {\r\n    var nextIn = _ref.in;\r\n\r\n    if (nextIn && prevState.status === UNMOUNTED) {\r\n      return {\r\n        status: EXITED\r\n      };\r\n    }\r\n\r\n    return null;\r\n  } // getSnapshotBeforeUpdate(prevProps) {\r\n  //   let nextStatus = null\r\n  //   if (prevProps !== this.props) {\r\n  //     const { status } = this.state\r\n  //     if (this.props.in) {\r\n  //       if (status !== ENTERING && status !== ENTERED) {\r\n  //         nextStatus = ENTERING\r\n  //       }\r\n  //     } else {\r\n  //       if (status === ENTERING || status === ENTERED) {\r\n  //         nextStatus = EXITING\r\n  //       }\r\n  //     }\r\n  //   }\r\n  //   return { nextStatus }\r\n  // }\r\n  ;\r\n\r\n  var _proto = Transition.prototype;\r\n\r\n  _proto.componentDidMount = function componentDidMount() {\r\n    this.updateStatus(true, this.appearStatus);\r\n  };\r\n\r\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\r\n    var nextStatus = null;\r\n\r\n    if (prevProps !== this.props) {\r\n      var status = this.state.status;\r\n\r\n      if (this.props.in) {\r\n        if (status !== ENTERING && status !== ENTERED) {\r\n          nextStatus = ENTERING;\r\n        }\r\n      } else {\r\n        if (status === ENTERING || status === ENTERED) {\r\n          nextStatus = EXITING;\r\n        }\r\n      }\r\n    }\r\n\r\n    this.updateStatus(false, nextStatus);\r\n  };\r\n\r\n  _proto.componentWillUnmount = function componentWillUnmount() {\r\n    this.cancelNextCallback();\r\n  };\r\n\r\n  _proto.getTimeouts = function getTimeouts() {\r\n    var timeout = this.props.timeout;\r\n    var exit, enter, appear;\r\n    exit = enter = appear = timeout;\r\n\r\n    if (timeout != null && typeof timeout !== 'number') {\r\n      exit = timeout.exit;\r\n      enter = timeout.enter; // TODO: remove fallback for next major\r\n\r\n      appear = timeout.appear !== undefined ? timeout.appear : enter;\r\n    }\r\n\r\n    return {\r\n      exit: exit,\r\n      enter: enter,\r\n      appear: appear\r\n    };\r\n  };\r\n\r\n  _proto.updateStatus = function updateStatus(mounting, nextStatus) {\r\n    if (mounting === void 0) {\r\n      mounting = false;\r\n    }\r\n\r\n    if (nextStatus !== null) {\r\n      // nextStatus will always be ENTERING or EXITING.\r\n      this.cancelNextCallback();\r\n\r\n      if (nextStatus === ENTERING) {\r\n        if (this.props.unmountOnExit || this.props.mountOnEnter) {\r\n          var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this); // https://github.com/reactjs/react-transition-group/pull/749\r\n          // With unmountOnExit or mountOnEnter, the enter animation should happen at the transition between `exited` and `entering`.\r\n          // To make the animation happen,  we have to separate each rendering and avoid being processed as batched.\r\n\r\n          if (node) forceReflow(node);\r\n        }\r\n\r\n        this.performEnter(mounting);\r\n      } else {\r\n        this.performExit();\r\n      }\r\n    } else if (this.props.unmountOnExit && this.state.status === EXITED) {\r\n      this.setState({\r\n        status: UNMOUNTED\r\n      });\r\n    }\r\n  };\r\n\r\n  _proto.performEnter = function performEnter(mounting) {\r\n    var _this2 = this;\r\n\r\n    var enter = this.props.enter;\r\n    var appearing = this.context ? this.context.isMounting : mounting;\r\n\r\n    var _ref2 = this.props.nodeRef ? [appearing] : [ReactDOM.findDOMNode(this), appearing],\r\n        maybeNode = _ref2[0],\r\n        maybeAppearing = _ref2[1];\r\n\r\n    var timeouts = this.getTimeouts();\r\n    var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED\r\n    // if we are mounting and running this it means appear _must_ be set\r\n\r\n    if (!mounting && !enter || config.disabled) {\r\n      this.safeSetState({\r\n        status: ENTERED\r\n      }, function () {\r\n        _this2.props.onEntered(maybeNode);\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.props.onEnter(maybeNode, maybeAppearing);\r\n    this.safeSetState({\r\n      status: ENTERING\r\n    }, function () {\r\n      _this2.props.onEntering(maybeNode, maybeAppearing);\r\n\r\n      _this2.onTransitionEnd(enterTimeout, function () {\r\n        _this2.safeSetState({\r\n          status: ENTERED\r\n        }, function () {\r\n          _this2.props.onEntered(maybeNode, maybeAppearing);\r\n        });\r\n      });\r\n    });\r\n  };\r\n\r\n  _proto.performExit = function performExit() {\r\n    var _this3 = this;\r\n\r\n    var exit = this.props.exit;\r\n    var timeouts = this.getTimeouts();\r\n    var maybeNode = this.props.nodeRef ? undefined : ReactDOM.findDOMNode(this); // no exit animation skip right to EXITED\r\n\r\n    if (!exit || config.disabled) {\r\n      this.safeSetState({\r\n        status: EXITED\r\n      }, function () {\r\n        _this3.props.onExited(maybeNode);\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.props.onExit(maybeNode);\r\n    this.safeSetState({\r\n      status: EXITING\r\n    }, function () {\r\n      _this3.props.onExiting(maybeNode);\r\n\r\n      _this3.onTransitionEnd(timeouts.exit, function () {\r\n        _this3.safeSetState({\r\n          status: EXITED\r\n        }, function () {\r\n          _this3.props.onExited(maybeNode);\r\n        });\r\n      });\r\n    });\r\n  };\r\n\r\n  _proto.cancelNextCallback = function cancelNextCallback() {\r\n    if (this.nextCallback !== null) {\r\n      this.nextCallback.cancel();\r\n      this.nextCallback = null;\r\n    }\r\n  };\r\n\r\n  _proto.safeSetState = function safeSetState(nextState, callback) {\r\n    // This shouldn't be necessary, but there are weird race conditions with\r\n    // setState callbacks and unmounting in testing, so always make sure that\r\n    // we can cancel any pending setState callbacks after we unmount.\r\n    callback = this.setNextCallback(callback);\r\n    this.setState(nextState, callback);\r\n  };\r\n\r\n  _proto.setNextCallback = function setNextCallback(callback) {\r\n    var _this4 = this;\r\n\r\n    var active = true;\r\n\r\n    this.nextCallback = function (event) {\r\n      if (active) {\r\n        active = false;\r\n        _this4.nextCallback = null;\r\n        callback(event);\r\n      }\r\n    };\r\n\r\n    this.nextCallback.cancel = function () {\r\n      active = false;\r\n    };\r\n\r\n    return this.nextCallback;\r\n  };\r\n\r\n  _proto.onTransitionEnd = function onTransitionEnd(timeout, handler) {\r\n    this.setNextCallback(handler);\r\n    var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this);\r\n    var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\r\n\r\n    if (!node || doesNotHaveTimeoutOrListener) {\r\n      setTimeout(this.nextCallback, 0);\r\n      return;\r\n    }\r\n\r\n    if (this.props.addEndListener) {\r\n      var _ref3 = this.props.nodeRef ? [this.nextCallback] : [node, this.nextCallback],\r\n          maybeNode = _ref3[0],\r\n          maybeNextCallback = _ref3[1];\r\n\r\n      this.props.addEndListener(maybeNode, maybeNextCallback);\r\n    }\r\n\r\n    if (timeout != null) {\r\n      setTimeout(this.nextCallback, timeout);\r\n    }\r\n  };\r\n\r\n  _proto.render = function render() {\r\n    var status = this.state.status;\r\n\r\n    if (status === UNMOUNTED) {\r\n      return null;\r\n    }\r\n\r\n    var _this$props = this.props,\r\n        children = _this$props.children,\r\n        _in = _this$props.in,\r\n        _mountOnEnter = _this$props.mountOnEnter,\r\n        _unmountOnExit = _this$props.unmountOnExit,\r\n        _appear = _this$props.appear,\r\n        _enter = _this$props.enter,\r\n        _exit = _this$props.exit,\r\n        _timeout = _this$props.timeout,\r\n        _addEndListener = _this$props.addEndListener,\r\n        _onEnter = _this$props.onEnter,\r\n        _onEntering = _this$props.onEntering,\r\n        _onEntered = _this$props.onEntered,\r\n        _onExit = _this$props.onExit,\r\n        _onExiting = _this$props.onExiting,\r\n        _onExited = _this$props.onExited,\r\n        _nodeRef = _this$props.nodeRef,\r\n        childProps = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\", \"mountOnEnter\", \"unmountOnExit\", \"appear\", \"enter\", \"exit\", \"timeout\", \"addEndListener\", \"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"nodeRef\"]);\r\n\r\n    return (\r\n      /*#__PURE__*/\r\n      // allows for nested Transitions\r\n      React.createElement(TransitionGroupContext.Provider, {\r\n        value: null\r\n      }, typeof children === 'function' ? children(status, childProps) : React.cloneElement(React.Children.only(children), childProps))\r\n    );\r\n  };\r\n\r\n  return Transition;\r\n}(React.Component);\r\n\r\nTransition.contextType = TransitionGroupContext;\r\nTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\r\n  /**\r\n   * A React reference to DOM element that need to transition:\r\n   * https://stackoverflow.com/a/51127130/4671932\r\n   *\r\n   *   - When `nodeRef` prop is used, `node` is not passed to callback functions\r\n   *      (e.g. `onEnter`) because user already has direct access to the node.\r\n   *   - When changing `key` prop of `Transition` in a `TransitionGroup` a new\r\n   *     `nodeRef` need to be provided to `Transition` with changed `key` prop\r\n   *     (see\r\n   *     [test/CSSTransition-test.js](https://github.com/reactjs/react-transition-group/blob/13435f897b3ab71f6e19d724f145596f5910581c/test/CSSTransition-test.js#L362-L437)).\r\n   */\r\n  nodeRef: PropTypes.shape({\r\n    current: typeof Element === 'undefined' ? PropTypes.any : function (propValue, key, componentName, location, propFullName, secret) {\r\n      var value = propValue[key];\r\n      return PropTypes.instanceOf(value && 'ownerDocument' in value ? value.ownerDocument.defaultView.Element : Element)(propValue, key, componentName, location, propFullName, secret);\r\n    }\r\n  }),\r\n\r\n  /**\r\n   * A `function` child can be used instead of a React element. This function is\r\n   * called with the current transition status (`'entering'`, `'entered'`,\r\n   * `'exiting'`, `'exited'`), which can be used to apply context\r\n   * specific props to a component.\r\n   *\r\n   * ```jsx\r\n   * <Transition in={this.state.in} timeout={150}>\r\n   *   {state => (\r\n   *     <MyComponent className={`fade fade-${state}`} />\r\n   *   )}\r\n   * </Transition>\r\n   * ```\r\n   */\r\n  children: PropTypes.oneOfType([PropTypes.func.isRequired, PropTypes.element.isRequired]).isRequired,\r\n\r\n  /**\r\n   * Show the component; triggers the enter or exit states\r\n   */\r\n  in: PropTypes.bool,\r\n\r\n  /**\r\n   * By default the child component is mounted immediately along with\r\n   * the parent `Transition` component. If you want to \"lazy mount\" the component on the\r\n   * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay\r\n   * mounted, even on \"exited\", unless you also specify `unmountOnExit`.\r\n   */\r\n  mountOnEnter: PropTypes.bool,\r\n\r\n  /**\r\n   * By default the child component stays mounted after it reaches the `'exited'` state.\r\n   * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.\r\n   */\r\n  unmountOnExit: PropTypes.bool,\r\n\r\n  /**\r\n   * By default the child component does not perform the enter transition when\r\n   * it first mounts, regardless of the value of `in`. If you want this\r\n   * behavior, set both `appear` and `in` to `true`.\r\n   *\r\n   * > **Note**: there are no special appear states like `appearing`/`appeared`, this prop\r\n   * > only adds an additional enter transition. However, in the\r\n   * > `<CSSTransition>` component that first enter transition does result in\r\n   * > additional `.appear-*` classes, that way you can choose to style it\r\n   * > differently.\r\n   */\r\n  appear: PropTypes.bool,\r\n\r\n  /**\r\n   * Enable or disable enter transitions.\r\n   */\r\n  enter: PropTypes.bool,\r\n\r\n  /**\r\n   * Enable or disable exit transitions.\r\n   */\r\n  exit: PropTypes.bool,\r\n\r\n  /**\r\n   * The duration of the transition, in milliseconds.\r\n   * Required unless `addEndListener` is provided.\r\n   *\r\n   * You may specify a single timeout for all transitions:\r\n   *\r\n   * ```jsx\r\n   * timeout={500}\r\n   * ```\r\n   *\r\n   * or individually:\r\n   *\r\n   * ```jsx\r\n   * timeout={{\r\n   *  appear: 500,\r\n   *  enter: 300,\r\n   *  exit: 500,\r\n   * }}\r\n   * ```\r\n   *\r\n   * - `appear` defaults to the value of `enter`\r\n   * - `enter` defaults to `0`\r\n   * - `exit` defaults to `0`\r\n   *\r\n   * @type {number | { enter?: number, exit?: number, appear?: number }}\r\n   */\r\n  timeout: function timeout(props) {\r\n    var pt = timeoutsShape;\r\n    if (!props.addEndListener) pt = pt.isRequired;\r\n\r\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\r\n      args[_key - 1] = arguments[_key];\r\n    }\r\n\r\n    return pt.apply(void 0, [props].concat(args));\r\n  },\r\n\r\n  /**\r\n   * Add a custom transition end trigger. Called with the transitioning\r\n   * DOM node and a `done` callback. Allows for more fine grained transition end\r\n   * logic. Timeouts are still used as a fallback if provided.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * ```jsx\r\n   * addEndListener={(node, done) => {\r\n   *   // use the css transitionend event to mark the finish of a transition\r\n   *   node.addEventListener('transitionend', done, false);\r\n   * }}\r\n   * ```\r\n   */\r\n  addEndListener: PropTypes.func,\r\n\r\n  /**\r\n   * Callback fired before the \"entering\" status is applied. An extra parameter\r\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\r\n   */\r\n  onEnter: PropTypes.func,\r\n\r\n  /**\r\n   * Callback fired after the \"entering\" status is applied. An extra parameter\r\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement, isAppearing: bool)\r\n   */\r\n  onEntering: PropTypes.func,\r\n\r\n  /**\r\n   * Callback fired after the \"entered\" status is applied. An extra parameter\r\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\r\n   */\r\n  onEntered: PropTypes.func,\r\n\r\n  /**\r\n   * Callback fired before the \"exiting\" status is applied.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement) -> void\r\n   */\r\n  onExit: PropTypes.func,\r\n\r\n  /**\r\n   * Callback fired after the \"exiting\" status is applied.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\r\n   *\r\n   * @type Function(node: HtmlElement) -> void\r\n   */\r\n  onExiting: PropTypes.func,\r\n\r\n  /**\r\n   * Callback fired after the \"exited\" status is applied.\r\n   *\r\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\r\n   *\r\n   * @type Function(node: HtmlElement) -> void\r\n   */\r\n  onExited: PropTypes.func\r\n} : {}; // Name the function so it is clearer in the documentation\r\n\r\nfunction noop() {}\r\n\r\nTransition.defaultProps = {\r\n  in: false,\r\n  mountOnEnter: false,\r\n  unmountOnExit: false,\r\n  appear: false,\r\n  enter: true,\r\n  exit: true,\r\n  onEnter: noop,\r\n  onEntering: noop,\r\n  onEntered: noop,\r\n  onExit: noop,\r\n  onExiting: noop,\r\n  onExited: noop\r\n};\r\nTransition.UNMOUNTED = UNMOUNTED;\r\nTransition.EXITED = EXITED;\r\nTransition.ENTERING = ENTERING;\r\nTransition.ENTERED = ENTERED;\r\nTransition.EXITING = EXITING;\r\nexport default Transition;", "export default {\r\n  disabled: false\r\n};", "import PropTypes from 'prop-types';\r\nexport var timeoutsShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\r\n  enter: PropTypes.number,\r\n  exit: PropTypes.number,\r\n  appear: PropTypes.number\r\n}).isRequired]) : null;\r\nexport var classNamesShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.string, PropTypes.shape({\r\n  enter: PropTypes.string,\r\n  exit: PropTypes.string,\r\n  active: PropTypes.string\r\n}), PropTypes.shape({\r\n  enter: PropTypes.string,\r\n  enterDone: PropTypes.string,\r\n  enterActive: PropTypes.string,\r\n  exit: PropTypes.string,\r\n  exitDone: PropTypes.string,\r\n  exitActive: PropTypes.string\r\n})]) : null;", "import React from 'react';\r\nexport default React.createContext(null);", "export var forceReflow = function forceReflow(node) {\r\n  return node.scrollTop;\r\n};", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\r\nimport _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\r\nimport PropTypes from 'prop-types';\r\nimport React from 'react';\r\nimport ReactDOM from 'react-dom';\r\nimport TransitionGroup from './TransitionGroup';\r\n/**\r\n * The `<ReplaceTransition>` component is a specialized `Transition` component\r\n * that animates between two children.\r\n *\r\n * ```jsx\r\n * <ReplaceTransition in>\r\n *   <Fade><div>I appear first</div></Fade>\r\n *   <Fade><div>I replace the above</div></Fade>\r\n * </ReplaceTransition>\r\n * ```\r\n */\r\n\r\nvar ReplaceTransition = /*#__PURE__*/function (_React$Component) {\r\n  _inheritsLoose(ReplaceTransition, _React$Component);\r\n\r\n  function ReplaceTransition() {\r\n    var _this;\r\n\r\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n      _args[_key] = arguments[_key];\r\n    }\r\n\r\n    _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\r\n\r\n    _this.handleEnter = function () {\r\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\r\n        args[_key2] = arguments[_key2];\r\n      }\r\n\r\n      return _this.handleLifecycle('onEnter', 0, args);\r\n    };\r\n\r\n    _this.handleEntering = function () {\r\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\r\n        args[_key3] = arguments[_key3];\r\n      }\r\n\r\n      return _this.handleLifecycle('onEntering', 0, args);\r\n    };\r\n\r\n    _this.handleEntered = function () {\r\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\r\n        args[_key4] = arguments[_key4];\r\n      }\r\n\r\n      return _this.handleLifecycle('onEntered', 0, args);\r\n    };\r\n\r\n    _this.handleExit = function () {\r\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\r\n        args[_key5] = arguments[_key5];\r\n      }\r\n\r\n      return _this.handleLifecycle('onExit', 1, args);\r\n    };\r\n\r\n    _this.handleExiting = function () {\r\n      for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\r\n        args[_key6] = arguments[_key6];\r\n      }\r\n\r\n      return _this.handleLifecycle('onExiting', 1, args);\r\n    };\r\n\r\n    _this.handleExited = function () {\r\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\r\n        args[_key7] = arguments[_key7];\r\n      }\r\n\r\n      return _this.handleLifecycle('onExited', 1, args);\r\n    };\r\n\r\n    return _this;\r\n  }\r\n\r\n  var _proto = ReplaceTransition.prototype;\r\n\r\n  _proto.handleLifecycle = function handleLifecycle(handler, idx, originalArgs) {\r\n    var _child$props;\r\n\r\n    var children = this.props.children;\r\n    var child = React.Children.toArray(children)[idx];\r\n    if (child.props[handler]) (_child$props = child.props)[handler].apply(_child$props, originalArgs);\r\n\r\n    if (this.props[handler]) {\r\n      var maybeNode = child.props.nodeRef ? undefined : ReactDOM.findDOMNode(this);\r\n      this.props[handler](maybeNode);\r\n    }\r\n  };\r\n\r\n  _proto.render = function render() {\r\n    var _this$props = this.props,\r\n        children = _this$props.children,\r\n        inProp = _this$props.in,\r\n        props = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\"]);\r\n\r\n    var _React$Children$toArr = React.Children.toArray(children),\r\n        first = _React$Children$toArr[0],\r\n        second = _React$Children$toArr[1];\r\n\r\n    delete props.onEnter;\r\n    delete props.onEntering;\r\n    delete props.onEntered;\r\n    delete props.onExit;\r\n    delete props.onExiting;\r\n    delete props.onExited;\r\n    return /*#__PURE__*/React.createElement(TransitionGroup, props, inProp ? React.cloneElement(first, {\r\n      key: 'first',\r\n      onEnter: this.handleEnter,\r\n      onEntering: this.handleEntering,\r\n      onEntered: this.handleEntered\r\n    }) : React.cloneElement(second, {\r\n      key: 'second',\r\n      onEnter: this.handleExit,\r\n      onEntering: this.handleExiting,\r\n      onEntered: this.handleExited\r\n    }));\r\n  };\r\n\r\n  return ReplaceTransition;\r\n}(React.Component);\r\n\r\nReplaceTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\r\n  in: PropTypes.bool.isRequired,\r\n  children: function children(props, propName) {\r\n    if (React.Children.count(props[propName]) !== 2) return new Error(\"\\\"\" + propName + \"\\\" must be exactly two transition components.\");\r\n    return null;\r\n  }\r\n} : {};\r\nexport default ReplaceTransition;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\r\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\r\nimport PropTypes from 'prop-types';\r\nimport React from 'react';\r\nimport TransitionGroupContext from './TransitionGroupContext';\r\nimport { getChildMapping, getInitialChildMapping, getNextChildMapping } from './utils/ChildMapping';\r\n\r\nvar values = Object.values || function (obj) {\r\n  return Object.keys(obj).map(function (k) {\r\n    return obj[k];\r\n  });\r\n};\r\n\r\nvar defaultProps = {\r\n  component: 'div',\r\n  childFactory: function childFactory(child) {\r\n    return child;\r\n  }\r\n};\r\n/**\r\n * The `<TransitionGroup>` component manages a set of transition components\r\n * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\r\n * components, `<TransitionGroup>` is a state machine for managing the mounting\r\n * and unmounting of components over time.\r\n *\r\n * Consider the example below. As items are removed or added to the TodoList the\r\n * `in` prop is toggled automatically by the `<TransitionGroup>`.\r\n *\r\n * Note that `<TransitionGroup>`  does not define any animation behavior!\r\n * Exactly _how_ a list item animates is up to the individual transition\r\n * component. This means you can mix and match animations across different list\r\n * items.\r\n */\r\n\r\nvar TransitionGroup = /*#__PURE__*/function (_React$Component) {\r\n  _inheritsLoose(TransitionGroup, _React$Component);\r\n\r\n  function TransitionGroup(props, context) {\r\n    var _this;\r\n\r\n    _this = _React$Component.call(this, props, context) || this;\r\n\r\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_this)); // Initial children should all be entering, dependent on appear\r\n\r\n\r\n    _this.state = {\r\n      contextValue: {\r\n        isMounting: true\r\n      },\r\n      handleExited: handleExited,\r\n      firstRender: true\r\n    };\r\n    return _this;\r\n  }\r\n\r\n  var _proto = TransitionGroup.prototype;\r\n\r\n  _proto.componentDidMount = function componentDidMount() {\r\n    this.mounted = true;\r\n    this.setState({\r\n      contextValue: {\r\n        isMounting: false\r\n      }\r\n    });\r\n  };\r\n\r\n  _proto.componentWillUnmount = function componentWillUnmount() {\r\n    this.mounted = false;\r\n  };\r\n\r\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\r\n    var prevChildMapping = _ref.children,\r\n        handleExited = _ref.handleExited,\r\n        firstRender = _ref.firstRender;\r\n    return {\r\n      children: firstRender ? getInitialChildMapping(nextProps, handleExited) : getNextChildMapping(nextProps, prevChildMapping, handleExited),\r\n      firstRender: false\r\n    };\r\n  } // node is `undefined` when user provided `nodeRef` prop\r\n  ;\r\n\r\n  _proto.handleExited = function handleExited(child, node) {\r\n    var currentChildMapping = getChildMapping(this.props.children);\r\n    if (child.key in currentChildMapping) return;\r\n\r\n    if (child.props.onExited) {\r\n      child.props.onExited(node);\r\n    }\r\n\r\n    if (this.mounted) {\r\n      this.setState(function (state) {\r\n        var children = _extends({}, state.children);\r\n\r\n        delete children[child.key];\r\n        return {\r\n          children: children\r\n        };\r\n      });\r\n    }\r\n  };\r\n\r\n  _proto.render = function render() {\r\n    var _this$props = this.props,\r\n        Component = _this$props.component,\r\n        childFactory = _this$props.childFactory,\r\n        props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\r\n\r\n    var contextValue = this.state.contextValue;\r\n    var children = values(this.state.children).map(childFactory);\r\n    delete props.appear;\r\n    delete props.enter;\r\n    delete props.exit;\r\n\r\n    if (Component === null) {\r\n      return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\r\n        value: contextValue\r\n      }, children);\r\n    }\r\n\r\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\r\n      value: contextValue\r\n    }, /*#__PURE__*/React.createElement(Component, props, children));\r\n  };\r\n\r\n  return TransitionGroup;\r\n}(React.Component);\r\n\r\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\r\n  /**\r\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\r\n   * behavior by providing a `component` prop.\r\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\r\n   * you can pass in `component={null}`. This is useful if the wrapping div\r\n   * borks your css styles.\r\n   */\r\n  component: PropTypes.any,\r\n\r\n  /**\r\n   * A set of `<Transition>` components, that are toggled `in` and out as they\r\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\r\n   * remember to spread them through if you are wrapping the `<Transition>` as\r\n   * with our `<Fade>` example.\r\n   *\r\n   * While this component is meant for multiple `Transition` or `CSSTransition`\r\n   * children, sometimes you may want to have a single transition child with\r\n   * content that you want to be transitioned out and in when you change it\r\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\r\n   * the transition child as you change its content, this will cause\r\n   * `TransitionGroup` to transition the child out and back in.\r\n   */\r\n  children: PropTypes.node,\r\n\r\n  /**\r\n   * A convenience prop that enables or disables appear animations\r\n   * for all children. Note that specifying this will override any defaults set\r\n   * on individual children Transitions.\r\n   */\r\n  appear: PropTypes.bool,\r\n\r\n  /**\r\n   * A convenience prop that enables or disables enter animations\r\n   * for all children. Note that specifying this will override any defaults set\r\n   * on individual children Transitions.\r\n   */\r\n  enter: PropTypes.bool,\r\n\r\n  /**\r\n   * A convenience prop that enables or disables exit animations\r\n   * for all children. Note that specifying this will override any defaults set\r\n   * on individual children Transitions.\r\n   */\r\n  exit: PropTypes.bool,\r\n\r\n  /**\r\n   * You may need to apply reactive updates to a child as it is exiting.\r\n   * This is generally done by using `cloneElement` however in the case of an exiting\r\n   * child the element has already been removed and not accessible to the consumer.\r\n   *\r\n   * If you do need to update a child as it leaves you can provide a `childFactory`\r\n   * to wrap every child, even the ones that are leaving.\r\n   *\r\n   * @type Function(child: ReactElement) -> ReactElement\r\n   */\r\n  childFactory: PropTypes.func\r\n} : {};\r\nTransitionGroup.defaultProps = defaultProps;\r\nexport default TransitionGroup;", "import { Children, cloneElement, isValidElement } from 'react';\r\n/**\r\n * Given `this.props.children`, return an object mapping key to child.\r\n *\r\n * @param {*} children `this.props.children`\r\n * @return {object} Mapping of key to child\r\n */\r\n\r\nexport function getChildMapping(children, mapFn) {\r\n  var mapper = function mapper(child) {\r\n    return mapFn && isValidElement(child) ? mapFn(child) : child;\r\n  };\r\n\r\n  var result = Object.create(null);\r\n  if (children) Children.map(children, function (c) {\r\n    return c;\r\n  }).forEach(function (child) {\r\n    // run the map function here instead so that the key is the computed one\r\n    result[child.key] = mapper(child);\r\n  });\r\n  return result;\r\n}\r\n/**\r\n * When you're adding or removing children some may be added or removed in the\r\n * same render pass. We want to show *both* since we want to simultaneously\r\n * animate elements in and out. This function takes a previous set of keys\r\n * and a new set of keys and merges them with its best guess of the correct\r\n * ordering. In the future we may expose some of the utilities in\r\n * ReactMultiChild to make this easy, but for now React itself does not\r\n * directly have this concept of the union of prevChildren and nextChildren\r\n * so we implement it here.\r\n *\r\n * @param {object} prev prev children as returned from\r\n * `ReactTransitionChildMapping.getChildMapping()`.\r\n * @param {object} next next children as returned from\r\n * `ReactTransitionChildMapping.getChildMapping()`.\r\n * @return {object} a key set that contains all keys in `prev` and all keys\r\n * in `next` in a reasonable order.\r\n */\r\n\r\nexport function mergeChildMappings(prev, next) {\r\n  prev = prev || {};\r\n  next = next || {};\r\n\r\n  function getValueForKey(key) {\r\n    return key in next ? next[key] : prev[key];\r\n  } // For each key of `next`, the list of keys to insert before that key in\r\n  // the combined list\r\n\r\n\r\n  var nextKeysPending = Object.create(null);\r\n  var pendingKeys = [];\r\n\r\n  for (var prevKey in prev) {\r\n    if (prevKey in next) {\r\n      if (pendingKeys.length) {\r\n        nextKeysPending[prevKey] = pendingKeys;\r\n        pendingKeys = [];\r\n      }\r\n    } else {\r\n      pendingKeys.push(prevKey);\r\n    }\r\n  }\r\n\r\n  var i;\r\n  var childMapping = {};\r\n\r\n  for (var nextKey in next) {\r\n    if (nextKeysPending[nextKey]) {\r\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\r\n        var pendingNextKey = nextKeysPending[nextKey][i];\r\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\r\n      }\r\n    }\r\n\r\n    childMapping[nextKey] = getValueForKey(nextKey);\r\n  } // Finally, add the keys which didn't appear before any key in `next`\r\n\r\n\r\n  for (i = 0; i < pendingKeys.length; i++) {\r\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\r\n  }\r\n\r\n  return childMapping;\r\n}\r\n\r\nfunction getProp(child, prop, props) {\r\n  return props[prop] != null ? props[prop] : child.props[prop];\r\n}\r\n\r\nexport function getInitialChildMapping(props, onExited) {\r\n  return getChildMapping(props.children, function (child) {\r\n    return cloneElement(child, {\r\n      onExited: onExited.bind(null, child),\r\n      in: true,\r\n      appear: getProp(child, 'appear', props),\r\n      enter: getProp(child, 'enter', props),\r\n      exit: getProp(child, 'exit', props)\r\n    });\r\n  });\r\n}\r\nexport function getNextChildMapping(nextProps, prevChildMapping, onExited) {\r\n  var nextChildMapping = getChildMapping(nextProps.children);\r\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\r\n  Object.keys(children).forEach(function (key) {\r\n    var child = children[key];\r\n    if (!isValidElement(child)) return;\r\n    var hasPrev = (key in prevChildMapping);\r\n    var hasNext = (key in nextChildMapping);\r\n    var prevChild = prevChildMapping[key];\r\n    var isLeaving = isValidElement(prevChild) && !prevChild.props.in; // item is new (entering)\r\n\r\n    if (hasNext && (!hasPrev || isLeaving)) {\r\n      // console.log('entering', key)\r\n      children[key] = cloneElement(child, {\r\n        onExited: onExited.bind(null, child),\r\n        in: true,\r\n        exit: getProp(child, 'exit', nextProps),\r\n        enter: getProp(child, 'enter', nextProps)\r\n      });\r\n    } else if (!hasNext && hasPrev && !isLeaving) {\r\n      // item is old (exiting)\r\n      // console.log('leaving', key)\r\n      children[key] = cloneElement(child, {\r\n        in: false\r\n      });\r\n    } else if (hasNext && hasPrev && isValidElement(prevChild)) {\r\n      // item hasn't changed transition states\r\n      // copy over the last transition props;\r\n      // console.log('unchanged', key)\r\n      children[key] = cloneElement(child, {\r\n        onExited: onExited.bind(null, child),\r\n        in: prevChild.props.in,\r\n        exit: getProp(child, 'exit', nextProps),\r\n        enter: getProp(child, 'enter', nextProps)\r\n      });\r\n    }\r\n  });\r\n  return children;\r\n}", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\r\n\r\nvar _leaveRenders, _enterRenders;\r\n\r\nimport React from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport { ENTERED, ENTERING, EXITING } from './Transition';\r\nimport TransitionGroupContext from './TransitionGroupContext';\r\n\r\nfunction areChildrenDifferent(oldChildren, newChildren) {\r\n  if (oldChildren === newChildren) return false;\r\n\r\n  if (React.isValidElement(oldChildren) && React.isValidElement(newChildren) && oldChildren.key != null && oldChildren.key === newChildren.key) {\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n}\r\n/**\r\n * Enum of modes for SwitchTransition component\r\n * @enum { string }\r\n */\r\n\r\n\r\nexport var modes = {\r\n  out: 'out-in',\r\n  in: 'in-out'\r\n};\r\n\r\nvar callHook = function callHook(element, name, cb) {\r\n  return function () {\r\n    var _element$props;\r\n\r\n    element.props[name] && (_element$props = element.props)[name].apply(_element$props, arguments);\r\n    cb();\r\n  };\r\n};\r\n\r\nvar leaveRenders = (_leaveRenders = {}, _leaveRenders[modes.out] = function (_ref) {\r\n  var current = _ref.current,\r\n      changeState = _ref.changeState;\r\n  return React.cloneElement(current, {\r\n    in: false,\r\n    onExited: callHook(current, 'onExited', function () {\r\n      changeState(ENTERING, null);\r\n    })\r\n  });\r\n}, _leaveRenders[modes.in] = function (_ref2) {\r\n  var current = _ref2.current,\r\n      changeState = _ref2.changeState,\r\n      children = _ref2.children;\r\n  return [current, React.cloneElement(children, {\r\n    in: true,\r\n    onEntered: callHook(children, 'onEntered', function () {\r\n      changeState(ENTERING);\r\n    })\r\n  })];\r\n}, _leaveRenders);\r\nvar enterRenders = (_enterRenders = {}, _enterRenders[modes.out] = function (_ref3) {\r\n  var children = _ref3.children,\r\n      changeState = _ref3.changeState;\r\n  return React.cloneElement(children, {\r\n    in: true,\r\n    onEntered: callHook(children, 'onEntered', function () {\r\n      changeState(ENTERED, React.cloneElement(children, {\r\n        in: true\r\n      }));\r\n    })\r\n  });\r\n}, _enterRenders[modes.in] = function (_ref4) {\r\n  var current = _ref4.current,\r\n      children = _ref4.children,\r\n      changeState = _ref4.changeState;\r\n  return [React.cloneElement(current, {\r\n    in: false,\r\n    onExited: callHook(current, 'onExited', function () {\r\n      changeState(ENTERED, React.cloneElement(children, {\r\n        in: true\r\n      }));\r\n    })\r\n  }), React.cloneElement(children, {\r\n    in: true\r\n  })];\r\n}, _enterRenders);\r\n/**\r\n * A transition component inspired by the [vue transition modes](https://vuejs.org/v2/guide/transitions.html#Transition-Modes).\r\n * You can use it when you want to control the render between state transitions.\r\n * Based on the selected mode and the child's key which is the `Transition` or `CSSTransition` component, the `SwitchTransition` makes a consistent transition between them.\r\n *\r\n * If the `out-in` mode is selected, the `SwitchTransition` waits until the old child leaves and then inserts a new child.\r\n * If the `in-out` mode is selected, the `SwitchTransition` inserts a new child first, waits for the new child to enter and then removes the old child.\r\n *\r\n * **Note**: If you want the animation to happen simultaneously\r\n * (that is, to have the old child removed and a new child inserted **at the same time**),\r\n * you should use\r\n * [`TransitionGroup`](https://reactcommunity.org/react-transition-group/transition-group)\r\n * instead.\r\n *\r\n * ```jsx\r\n * function App() {\r\n *  const [state, setState] = useState(false);\r\n *  return (\r\n *    <SwitchTransition>\r\n *      <CSSTransition\r\n *        key={state ? \"Goodbye, world!\" : \"Hello, world!\"}\r\n *        addEndListener={(node, done) => node.addEventListener(\"transitionend\", done, false)}\r\n *        classNames='fade'\r\n *      >\r\n *        <button onClick={() => setState(state => !state)}>\r\n *          {state ? \"Goodbye, world!\" : \"Hello, world!\"}\r\n *        </button>\r\n *      </CSSTransition>\r\n *    </SwitchTransition>\r\n *  );\r\n * }\r\n * ```\r\n *\r\n * ```css\r\n * .fade-enter{\r\n *    opacity: 0;\r\n * }\r\n * .fade-exit{\r\n *    opacity: 1;\r\n * }\r\n * .fade-enter-active{\r\n *    opacity: 1;\r\n * }\r\n * .fade-exit-active{\r\n *    opacity: 0;\r\n * }\r\n * .fade-enter-active,\r\n * .fade-exit-active{\r\n *    transition: opacity 500ms;\r\n * }\r\n * ```\r\n */\r\n\r\nvar SwitchTransition = /*#__PURE__*/function (_React$Component) {\r\n  _inheritsLoose(SwitchTransition, _React$Component);\r\n\r\n  function SwitchTransition() {\r\n    var _this;\r\n\r\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n      args[_key] = arguments[_key];\r\n    }\r\n\r\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\r\n    _this.state = {\r\n      status: ENTERED,\r\n      current: null\r\n    };\r\n    _this.appeared = false;\r\n\r\n    _this.changeState = function (status, current) {\r\n      if (current === void 0) {\r\n        current = _this.state.current;\r\n      }\r\n\r\n      _this.setState({\r\n        status: status,\r\n        current: current\r\n      });\r\n    };\r\n\r\n    return _this;\r\n  }\r\n\r\n  var _proto = SwitchTransition.prototype;\r\n\r\n  _proto.componentDidMount = function componentDidMount() {\r\n    this.appeared = true;\r\n  };\r\n\r\n  SwitchTransition.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\r\n    if (props.children == null) {\r\n      return {\r\n        current: null\r\n      };\r\n    }\r\n\r\n    if (state.status === ENTERING && props.mode === modes.in) {\r\n      return {\r\n        status: ENTERING\r\n      };\r\n    }\r\n\r\n    if (state.current && areChildrenDifferent(state.current, props.children)) {\r\n      return {\r\n        status: EXITING\r\n      };\r\n    }\r\n\r\n    return {\r\n      current: React.cloneElement(props.children, {\r\n        in: true\r\n      })\r\n    };\r\n  };\r\n\r\n  _proto.render = function render() {\r\n    var _this$props = this.props,\r\n        children = _this$props.children,\r\n        mode = _this$props.mode,\r\n        _this$state = this.state,\r\n        status = _this$state.status,\r\n        current = _this$state.current;\r\n    var data = {\r\n      children: children,\r\n      current: current,\r\n      changeState: this.changeState,\r\n      status: status\r\n    };\r\n    var component;\r\n\r\n    switch (status) {\r\n      case ENTERING:\r\n        component = enterRenders[mode](data);\r\n        break;\r\n\r\n      case EXITING:\r\n        component = leaveRenders[mode](data);\r\n        break;\r\n\r\n      case ENTERED:\r\n        component = current;\r\n    }\r\n\r\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\r\n      value: {\r\n        isMounting: !this.appeared\r\n      }\r\n    }, component);\r\n  };\r\n\r\n  return SwitchTransition;\r\n}(React.Component);\r\n\r\nSwitchTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\r\n  /**\r\n   * Transition modes.\r\n   * `out-in`: Current element transitions out first, then when complete, the new element transitions in.\r\n   * `in-out`: New element transitions in first, then when complete, the current element transitions out.\r\n   *\r\n   * @type {'out-in'|'in-out'}\r\n   */\r\n  mode: PropTypes.oneOf([modes.in, modes.out]),\r\n\r\n  /**\r\n   * Any `Transition` or `CSSTransition` component.\r\n   */\r\n  children: PropTypes.oneOfType([PropTypes.element.isRequired])\r\n} : {};\r\nSwitchTransition.defaultProps = {\r\n  mode: modes.out\r\n};\r\nexport default SwitchTransition;"], "mappings": ";;;;;;;;;;;;;;;;;;;;AACA,SAAS,eAAe,GAAG,GAAG;AAC5B,IAAE,YAAY,OAAO,OAAO,EAAE,SAAS,GAAG,EAAE,UAAU,cAAc,GAAG,gBAAe,GAAG,CAAC;AAC5F;;;ACAA,IAAAA,qBAAsB;;;ACGP,SAAR,SAA0B,SAAS,WAAW;AACnD,MAAI,QAAQ,UAAW,QAAO,CAAC,CAAC,aAAa,QAAQ,UAAU,SAAS,SAAS;AACjF,UAAQ,OAAO,QAAQ,UAAU,WAAW,QAAQ,aAAa,KAAK,QAAQ,MAAM,YAAY,GAAG,MAAM;AAC3G;;;ACDe,SAAR,SAA0B,SAAS,WAAW;AACnD,MAAI,QAAQ,UAAW,SAAQ,UAAU,IAAI,SAAS;AAAA,WAAW,CAAC,SAAS,SAAS,SAAS,EAAG,KAAI,OAAO,QAAQ,cAAc,SAAU,SAAQ,YAAY,QAAQ,YAAY,MAAM;AAAA,MAAe,SAAQ,aAAa,UAAU,QAAQ,aAAa,QAAQ,UAAU,WAAW,MAAM,MAAM,SAAS;AAChT;;;ACVA,SAAS,iBAAiB,WAAW,eAAe;AAClD,SAAO,UAAU,QAAQ,IAAI,OAAO,YAAY,gBAAgB,aAAa,GAAG,GAAG,IAAI,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,cAAc,EAAE;AACxI;AASe,SAAR,YAA6B,SAAS,WAAW;AACtD,MAAI,QAAQ,WAAW;AACrB,YAAQ,UAAU,OAAO,SAAS;AAAA,EACpC,WAAW,OAAO,QAAQ,cAAc,UAAU;AAChD,YAAQ,YAAY,iBAAiB,QAAQ,WAAW,SAAS;AAAA,EACnE,OAAO;AACL,YAAQ,aAAa,SAAS,iBAAiB,QAAQ,aAAa,QAAQ,UAAU,WAAW,IAAI,SAAS,CAAC;AAAA,EACjH;AACF;;;AHbA,IAAAC,gBAAkB;;;AIJlB,IAAAC,qBAAsB;AACtB,IAAAC,gBAAkB;AAClB,uBAAqB;;;ACJrB,IAAO,iBAAQ;AAAA,EACb,UAAU;AACZ;;;ACFA,wBAAsB;AACf,IAAI,gBAAgB,OAAwC,kBAAAC,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EACxH,OAAO,kBAAAA,QAAU;AAAA,EACjB,MAAM,kBAAAA,QAAU;AAAA,EAChB,QAAQ,kBAAAA,QAAU;AACpB,CAAC,EAAE,UAAU,CAAC,IAAI;AACX,IAAI,kBAAkB,OAAwC,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EAC1H,OAAO,kBAAAA,QAAU;AAAA,EACjB,MAAM,kBAAAA,QAAU;AAAA,EAChB,QAAQ,kBAAAA,QAAU;AACpB,CAAC,GAAG,kBAAAA,QAAU,MAAM;AAAA,EAClB,OAAO,kBAAAA,QAAU;AAAA,EACjB,WAAW,kBAAAA,QAAU;AAAA,EACrB,aAAa,kBAAAA,QAAU;AAAA,EACvB,MAAM,kBAAAA,QAAU;AAAA,EAChB,UAAU,kBAAAA,QAAU;AAAA,EACpB,YAAY,kBAAAA,QAAU;AACxB,CAAC,CAAC,CAAC,IAAI;;;ACjBP,mBAAkB;AAClB,IAAO,iCAAQ,aAAAC,QAAM,cAAc,IAAI;;;ACDhC,IAAI,cAAc,SAASC,aAAY,MAAM;AAClD,SAAO,KAAK;AACd;;;AJOO,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AA6FrB,IAAI,aAA0B,SAAU,kBAAkB;AACxD,iBAAeC,aAAY,gBAAgB;AAE3C,WAASA,YAAW,OAAO,SAAS;AAClC,QAAI;AAEJ,YAAQ,iBAAiB,KAAK,MAAM,OAAO,OAAO,KAAK;AACvD,QAAI,cAAc;AAElB,QAAI,SAAS,eAAe,CAAC,YAAY,aAAa,MAAM,QAAQ,MAAM;AAC1E,QAAI;AACJ,UAAM,eAAe;AAErB,QAAI,MAAM,IAAI;AACZ,UAAI,QAAQ;AACV,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACvB,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF,OAAO;AACL,UAAI,MAAM,iBAAiB,MAAM,cAAc;AAC7C,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AAEA,UAAM,QAAQ;AAAA,MACZ,QAAQ;AAAA,IACV;AACA,UAAM,eAAe;AACrB,WAAO;AAAA,EACT;AAEA,EAAAA,YAAW,2BAA2B,SAAS,yBAAyB,MAAM,WAAW;AACvF,QAAI,SAAS,KAAK;AAElB,QAAI,UAAU,UAAU,WAAW,WAAW;AAC5C,aAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAkBA,MAAI,SAASA,YAAW;AAExB,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,SAAK,aAAa,MAAM,KAAK,YAAY;AAAA,EAC3C;AAEA,SAAO,qBAAqB,SAAS,mBAAmB,WAAW;AACjE,QAAI,aAAa;AAEjB,QAAI,cAAc,KAAK,OAAO;AAC5B,UAAI,SAAS,KAAK,MAAM;AAExB,UAAI,KAAK,MAAM,IAAI;AACjB,YAAI,WAAW,YAAY,WAAW,SAAS;AAC7C,uBAAa;AAAA,QACf;AAAA,MACF,OAAO;AACL,YAAI,WAAW,YAAY,WAAW,SAAS;AAC7C,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAEA,SAAK,aAAa,OAAO,UAAU;AAAA,EACrC;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,SAAK,mBAAmB;AAAA,EAC1B;AAEA,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAIC,WAAU,KAAK,MAAM;AACzB,QAAI,MAAM,OAAO;AACjB,WAAO,QAAQ,SAASA;AAExB,QAAIA,YAAW,QAAQ,OAAOA,aAAY,UAAU;AAClD,aAAOA,SAAQ;AACf,cAAQA,SAAQ;AAEhB,eAASA,SAAQ,WAAW,SAAYA,SAAQ,SAAS;AAAA,IAC3D;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,aAAa,UAAU,YAAY;AAChE,QAAI,aAAa,QAAQ;AACvB,iBAAW;AAAA,IACb;AAEA,QAAI,eAAe,MAAM;AAEvB,WAAK,mBAAmB;AAExB,UAAI,eAAe,UAAU;AAC3B,YAAI,KAAK,MAAM,iBAAiB,KAAK,MAAM,cAAc;AACvD,cAAI,OAAO,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,UAAU,iBAAAC,QAAS,YAAY,IAAI;AAItF,cAAI,KAAM,aAAY,IAAI;AAAA,QAC5B;AAEA,aAAK,aAAa,QAAQ;AAAA,MAC5B,OAAO;AACL,aAAK,YAAY;AAAA,MACnB;AAAA,IACF,WAAW,KAAK,MAAM,iBAAiB,KAAK,MAAM,WAAW,QAAQ;AACnE,WAAK,SAAS;AAAA,QACZ,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,aAAa,UAAU;AACpD,QAAI,SAAS;AAEb,QAAI,QAAQ,KAAK,MAAM;AACvB,QAAI,YAAY,KAAK,UAAU,KAAK,QAAQ,aAAa;AAEzD,QAAI,QAAQ,KAAK,MAAM,UAAU,CAAC,SAAS,IAAI,CAAC,iBAAAA,QAAS,YAAY,IAAI,GAAG,SAAS,GACjF,YAAY,MAAM,CAAC,GACnB,iBAAiB,MAAM,CAAC;AAE5B,QAAI,WAAW,KAAK,YAAY;AAChC,QAAI,eAAe,YAAY,SAAS,SAAS,SAAS;AAG1D,QAAI,CAAC,YAAY,CAAC,SAAS,eAAO,UAAU;AAC1C,WAAK,aAAa;AAAA,QAChB,QAAQ;AAAA,MACV,GAAG,WAAY;AACb,eAAO,MAAM,UAAU,SAAS;AAAA,MAClC,CAAC;AACD;AAAA,IACF;AAEA,SAAK,MAAM,QAAQ,WAAW,cAAc;AAC5C,SAAK,aAAa;AAAA,MAChB,QAAQ;AAAA,IACV,GAAG,WAAY;AACb,aAAO,MAAM,WAAW,WAAW,cAAc;AAEjD,aAAO,gBAAgB,cAAc,WAAY;AAC/C,eAAO,aAAa;AAAA,UAClB,QAAQ;AAAA,QACV,GAAG,WAAY;AACb,iBAAO,MAAM,UAAU,WAAW,cAAc;AAAA,QAClD,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAI,SAAS;AAEb,QAAI,OAAO,KAAK,MAAM;AACtB,QAAI,WAAW,KAAK,YAAY;AAChC,QAAI,YAAY,KAAK,MAAM,UAAU,SAAY,iBAAAA,QAAS,YAAY,IAAI;AAE1E,QAAI,CAAC,QAAQ,eAAO,UAAU;AAC5B,WAAK,aAAa;AAAA,QAChB,QAAQ;AAAA,MACV,GAAG,WAAY;AACb,eAAO,MAAM,SAAS,SAAS;AAAA,MACjC,CAAC;AACD;AAAA,IACF;AAEA,SAAK,MAAM,OAAO,SAAS;AAC3B,SAAK,aAAa;AAAA,MAChB,QAAQ;AAAA,IACV,GAAG,WAAY;AACb,aAAO,MAAM,UAAU,SAAS;AAEhC,aAAO,gBAAgB,SAAS,MAAM,WAAY;AAChD,eAAO,aAAa;AAAA,UAClB,QAAQ;AAAA,QACV,GAAG,WAAY;AACb,iBAAO,MAAM,SAAS,SAAS;AAAA,QACjC,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,qBAAqB,SAAS,qBAAqB;AACxD,QAAI,KAAK,iBAAiB,MAAM;AAC9B,WAAK,aAAa,OAAO;AACzB,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,aAAa,WAAW,UAAU;AAI/D,eAAW,KAAK,gBAAgB,QAAQ;AACxC,SAAK,SAAS,WAAW,QAAQ;AAAA,EACnC;AAEA,SAAO,kBAAkB,SAAS,gBAAgB,UAAU;AAC1D,QAAI,SAAS;AAEb,QAAI,SAAS;AAEb,SAAK,eAAe,SAAU,OAAO;AACnC,UAAI,QAAQ;AACV,iBAAS;AACT,eAAO,eAAe;AACtB,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAEA,SAAK,aAAa,SAAS,WAAY;AACrC,eAAS;AAAA,IACX;AAEA,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,kBAAkB,SAAS,gBAAgBD,UAAS,SAAS;AAClE,SAAK,gBAAgB,OAAO;AAC5B,QAAI,OAAO,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,UAAU,iBAAAC,QAAS,YAAY,IAAI;AACtF,QAAI,+BAA+BD,YAAW,QAAQ,CAAC,KAAK,MAAM;AAElE,QAAI,CAAC,QAAQ,8BAA8B;AACzC,iBAAW,KAAK,cAAc,CAAC;AAC/B;AAAA,IACF;AAEA,QAAI,KAAK,MAAM,gBAAgB;AAC7B,UAAI,QAAQ,KAAK,MAAM,UAAU,CAAC,KAAK,YAAY,IAAI,CAAC,MAAM,KAAK,YAAY,GAC3E,YAAY,MAAM,CAAC,GACnB,oBAAoB,MAAM,CAAC;AAE/B,WAAK,MAAM,eAAe,WAAW,iBAAiB;AAAA,IACxD;AAEA,QAAIA,YAAW,MAAM;AACnB,iBAAW,KAAK,cAAcA,QAAO;AAAA,IACvC;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,SAAS,KAAK,MAAM;AAExB,QAAI,WAAW,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,KAAK,OACnBE,YAAW,YAAY,UACvB,MAAM,YAAY,IAClB,gBAAgB,YAAY,cAC5B,iBAAiB,YAAY,eAC7B,UAAU,YAAY,QACtB,SAAS,YAAY,OACrB,QAAQ,YAAY,MACpB,WAAW,YAAY,SACvB,kBAAkB,YAAY,gBAC9B,WAAW,YAAY,SACvB,cAAc,YAAY,YAC1B,aAAa,YAAY,WACzB,UAAU,YAAY,QACtB,aAAa,YAAY,WACzB,YAAY,YAAY,UACxB,WAAW,YAAY,SACvB,aAAa,8BAA8B,aAAa,CAAC,YAAY,MAAM,gBAAgB,iBAAiB,UAAU,SAAS,QAAQ,WAAW,kBAAkB,WAAW,cAAc,aAAa,UAAU,aAAa,YAAY,SAAS,CAAC;AAE3P;AAAA;AAAA,MAGE,cAAAC,QAAM,cAAc,+BAAuB,UAAU;AAAA,QACnD,OAAO;AAAA,MACT,GAAG,OAAOD,cAAa,aAAaA,UAAS,QAAQ,UAAU,IAAI,cAAAC,QAAM,aAAa,cAAAA,QAAM,SAAS,KAAKD,SAAQ,GAAG,UAAU,CAAC;AAAA;AAAA,EAEpI;AAEA,SAAOH;AACT,EAAE,cAAAI,QAAM,SAAS;AAEjB,WAAW,cAAc;AACzB,WAAW,YAAY,OAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY7D,SAAS,mBAAAC,QAAU,MAAM;AAAA,IACvB,SAAS,OAAO,YAAY,cAAc,mBAAAA,QAAU,MAAM,SAAU,WAAW,KAAK,eAAe,UAAU,cAAc,QAAQ;AACjI,UAAI,QAAQ,UAAU,GAAG;AACzB,aAAO,mBAAAA,QAAU,WAAW,SAAS,mBAAmB,QAAQ,MAAM,cAAc,YAAY,UAAU,OAAO,EAAE,WAAW,KAAK,eAAe,UAAU,cAAc,MAAM;AAAA,IAClL;AAAA,EACF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBD,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,KAAK,YAAY,mBAAAA,QAAU,QAAQ,UAAU,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAKzF,IAAI,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQd,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAazB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKlB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKjB,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BhB,SAAS,SAAS,QAAQ,OAAO;AAC/B,QAAI,KAAK;AACT,QAAI,CAAC,MAAM,eAAgB,MAAK,GAAG;AAEnC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,WAAO,GAAG,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU1B,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUnB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,UAAU,mBAAAA,QAAU;AACtB,IAAI,CAAC;AAEL,SAAS,OAAO;AAAC;AAEjB,WAAW,eAAe;AAAA,EACxB,IAAI;AAAA,EACJ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,UAAU;AACZ;AACA,WAAW,YAAY;AACvB,WAAW,SAAS;AACpB,WAAW,WAAW;AACtB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,IAAO,qBAAQ;;;AJrmBf,IAAI,YAAY,SAASC,UAAS,MAAM,SAAS;AAC/C,SAAO,QAAQ,WAAW,QAAQ,MAAM,GAAG,EAAE,QAAQ,SAAU,GAAG;AAChE,WAAO,SAAY,MAAM,CAAC;AAAA,EAC5B,CAAC;AACH;AAEA,IAAIC,eAAc,SAASA,aAAY,MAAM,SAAS;AACpD,SAAO,QAAQ,WAAW,QAAQ,MAAM,GAAG,EAAE,QAAQ,SAAU,GAAG;AAChE,WAAO,YAAe,MAAM,CAAC;AAAA,EAC/B,CAAC;AACH;AAwEA,IAAI,gBAA6B,SAAU,kBAAkB;AAC3D,iBAAeC,gBAAe,gBAAgB;AAE9C,WAASA,iBAAgB;AACvB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAC9E,UAAM,iBAAiB;AAAA,MACrB,QAAQ,CAAC;AAAA,MACT,OAAO,CAAC;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAEA,UAAM,UAAU,SAAU,WAAW,gBAAgB;AACnD,UAAI,wBAAwB,MAAM,iBAAiB,WAAW,cAAc,GACxE,OAAO,sBAAsB,CAAC,GAC9B,YAAY,sBAAsB,CAAC;AAEvC,YAAM,cAAc,MAAM,MAAM;AAEhC,YAAM,SAAS,MAAM,YAAY,WAAW,SAAS,MAAM;AAE3D,UAAI,MAAM,MAAM,SAAS;AACvB,cAAM,MAAM,QAAQ,WAAW,cAAc;AAAA,MAC/C;AAAA,IACF;AAEA,UAAM,aAAa,SAAU,WAAW,gBAAgB;AACtD,UAAI,yBAAyB,MAAM,iBAAiB,WAAW,cAAc,GACzE,OAAO,uBAAuB,CAAC,GAC/B,YAAY,uBAAuB,CAAC;AAExC,UAAI,OAAO,YAAY,WAAW;AAElC,YAAM,SAAS,MAAM,MAAM,QAAQ;AAEnC,UAAI,MAAM,MAAM,YAAY;AAC1B,cAAM,MAAM,WAAW,WAAW,cAAc;AAAA,MAClD;AAAA,IACF;AAEA,UAAM,YAAY,SAAU,WAAW,gBAAgB;AACrD,UAAI,yBAAyB,MAAM,iBAAiB,WAAW,cAAc,GACzE,OAAO,uBAAuB,CAAC,GAC/B,YAAY,uBAAuB,CAAC;AAExC,UAAI,OAAO,YAAY,WAAW;AAElC,YAAM,cAAc,MAAM,IAAI;AAE9B,YAAM,SAAS,MAAM,MAAM,MAAM;AAEjC,UAAI,MAAM,MAAM,WAAW;AACzB,cAAM,MAAM,UAAU,WAAW,cAAc;AAAA,MACjD;AAAA,IACF;AAEA,UAAM,SAAS,SAAU,WAAW;AAClC,UAAI,yBAAyB,MAAM,iBAAiB,SAAS,GACzD,OAAO,uBAAuB,CAAC;AAEnC,YAAM,cAAc,MAAM,QAAQ;AAElC,YAAM,cAAc,MAAM,OAAO;AAEjC,YAAM,SAAS,MAAM,QAAQ,MAAM;AAEnC,UAAI,MAAM,MAAM,QAAQ;AACtB,cAAM,MAAM,OAAO,SAAS;AAAA,MAC9B;AAAA,IACF;AAEA,UAAM,YAAY,SAAU,WAAW;AACrC,UAAI,yBAAyB,MAAM,iBAAiB,SAAS,GACzD,OAAO,uBAAuB,CAAC;AAEnC,YAAM,SAAS,MAAM,QAAQ,QAAQ;AAErC,UAAI,MAAM,MAAM,WAAW;AACzB,cAAM,MAAM,UAAU,SAAS;AAAA,MACjC;AAAA,IACF;AAEA,UAAM,WAAW,SAAU,WAAW;AACpC,UAAI,yBAAyB,MAAM,iBAAiB,SAAS,GACzD,OAAO,uBAAuB,CAAC;AAEnC,YAAM,cAAc,MAAM,MAAM;AAEhC,YAAM,SAAS,MAAM,QAAQ,MAAM;AAEnC,UAAI,MAAM,MAAM,UAAU;AACxB,cAAM,MAAM,SAAS,SAAS;AAAA,MAChC;AAAA,IACF;AAEA,UAAM,mBAAmB,SAAU,WAAW,gBAAgB;AAC5D,aAAO,MAAM,MAAM,UAAU,CAAC,MAAM,MAAM,QAAQ,SAAS,SAAS,IAClE,CAAC,WAAW,cAAc;AAAA,IAC9B;AAEA,UAAM,gBAAgB,SAAU,MAAM;AACpC,UAAI,aAAa,MAAM,MAAM;AAC7B,UAAI,qBAAqB,OAAO,eAAe;AAC/C,UAAI,SAAS,sBAAsB,aAAa,aAAa,MAAM;AACnE,UAAI,gBAAgB,qBAAqB,KAAK,SAAS,OAAO,WAAW,IAAI;AAC7E,UAAI,kBAAkB,qBAAqB,gBAAgB,YAAY,WAAW,OAAO,QAAQ;AACjG,UAAI,gBAAgB,qBAAqB,gBAAgB,UAAU,WAAW,OAAO,MAAM;AAC3F,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,eAAc;AAE3B,SAAO,WAAW,SAASF,UAAS,MAAM,MAAM,OAAO;AACrD,QAAI,YAAY,KAAK,cAAc,IAAI,EAAE,QAAQ,WAAW;AAE5D,QAAI,sBAAsB,KAAK,cAAc,OAAO,GAChD,gBAAgB,oBAAoB;AAExC,QAAI,SAAS,YAAY,UAAU,UAAU,eAAe;AAC1D,mBAAa,MAAM;AAAA,IACrB;AAIA,QAAI,UAAU,UAAU;AACtB,UAAI,KAAM,aAAY,IAAI;AAAA,IAC5B;AAEA,QAAI,WAAW;AACb,WAAK,eAAe,IAAI,EAAE,KAAK,IAAI;AAEnC,gBAAU,MAAM,SAAS;AAAA,IAC3B;AAAA,EACF;AAEA,SAAO,gBAAgB,SAAS,cAAc,MAAM,MAAM;AACxD,QAAI,wBAAwB,KAAK,eAAe,IAAI,GAChD,gBAAgB,sBAAsB,MACtC,kBAAkB,sBAAsB,QACxC,gBAAgB,sBAAsB;AAC1C,SAAK,eAAe,IAAI,IAAI,CAAC;AAE7B,QAAI,eAAe;AACjB,MAAAC,aAAY,MAAM,aAAa;AAAA,IACjC;AAEA,QAAI,iBAAiB;AACnB,MAAAA,aAAY,MAAM,eAAe;AAAA,IACnC;AAEA,QAAI,eAAe;AACjB,MAAAA,aAAY,MAAM,aAAa;AAAA,IACjC;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnB,IAAI,YAAY,YAChB,QAAQ,8BAA8B,aAAa,CAAC,YAAY,CAAC;AAErE,WAAoB,cAAAE,QAAM,cAAc,oBAAY,SAAS,CAAC,GAAG,OAAO;AAAA,MACtE,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,MAChB,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAEA,SAAOD;AACT,EAAE,cAAAC,QAAM,SAAS;AAEjB,cAAc,eAAe;AAAA,EAC3B,YAAY;AACd;AACA,cAAc,YAAY,OAAwC,SAAS,CAAC,GAAG,mBAAW,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqEnG,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUZ,SAAS,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUnB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUrB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUrB,UAAU,mBAAAA,QAAU;AACtB,CAAC,IAAI,CAAC;AACN,IAAO,wBAAQ;;;ASzZf,IAAAC,qBAAsB;AACtB,IAAAC,gBAAkB;AAClB,IAAAC,oBAAqB;;;ACArB,IAAAC,qBAAsB;AACtB,IAAAC,gBAAkB;;;ACLlB,IAAAC,gBAAuD;AAQhD,SAAS,gBAAgBC,WAAU,OAAO;AAC/C,MAAI,SAAS,SAASC,QAAO,OAAO;AAClC,WAAO,aAAS,8BAAe,KAAK,IAAI,MAAM,KAAK,IAAI;AAAA,EACzD;AAEA,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,MAAID,UAAU,wBAAS,IAAIA,WAAU,SAAU,GAAG;AAChD,WAAO;AAAA,EACT,CAAC,EAAE,QAAQ,SAAU,OAAO;AAE1B,WAAO,MAAM,GAAG,IAAI,OAAO,KAAK;AAAA,EAClC,CAAC;AACD,SAAO;AACT;AAmBO,SAAS,mBAAmB,MAAM,MAAM;AAC7C,SAAO,QAAQ,CAAC;AAChB,SAAO,QAAQ,CAAC;AAEhB,WAAS,eAAe,KAAK;AAC3B,WAAO,OAAO,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG;AAAA,EAC3C;AAIA,MAAI,kBAAkB,uBAAO,OAAO,IAAI;AACxC,MAAI,cAAc,CAAC;AAEnB,WAAS,WAAW,MAAM;AACxB,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,QAAQ;AACtB,wBAAgB,OAAO,IAAI;AAC3B,sBAAc,CAAC;AAAA,MACjB;AAAA,IACF,OAAO;AACL,kBAAY,KAAK,OAAO;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI;AACJ,MAAI,eAAe,CAAC;AAEpB,WAAS,WAAW,MAAM;AACxB,QAAI,gBAAgB,OAAO,GAAG;AAC5B,WAAK,IAAI,GAAG,IAAI,gBAAgB,OAAO,EAAE,QAAQ,KAAK;AACpD,YAAI,iBAAiB,gBAAgB,OAAO,EAAE,CAAC;AAC/C,qBAAa,gBAAgB,OAAO,EAAE,CAAC,CAAC,IAAI,eAAe,cAAc;AAAA,MAC3E;AAAA,IACF;AAEA,iBAAa,OAAO,IAAI,eAAe,OAAO;AAAA,EAChD;AAGA,OAAK,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACvC,iBAAa,YAAY,CAAC,CAAC,IAAI,eAAe,YAAY,CAAC,CAAC;AAAA,EAC9D;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,OAAO,MAAM,OAAO;AACnC,SAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI;AAC7D;AAEO,SAAS,uBAAuB,OAAO,UAAU;AACtD,SAAO,gBAAgB,MAAM,UAAU,SAAU,OAAO;AACtD,eAAO,4BAAa,OAAO;AAAA,MACzB,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,MACnC,IAAI;AAAA,MACJ,QAAQ,QAAQ,OAAO,UAAU,KAAK;AAAA,MACtC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MACpC,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH;AACO,SAAS,oBAAoB,WAAW,kBAAkB,UAAU;AACzE,MAAI,mBAAmB,gBAAgB,UAAU,QAAQ;AACzD,MAAIA,YAAW,mBAAmB,kBAAkB,gBAAgB;AACpE,SAAO,KAAKA,SAAQ,EAAE,QAAQ,SAAU,KAAK;AAC3C,QAAI,QAAQA,UAAS,GAAG;AACxB,QAAI,KAAC,8BAAe,KAAK,EAAG;AAC5B,QAAI,UAAW,OAAO;AACtB,QAAI,UAAW,OAAO;AACtB,QAAI,YAAY,iBAAiB,GAAG;AACpC,QAAI,gBAAY,8BAAe,SAAS,KAAK,CAAC,UAAU,MAAM;AAE9D,QAAI,YAAY,CAAC,WAAW,YAAY;AAEtC,MAAAA,UAAS,GAAG,QAAI,4BAAa,OAAO;AAAA,QAClC,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,QACnC,IAAI;AAAA,QACJ,MAAM,QAAQ,OAAO,QAAQ,SAAS;AAAA,QACtC,OAAO,QAAQ,OAAO,SAAS,SAAS;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,CAAC,WAAW,WAAW,CAAC,WAAW;AAG5C,MAAAA,UAAS,GAAG,QAAI,4BAAa,OAAO;AAAA,QAClC,IAAI;AAAA,MACN,CAAC;AAAA,IACH,WAAW,WAAW,eAAW,8BAAe,SAAS,GAAG;AAI1D,MAAAA,UAAS,GAAG,QAAI,4BAAa,OAAO;AAAA,QAClC,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,QACnC,IAAI,UAAU,MAAM;AAAA,QACpB,MAAM,QAAQ,OAAO,QAAQ,SAAS;AAAA,QACtC,OAAO,QAAQ,OAAO,SAAS,SAAS;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAOA;AACT;;;ADlIA,IAAI,SAAS,OAAO,UAAU,SAAU,KAAK;AAC3C,SAAO,OAAO,KAAK,GAAG,EAAE,IAAI,SAAU,GAAG;AACvC,WAAO,IAAI,CAAC;AAAA,EACd,CAAC;AACH;AAEA,IAAI,eAAe;AAAA,EACjB,WAAW;AAAA,EACX,cAAc,SAAS,aAAa,OAAO;AACzC,WAAO;AAAA,EACT;AACF;AAgBA,IAAI,kBAA+B,SAAU,kBAAkB;AAC7D,iBAAeE,kBAAiB,gBAAgB;AAEhD,WAASA,iBAAgB,OAAO,SAAS;AACvC,QAAI;AAEJ,YAAQ,iBAAiB,KAAK,MAAM,OAAO,OAAO,KAAK;AAEvD,QAAI,eAAe,MAAM,aAAa,KAAK,uBAAuB,KAAK,CAAC;AAGxE,UAAM,QAAQ;AAAA,MACZ,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,iBAAgB;AAE7B,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,MACZ,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,SAAK,UAAU;AAAA,EACjB;AAEA,EAAAA,iBAAgB,2BAA2B,SAAS,yBAAyB,WAAW,MAAM;AAC5F,QAAI,mBAAmB,KAAK,UACxB,eAAe,KAAK,cACpB,cAAc,KAAK;AACvB,WAAO;AAAA,MACL,UAAU,cAAc,uBAAuB,WAAW,YAAY,IAAI,oBAAoB,WAAW,kBAAkB,YAAY;AAAA,MACvI,aAAa;AAAA,IACf;AAAA,EACF;AAGA,SAAO,eAAe,SAAS,aAAa,OAAO,MAAM;AACvD,QAAI,sBAAsB,gBAAgB,KAAK,MAAM,QAAQ;AAC7D,QAAI,MAAM,OAAO,oBAAqB;AAEtC,QAAI,MAAM,MAAM,UAAU;AACxB,YAAM,MAAM,SAAS,IAAI;AAAA,IAC3B;AAEA,QAAI,KAAK,SAAS;AAChB,WAAK,SAAS,SAAU,OAAO;AAC7B,YAAIC,YAAW,SAAS,CAAC,GAAG,MAAM,QAAQ;AAE1C,eAAOA,UAAS,MAAM,GAAG;AACzB,eAAO;AAAA,UACL,UAAUA;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnB,YAAY,YAAY,WACxBC,gBAAe,YAAY,cAC3B,QAAQ,8BAA8B,aAAa,CAAC,aAAa,cAAc,CAAC;AAEpF,QAAI,eAAe,KAAK,MAAM;AAC9B,QAAID,YAAW,OAAO,KAAK,MAAM,QAAQ,EAAE,IAAIC,aAAY;AAC3D,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AAEb,QAAI,cAAc,MAAM;AACtB,aAAoB,cAAAC,QAAM,cAAc,+BAAuB,UAAU;AAAA,QACvE,OAAO;AAAA,MACT,GAAGF,SAAQ;AAAA,IACb;AAEA,WAAoB,cAAAE,QAAM,cAAc,+BAAuB,UAAU;AAAA,MACvE,OAAO;AAAA,IACT,GAAgB,cAAAA,QAAM,cAAc,WAAW,OAAOF,SAAQ,CAAC;AAAA,EACjE;AAEA,SAAOD;AACT,EAAE,cAAAG,QAAM,SAAS;AAEjB,gBAAgB,YAAY,OAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlE,WAAW,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAerB,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYhB,cAAc,mBAAAA,QAAU;AAC1B,IAAI,CAAC;AACL,gBAAgB,eAAe;AAC/B,IAAO,0BAAQ;;;AD1Kf,IAAI,oBAAiC,SAAU,kBAAkB;AAC/D,iBAAeC,oBAAmB,gBAAgB;AAElD,WAASA,qBAAoB;AAC3B,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACxF,YAAM,IAAI,IAAI,UAAU,IAAI;AAAA,IAC9B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC,KAAK;AAE/E,UAAM,cAAc,WAAY;AAC9B,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,WAAW,GAAG,IAAI;AAAA,IACjD;AAEA,UAAM,iBAAiB,WAAY;AACjC,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,cAAc,GAAG,IAAI;AAAA,IACpD;AAEA,UAAM,gBAAgB,WAAY;AAChC,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,aAAa,GAAG,IAAI;AAAA,IACnD;AAEA,UAAM,aAAa,WAAY;AAC7B,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,UAAU,GAAG,IAAI;AAAA,IAChD;AAEA,UAAM,gBAAgB,WAAY;AAChC,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,aAAa,GAAG,IAAI;AAAA,IACnD;AAEA,UAAM,eAAe,WAAY;AAC/B,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,YAAY,GAAG,IAAI;AAAA,IAClD;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,mBAAkB;AAE/B,SAAO,kBAAkB,SAAS,gBAAgB,SAAS,KAAK,cAAc;AAC5E,QAAI;AAEJ,QAAIC,YAAW,KAAK,MAAM;AAC1B,QAAI,QAAQ,cAAAC,QAAM,SAAS,QAAQD,SAAQ,EAAE,GAAG;AAChD,QAAI,MAAM,MAAM,OAAO,EAAG,EAAC,eAAe,MAAM,OAAO,OAAO,EAAE,MAAM,cAAc,YAAY;AAEhG,QAAI,KAAK,MAAM,OAAO,GAAG;AACvB,UAAI,YAAY,MAAM,MAAM,UAAU,SAAY,kBAAAE,QAAS,YAAY,IAAI;AAC3E,WAAK,MAAM,OAAO,EAAE,SAAS;AAAA,IAC/B;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnBF,YAAW,YAAY,UACvB,SAAS,YAAY,IACrB,QAAQ,8BAA8B,aAAa,CAAC,YAAY,IAAI,CAAC;AAEzE,QAAI,wBAAwB,cAAAC,QAAM,SAAS,QAAQD,SAAQ,GACvD,QAAQ,sBAAsB,CAAC,GAC/B,SAAS,sBAAsB,CAAC;AAEpC,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAoB,cAAAC,QAAM,cAAc,yBAAiB,OAAO,SAAS,cAAAA,QAAM,aAAa,OAAO;AAAA,MACjG,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,IAClB,CAAC,IAAI,cAAAA,QAAM,aAAa,QAAQ;AAAA,MAC9B,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,IAClB,CAAC,CAAC;AAAA,EACJ;AAEA,SAAOF;AACT,EAAE,cAAAE,QAAM,SAAS;AAEjB,kBAAkB,YAAY,OAAwC;AAAA,EACpE,IAAI,mBAAAE,QAAU,KAAK;AAAA,EACnB,UAAU,SAAS,SAAS,OAAO,UAAU;AAC3C,QAAI,cAAAF,QAAM,SAAS,MAAM,MAAM,QAAQ,CAAC,MAAM,EAAG,QAAO,IAAI,MAAM,MAAO,WAAW,8CAA+C;AACnI,WAAO;AAAA,EACT;AACF,IAAI,CAAC;AACL,IAAO,4BAAQ;;;AGnIf,IAAAG,gBAAkB;AAClB,IAAAC,qBAAsB;AAHtB,IAAI;AAAJ,IAAmB;AAOnB,SAAS,qBAAqB,aAAa,aAAa;AACtD,MAAI,gBAAgB,YAAa,QAAO;AAExC,MAAI,cAAAC,QAAM,eAAe,WAAW,KAAK,cAAAA,QAAM,eAAe,WAAW,KAAK,YAAY,OAAO,QAAQ,YAAY,QAAQ,YAAY,KAAK;AAC5I,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAOO,IAAI,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI;AACN;AAEA,IAAI,WAAW,SAASC,UAAS,SAAS,MAAM,IAAI;AAClD,SAAO,WAAY;AACjB,QAAI;AAEJ,YAAQ,MAAM,IAAI,MAAM,iBAAiB,QAAQ,OAAO,IAAI,EAAE,MAAM,gBAAgB,SAAS;AAC7F,OAAG;AAAA,EACL;AACF;AAEA,IAAI,gBAAgB,gBAAgB,CAAC,GAAG,cAAc,MAAM,GAAG,IAAI,SAAU,MAAM;AACjF,MAAI,UAAU,KAAK,SACf,cAAc,KAAK;AACvB,SAAO,cAAAD,QAAM,aAAa,SAAS;AAAA,IACjC,IAAI;AAAA,IACJ,UAAU,SAAS,SAAS,YAAY,WAAY;AAClD,kBAAY,UAAU,IAAI;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG,cAAc,MAAM,EAAE,IAAI,SAAU,OAAO;AAC5C,MAAI,UAAU,MAAM,SAChB,cAAc,MAAM,aACpBE,YAAW,MAAM;AACrB,SAAO,CAAC,SAAS,cAAAF,QAAM,aAAaE,WAAU;AAAA,IAC5C,IAAI;AAAA,IACJ,WAAW,SAASA,WAAU,aAAa,WAAY;AACrD,kBAAY,QAAQ;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,CAAC;AACJ,GAAG;AACH,IAAI,gBAAgB,gBAAgB,CAAC,GAAG,cAAc,MAAM,GAAG,IAAI,SAAU,OAAO;AAClF,MAAIA,YAAW,MAAM,UACjB,cAAc,MAAM;AACxB,SAAO,cAAAF,QAAM,aAAaE,WAAU;AAAA,IAClC,IAAI;AAAA,IACJ,WAAW,SAASA,WAAU,aAAa,WAAY;AACrD,kBAAY,SAAS,cAAAF,QAAM,aAAaE,WAAU;AAAA,QAChD,IAAI;AAAA,MACN,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH,CAAC;AACH,GAAG,cAAc,MAAM,EAAE,IAAI,SAAU,OAAO;AAC5C,MAAI,UAAU,MAAM,SAChBA,YAAW,MAAM,UACjB,cAAc,MAAM;AACxB,SAAO,CAAC,cAAAF,QAAM,aAAa,SAAS;AAAA,IAClC,IAAI;AAAA,IACJ,UAAU,SAAS,SAAS,YAAY,WAAY;AAClD,kBAAY,SAAS,cAAAA,QAAM,aAAaE,WAAU;AAAA,QAChD,IAAI;AAAA,MACN,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH,CAAC,GAAG,cAAAF,QAAM,aAAaE,WAAU;AAAA,IAC/B,IAAI;AAAA,EACN,CAAC,CAAC;AACJ,GAAG;AAsDH,IAAI,mBAAgC,SAAU,kBAAkB;AAC9D,iBAAeC,mBAAkB,gBAAgB;AAEjD,WAASA,oBAAmB;AAC1B,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAC9E,UAAM,QAAQ;AAAA,MACZ,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AACA,UAAM,WAAW;AAEjB,UAAM,cAAc,SAAU,QAAQ,SAAS;AAC7C,UAAI,YAAY,QAAQ;AACtB,kBAAU,MAAM,MAAM;AAAA,MACxB;AAEA,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,kBAAiB;AAE9B,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,SAAK,WAAW;AAAA,EAClB;AAEA,EAAAA,kBAAiB,2BAA2B,SAAS,yBAAyB,OAAO,OAAO;AAC1F,QAAI,MAAM,YAAY,MAAM;AAC1B,aAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,YAAY,MAAM,SAAS,MAAM,IAAI;AACxD,aAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,qBAAqB,MAAM,SAAS,MAAM,QAAQ,GAAG;AACxE,aAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO;AAAA,MACL,SAAS,cAAAH,QAAM,aAAa,MAAM,UAAU;AAAA,QAC1C,IAAI;AAAA,MACN,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnBE,YAAW,YAAY,UACvB,OAAO,YAAY,MACnB,cAAc,KAAK,OACnB,SAAS,YAAY,QACrB,UAAU,YAAY;AAC1B,QAAI,OAAO;AAAA,MACT,UAAUA;AAAA,MACV;AAAA,MACA,aAAa,KAAK;AAAA,MAClB;AAAA,IACF;AACA,QAAI;AAEJ,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,oBAAY,aAAa,IAAI,EAAE,IAAI;AACnC;AAAA,MAEF,KAAK;AACH,oBAAY,aAAa,IAAI,EAAE,IAAI;AACnC;AAAA,MAEF,KAAK;AACH,oBAAY;AAAA,IAChB;AAEA,WAAoB,cAAAF,QAAM,cAAc,+BAAuB,UAAU;AAAA,MACvE,OAAO;AAAA,QACL,YAAY,CAAC,KAAK;AAAA,MACpB;AAAA,IACF,GAAG,SAAS;AAAA,EACd;AAEA,SAAOG;AACT,EAAE,cAAAH,QAAM,SAAS;AAEjB,iBAAiB,YAAY,OAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnE,MAAM,mBAAAI,QAAU,MAAM,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA,EAK3C,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,UAAU,CAAC;AAC9D,IAAI,CAAC;AACL,iBAAiB,eAAe;AAAA,EAC9B,MAAM,MAAM;AACd;AACA,IAAO,2BAAQ;", "names": ["import_prop_types", "import_react", "import_prop_types", "import_react", "PropTypes", "React", "forceReflow", "Transition", "timeout", "ReactDOM", "children", "React", "PropTypes", "addClass", "removeClass", "CSSTransition", "React", "PropTypes", "import_prop_types", "import_react", "import_react_dom", "import_prop_types", "import_react", "import_react", "children", "mapper", "TransitionGroup", "children", "childFactory", "React", "PropTypes", "ReplaceTransition", "children", "React", "ReactDOM", "PropTypes", "import_react", "import_prop_types", "React", "callHook", "children", "SwitchTransition", "PropTypes"]}