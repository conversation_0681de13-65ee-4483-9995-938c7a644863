import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { FaTrash, FaEye, FaTimes, FaFileExport, FaFileImport, FaFilter } from 'react-icons/fa';
import { motion } from 'framer-motion';
import * as XLSX from 'xlsx';
import { useAuth } from '../../context/NewAuthContext';

const PaymentVoucherTransaction = ({ onClose }) => {
  const [transactions, setTransactions] = useState([]);
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selected, setSelected] = useState(null);
  const [viewMode, setViewMode] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    voucherNo: '',
    paymentMethod: '',
    fromDate: '',
    toDate: '',
    minAmount: '',
    maxAmount: '',
  });

  const { user: authUser } = useAuth ? useAuth() : { user: null };

  useEffect(() => {
    fetchTransactions();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [transactions, filters]);

  const fetchTransactions = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await axios.get('http://127.0.0.1:8000/api/payment-vouchers');
      setTransactions(res.data.data || []);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch transactions');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let result = [...transactions];

    if (filters.voucherNo) {
      result = result.filter((txn) =>
        txn.voucher_no.toLowerCase().includes(filters.voucherNo.toLowerCase())
      );
    }

    if (filters.paymentMethod) {
      result = result.filter((txn) => txn.payment_method === filters.paymentMethod);
    }

    if (filters.fromDate) {
      result = result.filter((txn) => new Date(txn.payment_date) >= new Date(filters.fromDate));
    }

    if (filters.toDate) {
      result = result.filter((txn) => new Date(txn.payment_date) <= new Date(filters.toDate));
    }

    if (filters.minAmount) {
      result = result.filter((txn) => parseFloat(txn.amount) >= parseFloat(filters.minAmount));
    }

    if (filters.maxAmount) {
      result = result.filter((txn) => parseFloat(txn.amount) <= parseFloat(filters.maxAmount));
    }

    setFilteredTransactions(result);
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  const resetFilters = () => {
    setFilters({
      voucherNo: '',
      paymentMethod: '',
      fromDate: '',
      toDate: '',
      minAmount: '',
      maxAmount: '',
    });
  };

  const handleView = async (id) => {
    setLoading(true);
    setError(null);
    try {
      const res = await axios.get(`http://127.0.0.1:8000/api/payment-vouchers/${id}`);
      setSelected(res.data.data);
      setViewMode(true);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch transaction details');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (id) => {
    setDeleteId(id);
  };

  const confirmDelete = async () => {
    if (!deleteId) return;
    setLoading(true);
    setError(null);
    try {
      // Try to get user ID from context, then localStorage, then sessionStorage
      let userId = authUser?.id;
      let token = authUser?.token;
      if (!userId || !token) {
        let user = null;
        if (localStorage.getItem("user")) {
          user = JSON.parse(localStorage.getItem("user"));
        } else if (sessionStorage.getItem("user")) {
          user = JSON.parse(sessionStorage.getItem("user"));
        }
        userId = user?.id;
        token = user?.token;
      }
      if (!userId) {
        setError('No user ID found. Please log in again.');
        setLoading(false);
        return;
      }
      const response = await axios.delete(`http://127.0.0.1:8000/api/payment-vouchers/${deleteId}`, {
        headers: { Authorization: `Bearer ${token}` },
        data: { deleted_by: userId },
      });
      setDeleteId(null);
      fetchTransactions();
      setError(response.data.message || 'Voucher deleted successfully');
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to delete transaction');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'LKR' }).format(value);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const exportToExcel = () => {
    const dataToExport = filteredTransactions.map((txn) => ({
      'Voucher No': txn.voucher_no,
      'Transaction Type': txn.transaction_type,
      'Refer Type': txn.refer_type,
      'Refer Name': txn.refer_name,
      Amount: txn.amount,
      'Payment Date': formatDate(txn.payment_date),
      'Payment Method': txn.payment_method,
      'Cheque No': txn.cheque_no || 'N/A',
      'Bank Name': txn.bank_name || 'N/A',
      'Issue Date': formatDate(txn.issue_date),
      Note: txn.note || 'N/A',
    }));

    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Transactions');
    XLSX.writeFile(workbook, 'Payment_Vouchers.xlsx');
  };

  const handleFileImport = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        const importData = jsonData.map((item) => ({
          voucher_no: item['Voucher No'] || '',
          transaction_type: item['Transaction Type'] || '',
          refer_type: item['Refer Type'] || '',
          refer_name: item['Refer Name'] || '',
          amount: parseFloat(item['Amount']) || 0,
          payment_date: item['Payment Date'] || new Date().toISOString().split('T')[0],
          payment_method: item['Payment Method'] || 'Cash',
          cheque_no: item['Cheque No'] || '',
          bank_name: item['Bank Name'] || '',
          issue_date: item['Issue Date'] || '',
          note: item['Note'] || '',
        }));

        setLoading(true);
        alert(`${importData.length} records ready for import. In a real app, this would send to your API.`);
      } catch (err) {
        setError('Failed to process import file');
        console.error(err);
      } finally {
        setLoading(false);
        e.target.value = '';
      }
    };
    reader.readAsArrayBuffer(file);
  };

  return (
    <div className="relative space-y-4">
      {error && (
        <div
          className={`p-4 rounded-lg ${
            error.includes('successfully')
              ? 'text-green-700 bg-green-100 dark:bg-green-900 dark:text-green-100'
              : 'text-red-700 bg-red-100 dark:bg-red-900 dark:text-red-100'
          }`}
        >
          {error}
        </div>
      )}

      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4 md:items-center md:justify-between">
        <div className="flex space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center px-4 py-2 space-x-2 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700"
          >
            <FaFilter className="w-4 h-4" />
            <span>Filters</span>
          </motion.button>

          {showFilters && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={resetFilters}
              className="flex items-center px-4 py-2 space-x-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500"
            >
              <span>Reset Filters</span>
            </motion.button>
          )}
        </div>

        <div className="flex space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={exportToExcel}
            className="flex items-center px-4 py-2 space-x-2 text-white bg-green-600 rounded-lg hover:bg-green-700"
          >
            <FaFileExport className="w-4 h-4" />
            <span>Export Excel</span>
          </motion.button>

          <label className="flex items-center px-4 py-2 space-x-2 text-white bg-blue-600 rounded-lg cursor-pointer hover:bg-blue-700">
            <FaFileImport className="w-4 h-4" />
            <span>Import Excel</span>
            <input
              type="file"
              accept=".xlsx, .xls"
              onChange={handleFileImport}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="p-4 bg-gray-100 rounded-lg dark:bg-gray-700"
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Voucher No</label>
              <input
                type="text"
                name="voucherNo"
                value={filters.voucherNo}
                onChange={handleFilterChange}
                className="w-full p-2 mt-1 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                placeholder="Filter by voucher no"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Payment Method</label>
              <select
                name="paymentMethod"
                value={filters.paymentMethod}
                onChange={handleFilterChange}
                className="w-full p-2 mt-1 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              >
                <option value="">All Methods</option>
                <option value="Cash">Cash</option>
                <option value="Card">Card</option>
                <option value="Cheque">Cheque</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">From Date</label>
              <input
                type="date"
                name="fromDate"
                value={filters.fromDate}
                onChange={handleFilterChange}
                className="w-full p-2 mt-1 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">To Date</label>
              <input
                type="date"
                name="toDate"
                value={filters.toDate}
                onChange={handleFilterChange}
                className="w-full p-2 mt-1 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Min Amount</label>
              <input
                type="number"
                name="minAmount"
                value={filters.minAmount}
                onChange={handleFilterChange}
                className="w-full p-2 mt-1 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                placeholder="Minimum amount"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Max Amount</label>
              <input
                type="number"
                name="maxAmount"
                value={filters.maxAmount}
                onChange={handleFilterChange}
                className="w-full p-2 mt-1 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                placeholder="Maximum amount"
              />
            </div>
          </div>
        </motion.div>
      )}

      {loading && (
        <div className="flex items-center justify-center p-8">
          <div className="w-12 h-12 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
        </div>
      )}

      {!loading && (
        <div className="overflow-x-auto">
          <div className="inline-block min-w-full py-2 align-middle">
            <div className="overflow-hidden rounded-lg shadow-sm ring-1 ring-black ring-opacity-5 dark:ring-gray-600">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                      Voucher No
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                      Payment Date
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                      Method
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase dark:text-gray-300">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                  {filteredTransactions.length > 0 ? (
                    filteredTransactions.map((txn) => (
                      <tr key={txn.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900 dark:text-white">{txn.voucher_no}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-gray-900 dark:text-white">{formatCurrency(txn.amount)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-gray-900 dark:text-white">{formatDate(txn.payment_date)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              txn.payment_method === 'Cash'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : txn.payment_method === 'Cheque'
                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                            }`}
                          >
                            {txn.payment_method}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                          <div className="flex justify-end space-x-2">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleView(txn.id)}
                              className="p-2 text-blue-600 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900"
                              title="View"
                            >
                              <FaEye className="w-4 h-4" />
                            </motion.button>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleDelete(txn.id)}
                              className="p-2 text-red-600 rounded-full hover:bg-red-100 dark:hover:bg-red-900"
                              title="Delete"
                            >
                              <FaTrash className="w-4 h-4" />
                            </motion.button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="5" className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        No transactions found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {viewMode && selected && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="w-full max-w-2xl p-6 mx-4 bg-white rounded-lg shadow-xl dark:bg-gray-800"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold">Transaction Details</h3>
              <button
                onClick={() => setViewMode(false)}
                className="p-2 text-gray-500 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Voucher No</label>
                  <p className="mt-1 text-gray-900 dark:text-white">{selected.voucher_no}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Transaction Type</label>
                  <p className="mt-1 text-gray-900 dark:text-white">{selected.transaction_type}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Refer Type</label>
                  <p className="mt-1 text-gray-900 dark:text-white">{selected.refer_type}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Refer Name</label>
                  <p className="mt-1 text-gray-900 dark:text-white">{selected.refer_name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Amount</label>
                  <p className="mt-1 text-gray-900 dark:text-white">{formatCurrency(selected.amount)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Payment Date</label>
                  <p className="mt-1 text-gray-900 dark:text-white">{formatDate(selected.payment_date)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Payment Method</label>
                  <p className="mt-1 text-gray-900 dark:text-white">{selected.payment_method}</p>
                </div>
                {selected.payment_method === 'Cheque' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Cheque No</label>
                      <p className="mt-1 text-gray-900 dark:text-white">{selected.cheque_no || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Bank Name</label>
                      <p className="mt-1 text-gray-900 dark:text-white">{selected.bank_name || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Issue Date</label>
                      <p className="mt-1 text-gray-900 dark:text-white">{formatDate(selected.issue_date)}</p>
                    </div>
                  </>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">Note</label>
                <p className="mt-1 text-gray-900 dark:text-white">{selected.note || 'N/A'}</p>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Delete Confirmation */}
      {deleteId && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="w-full max-w-md p-6 mx-4 bg-white rounded-lg shadow-xl dark:bg-gray-800"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold">Confirm Delete</h3>
              <button
                onClick={() => setDeleteId(null)}
                className="p-2 text-gray-500 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              Are you sure you want to delete this transaction? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteId(null)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="flex items-center px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <svg
                      className="w-4 h-4 mr-2 -ml-1 animate-spin"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Deleting...
                  </>
                ) : (
                  <>
                    <FaTrash className="w-4 h-4 mr-2" />
                    Delete
                  </>
                )}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default PaymentVoucherTransaction;