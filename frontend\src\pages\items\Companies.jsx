import React, { useState, useEffect } from "react";
import axios from "axios";
import CompanyForm from "../../components/companies/CompaniesForm";

const Companies = () => {
  const [companies, setCompanies] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState(null);

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/add-companies");
      setCompanies(response.data);
    } catch (error) {
      console.error("Error fetching companies:", error);
    }
  };

  return (
    <div>
      <CompanyForm
        company={selectedCompany}
        onSuccess={() => {
          setSelectedCompany(null);
          fetchCompanies();
        }}
      />
    </div>
  );
};

export default Companies;
