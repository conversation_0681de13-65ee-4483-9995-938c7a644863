import React, { useState, useEffect } from "react";
import axios from "axios";
import { useRegister } from "../../context/RegisterContext";

const TemplateManager = () => {
  const { getAuthHeaders } = useRegister();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(
        "http://127.0.0.1:8000/api/invoice-templates",
        {
          headers: getAuthHeaders().headers,
        }
      );
      setTemplates(response.data.data || []);
    } catch (err) {
      console.error("Error fetching templates:", err);
      setError(
        err.response?.data?.error ||
          "Failed to load templates. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const setDefaultTemplate = async (id) => {
    try {
      await axios.post(
        `http://127.0.0.1:8000/api/invoiceTemplates/${id}/set-default`,
        {},
        { headers: getAuthHeaders().headers }
      );
      alert("Default template set successfully!");
      fetchTemplates(); // Refresh the table
    } catch (err) {
      console.error("Error setting default template:", err);
      alert("Failed to set default template.");
    }
  };

  return (
    <div className="p-6 bg-gray-100 dark:bg-gray-900">
      <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-200">
        Manage Invoice Templates
      </h2>
      {error && <p className="text-red-500 mb-4">{error}</p>}
      {loading ? (
        <p>Loading templates...</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white dark:bg-gray-800 border rounded shadow">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-700">
                <th className="px-4 py-2 text-left text-gray-600 dark:text-gray-300">
                  Name
                </th>
                <th className="px-4 py-2 text-left text-gray-600 dark:text-gray-300">
                  Type
                </th>
                <th className="px-4 py-2 text-left text-gray-600 dark:text-gray-300">
                  Size
                </th>
                <th className="px-4 py-2 text-left text-gray-600 dark:text-gray-300">
                  Default
                </th>
                <th className="px-4 py-2 text-left text-gray-600 dark:text-gray-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {templates.length === 0 ? (
                <tr>
                  <td
                    colSpan="5"
                    className="px-4 py-2 text-center text-gray-500 dark:text-gray-400"
                  >
                    No templates found.
                  </td>
                </tr>
              ) : (
                templates.map((template) => (
                  <tr
                    key={template.id}
                    className="border-t dark:border-gray-700"
                  >
                    <td className="px-4 py-2 text-gray-800 dark:text-gray-200">
                      {template.name}
                    </td>
                    <td className="px-4 py-2 text-gray-800 dark:text-gray-200">
                      {template.type}
                    </td>
                    <td className="px-4 py-2 text-gray-800 dark:text-gray-200">
                      {template.width}mm x {template.height}mm
                    </td>
                    <td className="px-4 py-2 text-gray-800 dark:text-gray-200">
                      {template.is_default ? (
                        <span className="text-green-500">Default</span>
                      ) : (
                        "No"
                      )}
                    </td>
                    <td className="px-4 py-2">
                      {!template.is_default && (
                        <button
                          onClick={() => setDefaultTemplate(template.id)}
                          className="px-3 py-1 text-sm text-white bg-indigo-600 rounded hover:bg-indigo-700"
                        >
                          Set as Default
                        </button>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default TemplateManager;
