import React, { useState, useEffect } from "react";
import axios from "axios";
import { Printer, Calendar, Filter, Loader2 } from "lucide-react";
import { motion } from "framer-motion";

const RegistryReports = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [endDate, setEndDate] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });

  const fetchReports = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        "http://127.0.0.1:8000/api/register/report",
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
          timeout: 30000,
        }
      );
      setReports(response.data.reports || []);
    } catch (error) {
      console.error("Failed to fetch registry reports:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReports();
  }, [startDate, endDate]);

  const printMiniView = (report) => {
    const printWindow = window.open("", "_blank");
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Register Report</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
          body { 
            font-family: 'Inter', sans-serif; 
            margin: 0; 
            padding: 24px; 
            background-color: #f8fafc;
            color: #0f172a;
          }
          .report-container {
            max-width: 420px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 28px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
          }
          .report-header { 
            text-align: center; 
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f1f5f9;
          }
          .report-title { 
            font-size: 22px; 
            font-weight: 700; 
            color: #0f172a;
            margin-bottom: 6px;
            letter-spacing: -0.5px;
          }
          .report-subtitle { 
            font-size: 13px; 
            color: #64748b; 
            font-weight: 500;
          }
          .report-body { 
            margin-top: 20px; 
          }
          .report-row { 
            display: flex; 
            justify-content: space-between; 
            padding: 10px 0;
            font-size: 14px;
          }
          .report-label { 
            font-weight: 500;
            color: #475569;
          }
          .report-value {
            font-weight: 600;
            color: #0f172a;
          }
          .report-divider { 
            border-top: 1px dashed #e2e8f0; 
            margin: 14px 0; 
          }
          .report-footer { 
            margin-top: 28px; 
            font-size: 12px; 
            text-align: center;
            color: #64748b;
            padding-top: 18px;
            border-top: 1px solid #f1f5f9;
            font-weight: 500;
          }
          .highlight-row {
            background-color: #f8fafc;
            margin: 0 -28px;
            padding: 10px 28px;
            border-radius: 8px;
          }
          .currency {
            font-family: monospace;
          }
          @media print {
            @page { 
              size: auto; 
              margin: 10mm;
            }
            body {
              padding: 0;
              background-color: white;
            }
            .report-container {
              box-shadow: none;
              padding: 0;
              border: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="report-container">
          <div class="report-header">
            <div class="report-title">REGISTER REPORT</div>
            <div class="report-subtitle">${new Date().toLocaleString()}</div>
          </div>
          
          <div class="report-body">
            <div class="report-row">
              <span class="report-label">User:</span>
              <span class="report-value">${report.user?.name || "N/A"}</span>
            </div>
            
            <div class="report-row">
              <span class="report-label">Status:</span>
              <span class="report-value">${report.status}</span>
            </div>
            <div class="report-row">
              <span class="report-label">Opened At:</span>
              <span class="report-value">${new Date(report.opened_at).toLocaleString()}</span>
            </div>
            <div class="report-row">
              <span class="report-label">Closed At:</span>
              <span class="report-value">${report.closed_at ? new Date(report.closed_at).toLocaleString() : "N/A"}</span>
            </div>
            
            <div class="report-divider"></div>
            
            <div class="report-row highlight-row">
              <span class="report-label">Opening Cash:</span>
              <span class="report-value currency">LKR ${(Number(report.opening_balance) || 0).toFixed(2)}</span>
            </div>
            <div class="report-row">
              <span class="report-label">Total Sales:</span>
              <span class="report-value currency">LKR ${(Number(report.total_sales) || 0).toFixed(2)}</span>
            </div>
            <div class="report-row">
              <span class="report-label">Total Quantity:</span>
              <span class="report-value">${report.total_sales_qty || "0"}</span>
            </div>
            
            <div class="report-divider"></div>
            
            <div class="report-row highlight-row">
              <span class="report-label">Closing Balance:</span>
              <span class="report-value currency">LKR ${(Number(report.closing_balance) || 0).toFixed(2)}</span>
            </div>
            <div class="report-row">
              <span class="report-label">Actual Cash:</span>
              <span class="report-value currency">LKR ${(Number(report.opening_balance) + Number(report.total_sales) || 0).toFixed(2)}</span>
            </div>
          </div>
          
          <div class="report-footer">
            <div>System generated report - ${new Date().toLocaleDateString()}</div>
            <div>Thank you!</div>
          </div>
        </div>
        
        <script>
          window.onload = function() {
            setTimeout(function() {
              window.print();
              window.close();
            }, 200);
          }
        </script>
      </body>
      </html>
    `);
    printWindow.document.close();
  };

  return (
    <div className="p-6 max-w-screen mx-auto">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2 tracking-tight">
          Registry Reports
        </h1>
        <p className="text-slate-600">
          View and manage register opening/closing reports
        </p>
      </motion.div>

      <div className="bg-white dark:bg-slate-800 dark:text-white shadow rounded-xl p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h2 className="text-xl font-semibold text-slate-800 dark:text-white tracking-tight">
            Filter Reports
          </h2>
          <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
            <div className="flex items-center gap-2 bg-slate-50  dark:bg-slate-800 dark:text-white rounded-lg px-3 py-2  hover:border-slate-300 transition-colors">
              <Calendar className="text-slate-500" size={18} />
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="bg-transparent text-slate-700 focus:outline-none text-sm font-medium"
              />
            </div>
            <span className="text-slate-400 hidden sm:block">to</span>
            <div className="flex items-center gap-2 bg-slate-50  dark:bg-slate-800 dark:text-white rounded-lg px-3 py-2  hover:border-slate-300 transition-colors">
              <Calendar className="text-slate-500" size={18} />
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="bg-transparent text-slate-700 focus:outline-none text-sm font-medium"
              />
            </div>
            <button
              onClick={fetchReports}
              disabled={loading}
              className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium shadow-sm hover:shadow-md"
            >
              {loading ? (
                <Loader2 className="animate-spin" size={18} />
              ) : (
                <Filter size={18} />
              )}
              Filter
            </button>
          </div>
        </div>

        <div className="w-100% overflow-x-auto">
          {/* ── Scrollable wrapper for both header and body ───────────────────── */}
          <div className="max-h-[28rem] overflow-y-auto">
            <table className="min-w-[60rem] w-full divide-y divide-slate-200 dark:divide-slate-700 bg-white dark:bg-slate-800">
              {/* ── Table Head ─────────────────────────────────────────────── */}
              <thead className="bg-slate-100 dark:bg-slate-700/70 sticky top-0 z-10 backdrop-blur supports-[backdrop-filter]:bg-slate-100/80 dark:supports-[backdrop-filter]:bg-slate-700/60">
                <tr className="text-left text-sm font-semibold text-slate-600 dark:text-slate-100 uppercase tracking-wide">
                  {[
                    "User",
                    "Opened At",
                    "Closed At",
                    "Opening Cash",
                    "Total Sales",
                    "Closing Balance",
                    "Actual Cash",
                    "Status",
                    "Actions",
                  ].map((h, i) => (
                    <th
                      key={h}
                      className={`px-4 py-3.5 whitespace-nowrap ${
                        i === 0
                          ? "rounded-tl-xl"
                          : i === 8
                            ? "rounded-tr-xl"
                            : ""
                      }`}
                    >
                      {h}
                    </th>
                  ))}
                </tr>
              </thead>

              {/* ── Table Body ─────────────────────────────────────────────── */}
              <tbody className="divide-y divide-slate-200 dark:divide-slate-700 text-sm">
                {/* Loading state */}
                {loading && (
                  <tr>
                    <td
                      colSpan={9}
                      className="py-10 text-center text-slate-500 dark:text-slate-400"
                    >
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="animate-spin" size={20} />
                        Loading reports…
                      </div>
                    </td>
                  </tr>
                )}

                {/* Empty state */}
                {!loading && reports.length === 0 && (
                  <tr>
                    <td
                      colSpan={9}
                      className="py-10 text-center text-slate-500 dark:text-slate-400"
                    >
                      No reports found for the selected date range.
                    </td>
                  </tr>
                )}

                {/* Data rows */}
                {reports.map((report) => (
                  <tr
                    key={report.id}
                    className="hover:bg-slate-50 dark:hover:bg-slate-700/40 transition-colors even:bg-slate-50/50 dark:even:bg-slate-700/20"
                  >
                    {/* User */}
                    <td className="px-4 py-3 font-medium text-slate-800 dark:text-slate-50 whitespace-nowrap">
                      {report.user?.name || "N/A"}
                    </td>

                    {/* Opened At */}
                    <td className="px-4 py-3 text-slate-700 dark:text-slate-200 whitespace-nowrap">
                      {new Date(report.opened_at).toLocaleString()}
                    </td>

                    {/* Closed At */}
                    <td className="px-4 py-3 text-slate-700 dark:text-slate-200 whitespace-nowrap">
                      {report.closed_at
                        ? new Date(report.closed_at).toLocaleString()
                        : "-"}
                    </td>

                    {/* Opening Cash */}
                    <td className="px-4 py-3 font-mono font-semibold text-slate-900 dark:text-slate-100 whitespace-nowrap">
                      LKR {(Number(report.opening_balance) || 0).toFixed(2)}
                    </td>

                    {/* Total Sales */}
                    <td className="px-4 py-3 font-mono font-semibold text-slate-900 dark:text-slate-100 whitespace-nowrap">
                      LKR {(Number(report.total_sales) || 0).toFixed(2)}
                    </td>

                    {/* Closing Balance */}
                    <td className="px-4 py-3 font-mono font-semibold text-slate-900 dark:text-slate-100 whitespace-nowrap">
                      LKR {(Number(report.closing_balance) || 0).toFixed(2)}
                    </td>

                    {/* Actual Cash */}
                    <td className="px-4 py-3 font-mono font-semibold text-slate-900 dark:text-slate-100 whitespace-nowrap">
                      LKR{" "}
                      {(
                        Number(report.opening_balance) +
                          Number(report.total_sales) || 0
                      ).toFixed(2)}
                    </td>

                    {/* Status */}
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span
                        className={
                          `inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ` +
                          (report.status === "open"
                            ? "bg-emerald-100 text-emerald-800 dark:bg-emerald-400/10 dark:text-emerald-300"
                            : "bg-indigo-100 text-indigo-800 dark:bg-indigo-400/10 dark:text-indigo-300")
                        }
                      >
                        {report.status}
                      </span>
                    </td>

                    {/* Actions */}
                    <td className="px-4 py-3 whitespace-nowrap">
                      <button
                        onClick={() => printMiniView(report)}
                        className="inline-flex items-center gap-1.5 rounded-lg border border-slate-300 bg-white px-3 py-1.5 text-sm font-medium text-slate-700 shadow-sm transition-colors hover:bg-slate-50 dark:border-slate-600 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600"
                      >
                        <Printer
                          size={16}
                          className="text-slate-500 dark:text-slate-300"
                        />
                        Print
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegistryReports;
