import React, { useEffect, useState, useMemo } from "react";
import axios from "axios";
import { FiPackage, FiShoppingBag, FiTrendingUp, FiTrendingDown, FiCalendar, FiPrinter } from "react-icons/fi";
import { Bar, Pie } from "react-chartjs-2";
import { Chart, registerables } from 'chart.js';
Chart.register(...registerables);

// Helper functions
const getAgingDays = (createdAt) => {
  if (!createdAt) return null;
  const created = new Date(createdAt);
  const now = new Date();
  return Math.floor((now - created) / (1000 * 60 * 60 * 24));
};

// Helper to split product name into two lines if too long
const splitProductName = (name, maxLen = 20) => {
  if (!name || name.length <= maxLen) return [name];
  // Find the last space before maxLen
  const idx = name.lastIndexOf(' ', maxLen);
  if (idx === -1) return [name.slice(0, maxLen), name.slice(maxLen)];
  return [name.slice(0, idx), name.slice(idx + 1)];
};

const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value || 0);
}

const agingCategory = (days) => {
  if (days === null) return "Unknown";
  if (days <= 30) return "0-30 days";
  if (days <= 60) return "31-60 days";
  if (days <= 90) return "61-90 days";
  return "90+ days";
};

const ItemAgeAnalyze = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [transactions, setTransactions] = useState(null);
  const [filter, setFilter] = useState("all");
  const [sortConfig, setSortConfig] = useState({ key: "aging", direction: "desc" });
  const [search, setSearch] = useState("");

  useEffect(() => {
    const fetchAll = async () => {
      setLoading(true);
      try {
        const [prodRes, salesRes, invoicesRes, purchasesRes] = await Promise.all([
          axios.get("http://127.0.0.1:8000/api/products"),
          axios.get("http://127.0.0.1:8000/api/sales"),
          axios.get("http://127.0.0.1:8000/api/invoices?per_page=1000"),
          axios.get("http://127.0.0.1:8000/api/purchases"),
        ]);

        const productsData = Array.isArray(prodRes.data.data) ? prodRes.data.data : Array.isArray(prodRes.data) ? prodRes.data : [];
        setProducts(productsData);

        const invoiceData = invoicesRes.data?.data || invoicesRes.data || [];
        const invoiceSales = invoiceData.map((invoice) => ({
          id: `invoice_${invoice.id}`,
          created_at: invoice.invoice_date || invoice.created_at,
          items: invoice.items || [],
        }));

        setTransactions({
          sales: [...(salesRes.data || []), ...invoiceSales],
          purchases: purchasesRes.data?.data || purchasesRes.data || [],
        });
      } catch (err) {
        console.error("Fetch error:", err);
        setError("Failed to fetch data. Please try again.");
      } finally {
        setLoading(false);
      }
    };
    fetchAll();
  }, []);

  const calculateVariantStats = (variant, transactions) => {
    if (!transactions) return { totalPurchased: variant.opening_stock_quantity || 0, purchased: 0, sold: 0, closingStock: variant.closing_stock_quantity || variant.opening_stock_quantity || 0 };
    
    const { sales, purchases } = transactions;
    
    const sold = (Array.isArray(sales) ? sales : []).reduce((sum, sale) => {
      const items = sale.items || [];
      return (
        sum +
        items
          .filter(
            (item) =>
              (item.product_variant_id && item.product_variant_id === variant.product_variant_id) ||
              (item.batch_number && item.batch_number === variant.batch_number)
          )
          .reduce((s, item) => s + parseFloat(item.quantity || 0), 0)
      );
    }, 0);

    const purchased = (Array.isArray(purchases) ? purchases : []).reduce((sum, purchase) => {
      const items = purchase.items || [];
      return (
        sum +
        items
          .filter(
            (item) =>
              (item.product_variant_id && item.product_variant_id === variant.product_variant_id) ||
              (item.batch_number && item.batch_number === variant.batch_number)
          )
          .reduce((s, item) => s + parseFloat(item.quantity || 0), 0)
      );
    }, 0);

    let closingStock = parseFloat(variant.closing_stock_quantity ?? 0);
    if (!closingStock && variant.closing_stock_quantity == null) {
      closingStock = (variant.opening_stock_quantity || 0) + purchased - sold;
    }
    const openingStock = parseFloat(variant.opening_stock_quantity || 0);
    const totalPurchased = openingStock + purchased;
    
    return { totalPurchased, purchased, sold, closingStock };
  };

  // Process all variants with stats
  const allVariants = useMemo(() => {
    return products.flatMap((product) =>
      (product.variants || []).map((variant) => {
        const stats = calculateVariantStats(variant, transactions);
        const aging = getAgingDays(variant.created_at || product.created_at);
        return {
          ...variant,
          product_name: product.product_name,
          product_category: product.category,
          created_at: variant.created_at || product.created_at,
          aging,
          agingCategory: agingCategory(aging),
          ...stats,
          value: (variant.buying_cost || 0) * stats.closingStock
        };
      })
    );
  }, [products, transactions]);

  // Filter variants based on age category and search query
  const filteredVariants = useMemo(() => {
    let variants = allVariants;
    if (filter !== "all") {
      variants = variants.filter(v => v.agingCategory === filter);
    }
    if (search.trim() !== "") {
      const q = search.trim().toLowerCase();
      variants = variants.filter(v =>
        (v.product_name && v.product_name.toLowerCase().includes(q)) ||
        (v.product_category && v.product_category.toLowerCase().includes(q)) ||
        (v.batch_number && v.batch_number.toLowerCase().includes(q)) ||
        (v.product_variant_id && String(v.product_variant_id).toLowerCase().includes(q))
      );
    }
    return variants;
  }, [allVariants, filter, search]);

  // Sort variants
  const sortedVariants = useMemo(() => {
    const sortableItems = [...filteredVariants];
    if (sortConfig.key) {
      sortableItems.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [filteredVariants, sortConfig]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const totalItems = allVariants.length;
    const totalStock = allVariants.reduce((sum, v) => sum + v.closingStock, 0);
    const totalValue = allVariants.reduce((sum, v) => sum + (v.value || 0), 0);
    const agingDistribution = allVariants.reduce((acc, v) => {
      acc[v.agingCategory] = (acc[v.agingCategory] || 0) + 1;
      return acc;
    }, {});

    return {
      totalItems,
      totalStock,
      totalValue: new Intl.NumberFormat('Srilanka', {
        style: 'currency',
        currency: 'LKR'
      }).format(totalValue),
      avgAge: allVariants.length > 0 
        ? Math.round(allVariants.reduce((sum, v) => sum + (v.aging || 0), 0) / allVariants.length)
        : 0,
      agingDistribution
    };
  }, [allVariants]);

  // Prepare chart data
  const chartData = useMemo(() => {
    const agingData = Object.entries(summaryStats.agingDistribution).map(([key, value]) => ({
      label: key,
      value
    }));

    return {
      agingChart: {
        labels: agingData.map(d => d.label),
        datasets: [{
          data: agingData.map(d => d.value),
          backgroundColor: [
            '#4ade80', // green
            '#60a5fa', // blue
            '#fbbf24', // yellow
            '#f87171', // red
            '#9ca3af'  // gray
          ],
          borderWidth: 1
        }]
      },
      valueByAge: {
        labels: ["0-30 days", "31-60 days", "61-90 days", "90+ days"],
        datasets: [{
          label: 'Inventory Value by Age',
          data: [
            allVariants.filter(v => v.agingCategory === "0-30 days").reduce((sum, v) => sum + (v.value || 0), 0),
            allVariants.filter(v => v.agingCategory === "31-60 days").reduce((sum, v) => sum + (v.value || 0), 0),
            allVariants.filter(v => v.agingCategory === "61-90 days").reduce((sum, v) => sum + (v.value || 0), 0),
            allVariants.filter(v => v.agingCategory === "90+ days").reduce((sum, v) => sum + (v.value || 0), 0)
          ],
          backgroundColor: '#3b82f6',
          borderColor: '#1d4ed8',
          borderWidth: 1
        }]
      }
    };
  }, [allVariants, summaryStats]);

  // Handle sort request
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Print function
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow pop-ups to print this report');
      return;
    }

    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Item Age Analysis Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
          .header { text-align: center; margin-bottom: 20px; }
          .report-title { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
          .report-subtitle { font-size: 16px; color: #666; margin-bottom: 20px; }
          .summary-cards { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; }
          .card { border: 1px solid #ddd; border-radius: 5px; padding: 10px; }
          .card-title { margin: 0 0 5px 0; font-size: 14px; color: #666; }
          .card-value { margin: 0; font-size: 18px; font-weight: bold; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .aging-0 { background-color: #f0fdf4; }
          .aging-1 { background-color: #eff6ff; }
          .aging-2 { background-color: #fef9c3; }
          .aging-3 { background-color: #fee2e2; }
          .aging-unknown { background-color: #f3f4f6; }
          .footer { margin-top: 30px; font-size: 12px; color: #999; text-align: center; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="report-title">Item Age Analysis Report</div>
          <div class="report-subtitle">Generated on ${new Date().toLocaleString()}</div>
        </div>
        
        <div class="summary-cards">
          <div class="card">
            <div class="card-title">Total Variants</div>
            <div class="card-value">${summaryStats.totalItems}</div>
          </div>
          <div class="card">
            <div class="card-title">Total Stock</div>
            <div class="card-value">${summaryStats.totalStock}</div>
          </div>
          <div class="card">
            <div class="card-title">Total Value</div>
            <div class="card-value">LKR ${summaryStats.totalValue}</div>
          </div>
          <div class="card">
            <div class="card-title">Average Age</div>
            <div class="card-value">${Math.round(summaryStats.avgAge)} days</div>
          </div>
        </div>
        
        <table>
          <thead>
            <tr>
              <th>Product Name</th>
              <th>Batch/Variant</th>
              <th>Created At</th>
              <th>Aging (days)</th>
              <th>Purchased</th>
              <th>Sold</th>
              <th>Current Stock</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            ${sortedVariants.map(variant => {
              const agingClass = 
                variant.agingCategory === "0-30 days" ? "aging-0" :
                variant.agingCategory === "31-60 days" ? "aging-1" :
                variant.agingCategory === "61-90 days" ? "aging-2" :
                variant.agingCategory === "90+ days" ? "aging-3" : "aging-unknown";
              
              return `
                <tr class="${agingClass}">
                  <td>${variant.product_name}</td>
                  <td>${variant.batch_number || variant.product_variant_id || "-"}</td>
                  <td>${formatDate(variant.created_at)}</td>
                  <td>${variant.aging ?? "N/A"}</td>
                  <td>${(variant.purchased ?? 0) + (variant.opening_stock_quantity ?? 0)}</td>
                  <td>${variant.sold}</td>
                  <td>${variant.closingStock}</td>
                  <td>${new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(variant.value || 0)}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
        
        <div class="footer">
          Report generated by Inventory Management System
        </div>
        
        <script>
          window.onload = () => window.print();
        </script>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
  };

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Item Age Analysis</h1>
            <p className="text-gray-600">Track inventory aging and identify slow-moving items</p>
          </div>
          <button
            onClick={handlePrint}
            className="mt-4 md:mt-0 flex items-center justify-center px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <FiPrinter className="mr-2" />
            Generate Report
          </button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 gap-4 mb-6 sm:grid-cols-2 lg:grid-cols-4">
          <div className="p-4 bg-white rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-3 mr-4 text-blue-600 bg-blue-100 rounded-full">
                <FiPackage size={20} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Total Variants</p>
                <p className="text-2xl font-semibold">{summaryStats.totalItems}</p>
              </div>
            </div>
          </div>
          
          <div className="p-4 bg-white rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-3 mr-4 text-green-600 bg-green-100 rounded-full">
                <FiShoppingBag size={20} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Total Stock</p>
                <p className="text-2xl font-semibold">{summaryStats.totalStock}</p>
              </div>
            </div>
          </div>
          
          <div className="p-4 bg-white rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-3 mr-4 text-purple-600 bg-purple-100 rounded-full">
                <FiTrendingUp size={20} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Total Value</p>
                <p className="text-xl font-semibold">{summaryStats.totalValue}</p>
              </div>
            </div>
          </div>
          
          <div className="p-4 bg-white rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-3 mr-4 text-yellow-600 bg-yellow-100 rounded-full">
                <FiCalendar size={20} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Avg Age</p>
                <p className="text-2xl font-semibold">{Math.round(summaryStats.avgAge)} days</p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 gap-3 mb-6 lg:grid-cols-2">
          <div className="p-6 bg-white rounded-lg shadow">
            <h3 className="mb-4 text-lg font-medium text-gray-900">Inventory Age Distribution</h3>
            <Pie
              data={chartData.agingChart}
              width={280}
              height={280}
              options={{
                responsive: false, // Set to false to use width/height
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                  tooltip: {
                    callbacks: {
                      label: (context) => {
                        const label = context.label || '';
                        const value = context.raw || 0;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = Math.round((value / total) * 100);
                        return `${label}: ${value} (${percentage}%)`;
                      }
                    }
                  }
                }
              }}
            />
          </div>
          
          <div className="p-6 bg-white rounded-lg shadow">
            <h3 className="mb-4 text-lg font-medium text-gray-900">Inventory Value by Age</h3>
            <Bar
              data={chartData.valueByAge}
              options={{
                responsive: true,
                scales: {
                  y: {
                    beginAtZero: true,
                    ticks: {
                      callback: (value) => `${value}`
                    }
                  }
                },
                plugins: {
                  tooltip: {
                    callbacks: {
                      label: (context) => {
                        return `$${context.raw}`;
                      }
                    }
                  }
                }
              }}
            />
          </div>
        </div>

        {/* Filters */}
        <div className="p-4 mb-6 bg-white rounded-lg shadow">
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
            {/* Search Bar */}
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
              <input
                type="text"
                value={search}
                onChange={e => setSearch(e.target.value)}
                placeholder="Search by product, batch, or category"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            {/* Filter Dropdown */}
            <div className="flex-1 ">
              <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Age Category</label>
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Items</option>
                <option value="0-30 days">0-30 days</option>
                <option value="31-60 days">31-60 days</option>
                <option value="61-90 days">61-90 days</option>
                <option value="90+ days">90+ days</option>
                {/* <option value="Unknown">Unknown</option> */}
              </select>
            </div>
          </div>
        </div>

        {/* Data Table */}
        <div className="p-6 bg-white rounded-lg shadow">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => requestSort("product_name")}
                  >
                    Product
                    {sortConfig.key === "product_name" && (
                      <span className="ml-1">{sortConfig.direction === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Batch/Variant
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => requestSort("created_at")}
                  >
                    Created
                    {sortConfig.key === "created_at" && (
                      <span className="ml-1">{sortConfig.direction === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => requestSort("aging")}
                  >
                    Age (days)
                    {sortConfig.key === "aging" && (
                      <span className="ml-1">{sortConfig.direction === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Purchased
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sold
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Value
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center">
                      Loading...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : sortedVariants.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No items found matching your criteria
                    </td>
                  </tr>
                ) : (
                  sortedVariants.map((variant, idx) => {
                    const agingClass = 
                      variant.agingCategory === "0-30 days" ? "bg-green-50" :
                      variant.agingCategory === "31-60 days" ? "bg-blue-50" :
                      variant.agingCategory === "61-90 days" ? "bg-yellow-50" :
                      variant.agingCategory === "90+ days" ? "bg-red-50" : "bg-gray-50";
                    
                    return (
                      <tr key={variant.product_variant_id || variant.batch_number || idx} className={agingClass}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">
                            {splitProductName(variant.product_name).map((line, i) => (
                              <span key={i} style={{ display: "block", lineHeight: "1.2" }}>{line}</span>
                            ))}
                          </div>
                          <div className="text-sm text-gray-500">{variant.product_category}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full">
                            {variant.batch_number || variant.product_variant_id || "-"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(variant.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                            variant.agingCategory === "0-30 days" ? "bg-green-100 text-green-800" :
                            variant.agingCategory === "31-60 days" ? "bg-blue-100 text-blue-800" :
                            variant.agingCategory === "61-90 days" ? "bg-yellow-100 text-yellow-800" :
                            variant.agingCategory === "90+ days" ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"
                          }`}>
                            {variant.aging ?? "N/A"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {(variant.purchased ?? 0) + (variant.opening_stock_quantity ?? 0)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {variant.sold}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {variant.closingStock}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          LKR {(variant.value || 0)}
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemAgeAnalyze;