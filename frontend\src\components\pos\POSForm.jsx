import React, { useState, useEffect, useRef, useCallback } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import debounce from "lodash/debounce";
import CloseRegisterModal from "../models/CloseRegisterModal";
import HeldSalesList from "./HeldSalesList";
import { ExclamationTriangleIcon } from "@heroicons/react/20/solid";
import CashRegister from "./CashRegistryModal.jsx";
import RegisterModal from "../models/registerModel.jsx";
import {
  ClipboardList,
  Trash2,
  LogOut,
  LockOpen,
  Maximize,
  Minimize,
  Calculator,
  LayoutDashboard,
  PauseCircle,
  RefreshCw,
  Printer,
  Plus,
} from "lucide-react";
import BillPrintModal from "../models/BillPrintModel.jsx";
import Notification from "../notification/Notification.jsx";
import {
  formatNumberWithCommas,
  parseFormattedNumber,
} from "../../utils/numberformat";
import CalculatorModal from "../models/calculator/CalculatorModal.jsx";
import { useRegister } from "../../context/RegisterContext";
import { useAuth } from "../../context/NewAuthContext";
import ProductDetailsModal from "../../pages/items/ProductDetailsModal.jsx";
import "./no-spinner.css";
import ItemForm from "../Item Form/ItemForm";

// Helper function to check if date is within discount scheme period
const isDateWithinScheme = (invoiceDate, startDate, endDate) => {
  try {
    const invDate = new Date(invoiceDate);
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;
    return (!start || invDate >= start) && (!end || invDate <= end);
  } catch (e) {
    console.error("Error checking date within scheme:", e);
    return false;
  }
};

// Function to apply discount schemes for products or categories
const applyDiscountScheme = (product, saleType, schemes) => {
  if (
    !product ||
    (!product.product_id && !product.id) ||
    !Array.isArray(schemes)
  ) {
    const fallbackPrice =
      saleType === "Wholesale"
        ? parseFloat(product?.wholesale_price || product?.sales_price || 0)
        : parseFloat(product?.sales_price || 0);
    return { price: Math.max(0, fallbackPrice), schemeName: null, free_qty: 0 };
  }

  const basePrice =
    saleType === "Wholesale"
      ? parseFloat(product.wholesale_price || product.sales_price || 0)
      : parseFloat(product.sales_price || 0);

  if (isNaN(basePrice) || basePrice <= 0) {
    return { price: 0, schemeName: null, free_qty: 0 };
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let bestScheme = null;
  let maxDiscountValue = -1;
  let bestFreeScheme = null;
  let maxFreeQty = 0;

  const findBestScheme = (schemeList) => {
    schemeList.forEach((scheme) => {
      if (
        !scheme.active ||
        !scheme.type
      ) {
        return;
      }

      // For non-free schemes, check if value is provided
      if (scheme.type !== "free" && (scheme.value === null || scheme.value === undefined)) {
        return;
      }

      const startDate = scheme.start_date ? new Date(scheme.start_date) : null;
      const endDate = scheme.end_date ? new Date(scheme.end_date) : null;
      if (startDate && startDate > today) return;
      if (endDate) {
        endDate.setHours(23, 59, 59, 999);
        if (endDate < today) return;
      }

      let currentDiscountValue = 0;
      const schemeValue = parseFloat(scheme.value || 0);

      if (scheme.type === "percentage" && schemeValue > 0) {
        currentDiscountValue = (basePrice * schemeValue) / 100;
      } else if (scheme.type === "amount" && schemeValue > 0) {
        currentDiscountValue = schemeValue;
      } else if (scheme.type === "free") {
        const qtyNum = Number(product.qty) || 1;
        const buyQtyNum = Number(scheme.buy_quantity);
        const freeQtyNum = Number(scheme.free_quantity);
        const eligibleFree = Math.floor(qtyNum / buyQtyNum) * freeQtyNum;
        if (eligibleFree > maxFreeQty) {
          maxFreeQty = eligibleFree;
          bestFreeScheme = scheme;
        }
        return;
      } else {
        return;
      }

      currentDiscountValue = Math.min(currentDiscountValue, basePrice);

      if (currentDiscountValue > maxDiscountValue) {
        maxDiscountValue = currentDiscountValue;
        bestScheme = scheme;
      }
    });
  };

  // Create display name for batch-wise matching
  const batchInfo = product.batch_number
    ? ` (Batch: ${product.batch_number})`
    : "";
  const expiryInfo = product.expiry_date
    ? ` (Exp: ${product.expiry_date.split("T")[0]})`
    : "";
  const displayName = `${product.product_name}${batchInfo}${expiryInfo}`;

  const productSchemes = schemes.filter((s) => {
    if (s.applies_to !== "product") return false;
    const target = s.target?.trim();
    // Match both old format (just product name) and new format (with batch info)
    const matches = target === product.product_name || target === displayName;
    
    // Debug: Log scheme matching
    if (s.type === 'free') {
      console.log('Checking free scheme:', {
        scheme_name: s.name,
        target: target,
        product_name: product.product_name,
        displayName: displayName,
        matches: matches
      });
    }
    
    return matches;
  });
  
  findBestScheme(productSchemes);

  if ((!bestScheme || maxDiscountValue <= 0) && product.category_name) {
    const categorySchemes = schemes.filter(
      (s) => s.applies_to === "category" && s.target === product.category_name
    );
    findBestScheme(categorySchemes);
  }

  if (bestFreeScheme && maxFreeQty > 0) {
    return {
      price: basePrice,
      schemeName: bestFreeScheme.name || "Unnamed Free Scheme",
      free_qty: maxFreeQty,
    };
  }

  if (bestScheme && maxDiscountValue > 0) {
    let discountedPrice = basePrice;
    const schemeValue = parseFloat(bestScheme.value || 0);

    if (bestScheme.type === "percentage") {
      discountedPrice = basePrice * (1 - schemeValue / 100);
    } else if (bestScheme.type === "amount") {
      discountedPrice = basePrice - schemeValue;
    }
    return {
      price: Math.max(0, discountedPrice),
      schemeName: bestScheme.name || "Unnamed Scheme",
      free_qty: 0,
    };
  }

  return { price: basePrice, schemeName: null, free_qty: 0 };
};

const POSForm = ({
  initialProducts = [],
  initialBillDiscount = 0,
  initialTax = 0,
  initialShipping = 0,
  initialCustomerInfo = {
    name: "",
    mobile: "",
    bill_number: "",
    userId: "U-1",
  },
  isEditMode = false,
  onSubmit,
  onCancel,
}) => {
  // Add closeBillModal function to close the BillPrintModal
  const closeBillModal = () => {
    setShowBillModal(false);
  };
  const { user } = useAuth();
  const { registerStatus, openRegister, closeRegister, refreshRegisterStatus, initializeDailyRegister } =
    useRegister();
  const terminalId = registerStatus.terminalId || "T-1";
  const userId = user?.id || 1;
  const navigate = useNavigate();

  const [isFullScreen, setIsFullScreen] = useState(false);
  const [saleType, setSaleType] = useState("Retail");
  const [products, setProducts] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [selectedSearchIndex, setSelectedSearchIndex] = useState(-1);
  const [items, setItems] = useState([]);
  const [activeSchemes, setActiveSchemes] = useState([]);
  const [tax, setTax] = useState(initialTax);
  const [billDiscount, setBillDiscount] = useState(initialBillDiscount);
  const [billDiscountPercentage, setBillDiscountPercentage] = useState(0);
  const [shipping, setShipping] = useState(initialShipping);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [customerInfo, setCustomerInfo] = useState(initialCustomerInfo);
  const [billNumber, setBillNumber] = useState(
    initialCustomerInfo.bill_number || ""
  );
  const [showNotification, setShowNotification] = useState(false);
  const [pendingDeleteIndex, setPendingDeleteIndex] = useState(null);
  const [showBillModal, setShowBillModal] = useState(false);
  const [showHeldSalesList, setShowHeldSalesList] = useState(false);
  const [heldSales, setHeldSales] = useState([]);
  const [loadingHeldSales, setLoadingHeldSales] = useState(false);
  const [holdingSale, setHoldingSale] = useState(false);
  const [showCalculatorModal, setShowCalculatorModal] = useState(false);
  const [isCloseRegisterOpen, setIsCloseRegisterOpen] = useState(false);
  const [loadingItems, setLoadingItems] = useState(false);
  const [loadingSchemes, setLoadingSchemes] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [isClosingRegister, setIsClosingRegister] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [stockWarning, setStockWarning] = useState("");
  const searchInputRef = useRef(null);
  const quantityInputRef = useRef(null);
  const payButtonRef = useRef(null);
  const [showAddItemForm, setShowAddItemForm] = useState(false);

  // Helper functions for discount calculations
  const calculateDiscountFromPercentage = (percentage, subtotal) => {
    return (subtotal * percentage) / 100;
  };

  const calculatePercentageFromDiscount = (discountAmount, subtotal) => {
    if (subtotal === 0) return 0;
    return (discountAmount / subtotal) * 100;
  };

  // Initialize values when in edit mode
  useEffect(() => {
    if (isEditMode) {
      setProducts(initialProducts);
      setBillDiscount(initialBillDiscount);
      setBillDiscountPercentage(0); // Initialize percentage as 0 in edit mode
      setTax(initialTax);
      setShipping(initialShipping);
      setCustomerInfo(initialCustomerInfo);
      setBillNumber(initialCustomerInfo.bill_number || "");
    }
  }, [
    isEditMode,
    initialProducts,
    initialBillDiscount,
    initialTax,
    initialShipping,
    initialCustomerInfo,
  ]);

  // Fetch next bill number on component mount if not in edit mode
  useEffect(() => {
    if (!isEditMode) {
      const fetchNextBillNumber = async () => {
        try {
          const token = user?.token;
          const headers = token ? { Authorization: `Bearer ${token}` } : {};
          const response = await axios.get(
            "http://127.0.0.1:8000/api/next-bill-number",
            { headers }
          );
          setBillNumber(response.data.next_bill_number);
          setCustomerInfo((prev) => ({
            ...prev,
            bill_number: response.data.next_bill_number,
          }));
        } catch (error) {
          console.error("Error fetching next bill number:", error);
          setBillNumber("ERR-001");
        }
      };
      fetchNextBillNumber();
    }
  }, [isEditMode, user]);

  // Function to refresh a specific product
  const refreshSpecificProduct = useCallback(async (productId, variantId = null) => {
    try {
      const response = await axios.get(`http://127.0.0.1:8000/api/products/${productId}`);
      if (response.data && response.data.data) {
        const updatedProduct = response.data.data;
        const categoryName = updatedProduct.category || updatedProduct.category_name || "Unknown";

        const processedProduct = {
          ...updatedProduct,
          stock: parseFloat(updatedProduct.closing_stock_quantity || 0),
          category_name: categoryName,
          sales_price: parseFloat(updatedProduct.sales_price || 0),
          mrp: parseFloat(updatedProduct.mrp || 0),
          supplier: updatedProduct.supplier || "N/A",
          store_location: updatedProduct.store_location || "N/A",
          variants: updatedProduct.variants || [],
        };

        // Update the items array with the refreshed product
        setItems(prevItems => {
          return prevItems.map(item => {
            if (item.product_id === productId) {
              return processedProduct;
            }
            return item;
          });
        });

        console.log(`Refreshed product: ${updatedProduct.product_name}`);
        return processedProduct;
      }
    } catch (error) {
      console.error(`Error refreshing product ${productId}:`, error);
      return null;
    }
  }, []);

  // Fetch products
  useEffect(() => {
    setLoadingItems(true);
    axios
      .get("http://127.0.0.1:8000/api/products")
      .then((response) => {
        if (response.data && Array.isArray(response.data.data)) {
          const productsWithOpeningStock = response.data.data.map((p) => {
            const categoryName = p.category || p.category_name || "Unknown";
            if (categoryName === "Unknown") {
              console.warn(
                `Missing category_name for product: ${p.product_name} (category_id: ${p.category_id})`
              );
            }

            // Debug logging for specific products
            if (p.product_name?.toLowerCase().includes("anchor")) {
              console.log("ANCHOR PRODUCT DEBUG - Raw API data:", p);
              console.log("ANCHOR PRODUCT DEBUG - Raw MRP:", p.mrp);
              console.log(
                "ANCHOR PRODUCT DEBUG - Raw sales_price:",
                p.sales_price
              );
              console.log("ANCHOR PRODUCT DEBUG - Raw variants:", p.variants);
            }

            return {
              ...p,
              stock: parseFloat(p.closing_stock_quantity || 0),
              category_name: categoryName,
              sales_price: parseFloat(p.sales_price || 0),
              mrp: parseFloat(p.mrp || 0),
              supplier: p.supplier || "N/A",
              store_location: p.store_location || "N/A",
              // Include variants for batch selection
              variants: p.variants || [],
            };
          });
          setItems(productsWithOpeningStock);
          console.log("Fetched Products:", productsWithOpeningStock);
        } else {
          console.error("Unexpected product data format:", response.data);
          setItems([]);
        }
      })
      .catch((error) => {
        console.error("Error fetching items:", error);
        setItems([]);
      })
      .finally(() => {
        setLoadingItems(false);
      });
  }, []);

  // Fetch discount schemes
  useEffect(() => {
    setLoadingSchemes(true);
    axios
      .get("http://127.0.0.1:8000/api/discount-schemes")
      .then((response) => {
        if (response.data && Array.isArray(response.data.data)) {
          const formattedSchemes = response.data.data.map((s) => ({
            ...s,
            applies_to: s.applies_to || s.appliesTo,
            start_date: s.start_date || s.startDate,
            end_date: s.end_date || s.endDate,
          }));
          const active = formattedSchemes.filter((scheme) => scheme.active);
          setActiveSchemes(active);
          console.log("Fetched Active Schemes:", active);
        } else {
          console.error("Unexpected scheme data format:", response.data);
          setActiveSchemes([]);
        }
      })
      .catch((error) => {
        console.error("Error fetching discount schemes:", error);
        setActiveSchemes([]);
      })
      .finally(() => {
        setLoadingSchemes(false);
      });
  }, []);

  // Apply discounts to products (only for special discounts)
  useEffect(() => {
    if (activeSchemes.length > 0 || !loadingSchemes) {
      setProducts((prevProducts) =>
        prevProducts.map((product) => {
          const { schemeName } = applyDiscountScheme(
            product,
            saleType,
            activeSchemes
          );
          const productWithQty = { ...product, qty: product.qty || 1 };
          const specialDiscount = calculateSpecialDiscount(
            productWithQty,
            saleType,
            new Date().toISOString().split("T")[0]
          );
          const unitPrice = parseFloat(product.price || 0);
          const unitDiscount = Math.max(0, (product.mrp || 0) - unitPrice);
          const qty = product.qty || 1;
          const combinedDiscount = unitDiscount + specialDiscount;
          const total = qty * ((product.mrp || 0) - combinedDiscount);

          return {
            ...product,
            schemeName: schemeName,
            discount: combinedDiscount,
            total: total >= 0 ? total : 0,
          };
        })
      );
    }
  }, [saleType, activeSchemes, loadingSchemes]);

  // Initialize products when in edit mode
  useEffect(() => {
    if (isEditMode && initialProducts.length > 0) {
      // Process initial products to ensure proper discount calculations
      const processedProducts = initialProducts.map((product, index) => {
        const qty = product.qty || 1;
        const totalDiscount = product.discount || 0;
        const mrp = parseFloat(product.mrp || 0);
        const price = parseFloat(product.price || product.sales_price || 0);

        // Calculate per-unit discount from total discount
        // If total discount is 0, calculate from MRP - price difference
        let discountPerUnit = 0;
        if (totalDiscount > 0) {
          discountPerUnit = totalDiscount / qty;
        } else if (mrp > price) {
          discountPerUnit = mrp - price;
        }

        return {
          ...product,
          serialNumber: index + 1,
          discountPerUnit: discountPerUnit,
          specialDiscountPerUnit: 0, // Reset special discount for edit mode
          discount: discountPerUnit * qty, // Recalculate total discount
          // Ensure all required fields are present
          mrp: mrp,
          price: price,
          total: product.total || price * qty,
        };
      });

      setProducts(processedProducts);
    }
  }, [isEditMode, initialProducts]);

  // Calculate special discounts
  const calculateSpecialDiscount = useCallback(
    (item, saleType, billDate) => {
      if (!item || !item.product_id) {
        console.warn("No product_id provided for discount calculation");
        return 0;
      }

      // Find the product in the items array
      const baseProduct = items.find((p) => p.product_id === item.product_id);
      if (!baseProduct) {
        console.warn(
          `Base product not found for product_id: ${item.product_id}`
        );
        return 0;
      }

      // If item has variant_id, find the specific variant, otherwise use the base product
      let product;
      if (
        item.variant_id &&
        baseProduct.variants &&
        baseProduct.variants.length > 0
      ) {
        const variant = baseProduct.variants.find(
          (v) => v.product_variant_id === item.variant_id
        );
        if (variant) {
          // Merge base product with variant data
          product = {
            ...baseProduct,
            ...variant,
            variant_id: variant.product_variant_id,
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            sales_price: variant.sales_price,
            mrp: variant.mrp,
          };
        } else {
          console.warn(`Variant not found for variant_id: ${item.variant_id}`);
          product = baseProduct;
        }
      } else {
        product = baseProduct;
      }

      const categoryName = product.category_name || "Unknown";
      if (categoryName === "Unknown") {
        console.warn(
          `No valid category_name for product: ${product.product_name} (category_id: ${product.category_id})`
        );
      }

      const basePrice =
        saleType === "Wholesale"
          ? parseFloat(product.wholesale_price || product.sales_price || 0)
          : parseFloat(product.sales_price || 0);
      const qty = parseFloat(item.qty) || 1;
      // Use MRP for special discount calculation, not sales price
      const totalAmount = qty * (product.mrp || 0);

      const applicableScheme = activeSchemes.find((scheme) => {
        if (
          !scheme.active ||
          !isDateWithinScheme(billDate, scheme.start_date, scheme.end_date)
        ) {
          return false;
        }
        const target = scheme.target?.trim().toLowerCase();

        // Create display name for batch-wise matching
        // Use item data first (from cart), then fall back to product data (from items array)
        const batchNumber = item.batch_number || product.batch_number;
        const expiryDate = item.expiry_date || product.expiry_date;

        const batchInfo = batchNumber ? ` (Batch: ${batchNumber})` : "";
        const expiryInfo = expiryDate
          ? ` (Exp: ${expiryDate.split("T")[0]})`
          : "";
        const displayName = `${product.product_name}${batchInfo}${expiryInfo}`;

        const productMatch =
          scheme.applies_to === "product" &&
          (target === (product.product_name?.trim().toLowerCase() || "") ||
            target === (product.description?.trim().toLowerCase() || "") ||
            target === displayName.trim().toLowerCase());
        const categoryMatch =
          scheme.applies_to === "category" &&
          categoryName &&
          target === categoryName?.trim().toLowerCase();

        if (productMatch) {
          console.log(
            `✅ Product discount match for ${product.product_name}: Target=${target}, Value=${scheme.value} ${scheme.type}`
          );
        }
        if (categoryMatch) {
          console.log(
            `✅ Category discount match for ${product.product_name} (Category: ${categoryName}): Target=${target}, Value=${scheme.value} ${scheme.type}`
          );
        }
        return productMatch || categoryMatch;
      });

      if (!applicableScheme) {
        console.log(
          `No discount scheme found for ${product.product_name} (Category: ${categoryName})`
        );
        return 0;
      }

      let discount = 0;
      if (applicableScheme.type === "percentage") {
        discount = (totalAmount * parseFloat(applicableScheme.value)) / 100;
      } else if (applicableScheme.type === "amount") {
        discount = parseFloat(applicableScheme.value) * qty;
      }

      console.log(
        `Applied ${applicableScheme.applies_to} discount for ${product.product_name}: Target=${applicableScheme.target}, Discount=${discount.toFixed(2)}`
      );
      return discount >= 0 ? discount : 0;
    },
    [items, activeSchemes]
  );

  // Generate filtered product options with variants (similar to SalesInvoice)
  const getFilteredProductOptions = useCallback(() => {
    const options = [];
    items.forEach((product) => {
      if (product.variants && product.variants.length > 0) {
        // Product has variants - create option for each variant
        product.variants.forEach((variant) => {
          const categoryName = product.category_name || "Unknown";
          const stockInfo = variant.closing_stock_quantity || 0;
          const batchInfo = variant.batch_number
            ? ` (Batch: ${variant.batch_number})`
            : "";
          const expiryInfo = variant.expiry_date
            ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
            : "";

          const parsedMRP = parseFloat(variant.mrp || 0);
          const option = {
            ...product,
            ...variant,
            // Use variant-specific values
            product_id: product.product_id,
            variant_id: variant.product_variant_id,
            display_name: `${product.product_name}${batchInfo}${expiryInfo}`,
            stock: parseFloat(stockInfo),
            sales_price: parseFloat(variant.sales_price || 0),
            mrp: parsedMRP,
            buying_cost: parseFloat(variant.buying_cost || 0),
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            store_location:
              variant.store_location || product.store_location || "N/A",
            category_name: categoryName,
          };

          // Debug logging for high MRP variants
          if (parsedMRP > 1000) {
            console.log("HIGH MRP VARIANT ADDED TO OPTIONS:", {
              name: option.display_name,
              mrp: parsedMRP,
              originalMRP: variant.mrp,
              productName: product.product_name,
              variantName: variant.variant_name,
            });
          }

          options.push(option);
        });
      } else {
        // No variants - add product as is with explicit MRP parsing
        const categoryName = product.category_name || "Unknown";
        const parsedMRP = parseFloat(product.mrp || 0);
        const option = {
          ...product,
          display_name: product.product_name,
          stock: parseFloat(product.closing_stock_quantity || 0),
          category_name: categoryName,
          // Explicitly parse MRP and sales_price for non-variant products
          mrp: parsedMRP,
          sales_price: parseFloat(product.sales_price || 0),
          buying_cost: parseFloat(product.buying_cost || 0),
        };

        // Debug logging for high MRP non-variant products
        if (parsedMRP > 1000) {
          console.log("HIGH MRP NON-VARIANT ADDED TO OPTIONS:", {
            name: option.display_name,
            mrp: parsedMRP,
            originalMRP: product.mrp,
            productName: product.product_name,
          });
        }

        options.push(option);
      }
    });

    // Debug logging for total options
    const highMRPOptions = options.filter(
      (item) => parseFloat(item.mrp || 0) > 1000
    );
    console.log(
      `Total product options: ${options.length}, High MRP options (>1000): ${highMRPOptions.length}`
    );
    if (highMRPOptions.length > 0) {
      console.log(
        "High MRP products in options:",
        highMRPOptions.map((item) => ({
          name: item.display_name || item.product_name,
          mrp: item.mrp,
        }))
      );
    }

    return options;
  }, [items]);

  // Debounced search for products (updated to use variant-aware options)
  const debouncedSearch = useCallback(
    debounce((query) => {
      const productOptions = getFilteredProductOptions();
      if (query.trim() === "") {
        setSearchResults([]);
        setSelectedProduct(null);
        return;
      }
      const lowerCaseQuery = query.toLowerCase().trim();
      // Read the setting from localStorage
      const startsWithOnly = localStorage.getItem("productSearchStartsWithOnly") === "true";
      const results = productOptions.filter((item) => {
        let matches = false;
        if (startsWithOnly) {
          matches =
            item &&
            ((item.product_name &&
              item.product_name.toLowerCase().trim().startsWith(lowerCaseQuery)) ||
              (item.display_name &&
                item.display_name
                  .toLowerCase()
                  .trim()
                  .startsWith(lowerCaseQuery)) ||
              (item.item_code && String(item.item_code).startsWith(query.trim())) ||
              (item.barcode && String(item.barcode).startsWith(query.trim())) ||
              (item.batch_number &&
                String(item.batch_number).startsWith(query.trim())));
        } else {
          matches =
            item &&
            ((item.product_name &&
              item.product_name.toLowerCase().trim().includes(lowerCaseQuery)) ||
              (item.display_name &&
                item.display_name
                  .toLowerCase()
                  .trim()
                  .includes(lowerCaseQuery)) ||
              (item.item_code && String(item.item_code).includes(query.trim())) ||
              (item.barcode && String(item.barcode).includes(query.trim())) ||
              (item.batch_number &&
                String(item.batch_number).includes(query.trim())));
        }
        // Debug logging for high MRP products
        if (matches && parseFloat(item.mrp || 0) > 1000) {
          console.log("HIGH MRP PRODUCT FOUND IN SEARCH:", {
            name: item.product_name,
            mrp: item.mrp,
            parsedMRP: parseFloat(item.mrp || 0),
            query: query,
            matches: matches,
          });
        }
        return matches;
      });
      // Debug logging for search results
      console.log(`Search for "${query}" found ${results.length} results`);
      const highMRPResults = results.filter(
        (item) => parseFloat(item.mrp || 0) > 1000
      );
      if (highMRPResults.length > 0) {
        console.log(
          `Found ${highMRPResults.length} products with MRP > 1000:`,
          highMRPResults.map((item) => ({
            name: item.product_name,
            mrp: item.mrp,
          }))
        );
      }
      setSearchResults(results);
      setSelectedSearchIndex(results.length > 0 ? 0 : -1);
    }, 300),
    [items, getFilteredProductOptions]
  );

  const handleSearch = (query) => {
    setSearchQuery(query);
    setSelectedProduct(null);
    debouncedSearch(query);
  };

  const handleQuantityChange = (e) => {
    const value = e.target.value;
    if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
      setQuantity(value);
    }
  };

  const handleKeyDown = (e) => {
    const numSearchResults = searchResults.length;

    if (e.key === "ArrowDown") {
      if (numSearchResults > 0) {
        e.preventDefault();
        setSelectedSearchIndex((prev) => (prev + 1) % numSearchResults);
      }
    } else if (e.key === "ArrowUp") {
      if (numSearchResults > 0) {
        e.preventDefault();
        setSelectedSearchIndex(
          (prev) => (prev - 1 + numSearchResults) % numSearchResults
        );
      }
    } else if (e.key === "Enter") {
      e.preventDefault();
      if (document.activeElement === searchInputRef.current) {
        if (numSearchResults > 0 && selectedSearchIndex >= 0) {
          handleItemSelection(searchResults[selectedSearchIndex]);
        } else if (selectedProduct) {
          quantityInputRef.current?.focus();
          quantityInputRef.current?.select();
        } else {
          console.log("Product not found or not selected.");
        }
      } else if (document.activeElement === quantityInputRef.current) {
        const currentQuantity = parseFloat(quantity || 0);
        if (currentQuantity > 0) {
          addProductToTable();
        } else {
          alert("Please enter a valid quantity.");
        }
      }
    }
  };

  // Read auto-add scanned product setting from localStorage
  const autoAddScannedProductToCart = localStorage.getItem("autoAddScannedProductToCart") === "true";

  const handleItemSelection = (item, { fromBarcodeScan = false } = {}) => {
    if (!item || !item.product_id) {
      console.error("Invalid item selected:", item);
      return;
    }
    setSelectedProduct({
      ...item,
      supplier: item.supplier || "N/A",
      category: item.category_name || "N/A",
      store_location: item.store_location || "N/A",
    });
    setSearchQuery(item.product_name?.trim() || "");
    setSearchResults([]);
    setQuantity(1);
    setSelectedSearchIndex(-1);
    if (fromBarcodeScan && autoAddScannedProductToCart) {
      // Immediately add to cart if setting is ON
      setTimeout(() => addProductToTable(), 0);
    } else if (quantityInputRef.current) {
      quantityInputRef.current.focus();
      quantityInputRef.current.select();
    }
  };

  const addProductToTable = () => {
    const currentQuantity = parseFloat(quantity || 0);

    if (!selectedProduct || !selectedProduct.product_id) {
      alert("Please select a valid product first.");
      searchInputRef.current?.focus();
      return;
    }
    if (isNaN(currentQuantity) || currentQuantity <= 0) {
      alert("Please enter a valid quantity greater than 0.");
      quantityInputRef.current?.focus();
      quantityInputRef.current?.select();
      return;
    }

    const availableStock = parseFloat(selectedProduct.stock || 0);
    if (isNaN(availableStock)) {
      alert(
        `Stock information missing or invalid for ${selectedProduct.product_name}. Cannot add.`
      );
      return;
    }

    // Calculate discounts and free_qty
    const { price, schemeName, free_qty } = applyDiscountScheme(
      { ...selectedProduct, qty: currentQuantity },
      saleType,
      activeSchemes
    );

    // Debug: Log the applyDiscountScheme result
    console.log('applyDiscountScheme result:', {
      product_name: selectedProduct.product_name,
      qty: currentQuantity,
      price,
      schemeName,
      free_qty,
      activeSchemes: activeSchemes.filter(s => s.type === 'free')
    });


    const discountAmountPerUnit = Math.max(
      0,
      (selectedProduct.mrp || 0) - (selectedProduct.sales_price || 0)
    );

    const totalSpecialDiscount = calculateSpecialDiscount(
      { ...selectedProduct, qty: currentQuantity },
      saleType,
      new Date().toISOString().split("T")[0]
    );

    // Calculate special discount per unit (since calculateSpecialDiscount returns total for all quantity)
    const specialDiscountPerUnit = currentQuantity > 0 ? totalSpecialDiscount / currentQuantity : 0;

    // Debug: Log the discount calculations
    console.log('Discount calculation debug:', {
      product_name: selectedProduct.product_name,
      mrp: selectedProduct.mrp,
      sales_price: selectedProduct.sales_price,
      qty: currentQuantity,
      discountAmountPerUnit,
      totalSpecialDiscount,
      specialDiscountPerUnit,
      totalDiscount: (discountAmountPerUnit + specialDiscountPerUnit) * currentQuantity,
      free_qty
    });

    const mrp = parseFloat(selectedProduct.mrp || 0);

    const newProduct = {
      ...selectedProduct,
      qty: currentQuantity,
      price: mrp, // Use MRP like TouchPOS does
      discountPerUnit: discountAmountPerUnit,
      specialDiscountPerUnit: specialDiscountPerUnit,
      discount:
        (discountAmountPerUnit + specialDiscountPerUnit) * currentQuantity,
      discount_percentage:
        selectedProduct.mrp && selectedProduct.sales_price
          ? ((discountAmountPerUnit + specialDiscountPerUnit) /
              selectedProduct.mrp) *
            100
          : 0,
      schemeName: schemeName,
      total:
        currentQuantity *
        ((selectedProduct.mrp || 0) -
          (discountAmountPerUnit + specialDiscountPerUnit)),
      serialNumber: products.length + 1,
      supplier: selectedProduct.supplier,
      category: selectedProduct.category,
      store_location: selectedProduct.store_location,
      // Include variant information if present
      variant_id: selectedProduct.variant_id || null,
      batch_number: selectedProduct.batch_number || null,
      expiry_date: selectedProduct.expiry_date || null,
      display_name: selectedProduct.product_name?.trim(), // Use only product name for item list display
      free_qty: free_qty || 0, // <-- Ensure free_qty is set
    };
    setProducts([...products, newProduct]);
    // Clear search bar, selected product, and quantity after adding
    setSearchQuery("");
    setSelectedProduct(null);
    setQuantity(1);
    setSearchResults([]);
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const updateProductQuantity = (index, newQtyStr) => {
    // Use helper function to parse comma-formatted numbers
    const newQty = parseFormattedNumber(newQtyStr);

    if (isNaN(newQty) || newQty < 0) {
      console.warn("Invalid quantity input:", newQtyStr);
      return;
    }

    setProducts((prevProducts) => {
      const productToUpdate = prevProducts[index];
      if (!productToUpdate) return prevProducts;

      const availableStock = parseFloat(productToUpdate.stock || 0);

      if (!isNaN(availableStock) && newQty > availableStock) {
        setStockWarning(
          `Warning: Insufficient stock for ${productToUpdate.product_name}! Only ${availableStock} available. You're setting quantity to ${newQty}.`
        );
      } else {
        setStockWarning("");
      }

      // Recalculate free_qty based on new quantity
      const { free_qty } = applyDiscountScheme(
        { ...productToUpdate, qty: newQty },
        saleType,
        activeSchemes
      );

      const discountPerUnit = productToUpdate.discountPerUnit || 0;
      const specialDiscountPerUnit =
        productToUpdate.specialDiscountPerUnit || 0;
      const combinedDiscountPerUnit = discountPerUnit + specialDiscountPerUnit;

      return prevProducts.map((product, i) => {
        if (i === index) {
          const total = newQty * ((product.mrp || 0) - combinedDiscountPerUnit);
          return {
            ...product,
            qty: newQty,
            discount: combinedDiscountPerUnit * newQty, // Fix: multiply by quantity
            total: total >= 0 ? total : 0,
            free_qty: free_qty || 0, // Update free_qty based on new quantity
          };
        }
        return product;
      });
    });
  };

  const updateProductPrice = (index, newPriceStr) => {
    // Use helper function to parse comma-formatted numbers like "1,500"
    const newPrice = parseFormattedNumber(newPriceStr);
    if (isNaN(newPrice) || newPrice < 0) {
      console.warn("Invalid price input:", newPriceStr);
      return;
    }
    setProducts((prevProducts) =>
      prevProducts.map((product, i) => {
        if (i === index) {
          const qty = product.qty || 1;
          
          // Keep the existing special discount (don't recalculate it)
          const existingSpecialDiscountPerUnit = product.specialDiscountPerUnit || 0;
          const existingSpecialDiscountTotal = existingSpecialDiscountPerUnit * qty;
          
          // Calculate new normal discount based on new price: MRP - New Price
          const newNormalDiscountPerUnit = Math.max(0, (product.mrp || 0) - newPrice);
          const newNormalDiscountTotal = newNormalDiscountPerUnit * qty;
          
          // Total discount = Normal discount + Existing Special discount
          const totalDiscount = newNormalDiscountTotal + existingSpecialDiscountTotal;
          
          const newDiscountPercentage =
            newPrice > 0 ? (newNormalDiscountPerUnit / newPrice) * 100 : 0;
          
          // Recalculate free_qty based on current product state
          const { free_qty } = applyDiscountScheme(
            { ...product, qty: qty },
            saleType,
            activeSchemes
          );
          
          // Total calculation: (MRP × Qty) - Total Discount
          const total = (qty * (product.mrp || 0)) - totalDiscount;
          
          // Debug logging
          console.log('Price Update Debug:', {
            mrp: product.mrp,
            newPrice: newPrice,
            qty: qty,
            newNormalDiscountPerUnit: newNormalDiscountPerUnit,
            newNormalDiscountTotal: newNormalDiscountTotal,
            existingSpecialDiscountPerUnit: existingSpecialDiscountPerUnit,
            existingSpecialDiscountTotal: existingSpecialDiscountTotal,
            totalDiscount: totalDiscount,
            total: total
          });
          
          return {
            ...product,
            price: newPrice,
            discountPerUnit: newNormalDiscountPerUnit,
            specialDiscountPerUnit: existingSpecialDiscountPerUnit, // Keep existing special discount
            discount: totalDiscount, // Total discount (normal + special)
            discount_percentage: newDiscountPercentage,
            schemeName: null,
            total: total >= 0 ? total : 0,
            free_qty: free_qty || 0, // Update free_qty
          };
        }
        return product;
      })
    );
  };

  const updateProductDiscount = (index, newDiscountStr) => {
    // Use helper function to parse comma-formatted numbers like "1,500"
    const newTotalDiscount = parseFormattedNumber(newDiscountStr);
    if (isNaN(newTotalDiscount) || newTotalDiscount < 0) {
      console.warn("Invalid discount input:", newDiscountStr);
      return;
    }
    setProducts((prevProducts) =>
      prevProducts.map((product, i) => {
        if (i === index) {
          const qty = product.qty || 1;
          
          // Keep the existing special discount (don't recalculate it)
          const existingSpecialDiscountPerUnit = product.specialDiscountPerUnit || 0;
          const existingSpecialDiscountTotal = existingSpecialDiscountPerUnit * qty;
          
          // Calculate normal discount from the remaining amount
          const normalDiscountTotal = Math.max(0, newTotalDiscount - existingSpecialDiscountTotal);
          const normalDiscountPerUnit = qty > 0 ? normalDiscountTotal / qty : 0;
          
          // Recalculate free_qty based on current product state
          const { free_qty } = applyDiscountScheme(
            { ...product, qty: qty },
            saleType,
            activeSchemes
          );
          
          const newDiscountPercentage =
            product.price > 0 ? (normalDiscountPerUnit / product.price) * 100 : 0;
          
          // Total calculation: (MRP × Qty) - Total Discount
          const total = (qty * (product.mrp || 0)) - newTotalDiscount;

          return {
            ...product,
            discountPerUnit: normalDiscountPerUnit,
            specialDiscountPerUnit: existingSpecialDiscountPerUnit, // Keep existing special discount
            discount: newTotalDiscount, // Use the total discount entered by user
            discount_percentage: newDiscountPercentage,
            schemeName: null,
            total: total >= 0 ? total : 0,
            free_qty: free_qty || 0, // Update free_qty
          };
        }
        return product;
      })
    );
  };

  const updateProductDiscountPercentage = (index, newPercentageStr) => {
    const newPercentage = parseFloat(newPercentageStr);
    if (isNaN(newPercentage) || newPercentage < 0) {
      console.warn("Invalid discount percentage input:", newPercentageStr);
      return;
    }
    setProducts((prevProducts) =>
      prevProducts.map((product, i) => {
        if (i === index) {
          const updatedProductWithQty = { ...product, qty: product.qty || 1 };
          const totalSpecialDiscount = calculateSpecialDiscount(
            updatedProductWithQty,
            saleType,
            new Date().toISOString().split("T")[0]
          );
          const specialDiscountPerUnit =
            (product.qty || 1) > 0 ? totalSpecialDiscount / (product.qty || 1) : 0;
          const newDiscount = (product.price * newPercentage) / 100;
          const combinedDiscountPerUnit = newDiscount + specialDiscountPerUnit;
          
          // Recalculate free_qty based on current product state
          const { free_qty } = applyDiscountScheme(
            { ...product, qty: product.qty || 1 },
            saleType,
            activeSchemes
          );
          
          const newTotal =
            ((product.mrp || 0) - combinedDiscountPerUnit) * product.qty;
          return {
            ...product,
            discountPerUnit: newDiscount,
            specialDiscountPerUnit: specialDiscountPerUnit,
            discount: combinedDiscountPerUnit * product.qty,
            discount_percentage: newPercentage,
            schemeName: null,
            total: newTotal >= 0 ? newTotal : 0,
            free_qty: free_qty || 0, // Update free_qty
          };
        }
        return product;
      })
    );
  };

  const handleDeleteClick = (index) => {
    setPendingDeleteIndex(index);
    setShowNotification(true);
  };

  const confirmDelete = () => {
    if (
      pendingDeleteIndex !== null &&
      pendingDeleteIndex >= 0 &&
      pendingDeleteIndex < products.length
    ) {
      setProducts((prevProducts) => {
        const updated = prevProducts.filter((_, i) => i !== pendingDeleteIndex);
        return updated.map((p, idx) => ({ ...p, serialNumber: idx + 1 }));
      });
    }
    setShowNotification(false);
    setPendingDeleteIndex(null);
    searchInputRef.current?.focus();
  };

  const cancelDelete = () => {
    setShowNotification(false);
    setPendingDeleteIndex(null);
    searchInputRef.current?.focus();
  };

  const calculateTotals = useCallback(() => {
    let totalQty = 0;
    let subTotalMRP = 0;
    let totalItemDiscounts = 0;
    let totalNormalDiscounts = 0;
    let totalSpecialDiscounts = 0;
    let grandTotalBeforeAdjustments = 0;

    products.forEach((p) => {
      const qty = p.qty || 0;
      const mrp = parseFloat(p.mrp || 0);
      const unitDiscount = p.discount || 0;
      const normalDiscountTotal = (p.discountPerUnit || 0) * qty;
      const specialDiscountTotal = (p.specialDiscountPerUnit || 0) * qty;

      totalQty += qty;
      subTotalMRP += mrp * qty;
      totalItemDiscounts += unitDiscount; // unitDiscount is already total discount (qty * per-unit discount)
      totalNormalDiscounts += normalDiscountTotal;
      totalSpecialDiscounts += specialDiscountTotal;
      grandTotalBeforeAdjustments += p.total;
    });

    const currentTaxRate = parseFloat(tax || 0);
    const currentBillDiscount = parseFloat(billDiscount || 0);
    const currentShipping = parseFloat(shipping || 0);
    const taxAmount = grandTotalBeforeAdjustments * (currentTaxRate / 100);
    const finalTotalDiscount = totalItemDiscounts + currentBillDiscount;
    const finalTotal =
      grandTotalBeforeAdjustments +
      taxAmount -
      currentBillDiscount +
      currentShipping;

    return {
      totalQty,
      subTotalMRP: isNaN(subTotalMRP) ? 0 : subTotalMRP,
      totalItemDiscounts: isNaN(totalItemDiscounts) ? 0 : totalItemDiscounts,
      totalNormalDiscounts: isNaN(totalNormalDiscounts)
        ? 0
        : totalNormalDiscounts,
      totalSpecialDiscounts: isNaN(totalSpecialDiscounts)
        ? 0
        : totalSpecialDiscounts,
      totalBillDiscount: isNaN(currentBillDiscount) ? 0 : currentBillDiscount,
      finalTotalDiscount: isNaN(finalTotalDiscount) ? 0 : finalTotalDiscount,
      taxAmount: isNaN(taxAmount) ? 0 : taxAmount,
      grandTotalBeforeAdjustments: isNaN(grandTotalBeforeAdjustments)
        ? 0
        : grandTotalBeforeAdjustments,
      finalTotal: isNaN(finalTotal) ? 0 : finalTotal,
    };
  }, [products, tax, billDiscount, shipping]);

  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(`Error enabling full-screen: ${err.message}`);
        alert(`Could not enter full-screen: ${err.message}`);
      });
      setIsFullScreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
        setIsFullScreen(false);
      }
    }
  };

  const holdSale = useCallback(async () => {
    if (products.length === 0) {
      alert("Cannot hold an empty sale.");
      return;
    }
    setHoldingSale(true);
    const currentTotals = calculateTotals();
    const saleData = {
      products,
      totals: currentTotals,
      tax,
      billDiscount,
      billDiscountPercentage,
      shipping,
      saleType,
      customerInfo,
      billNumber,
    };
    try {
      const response = await axios.post("/api/holds", {
        terminal_id: terminalId,
        user_id: userId,
        sale_data: saleData,
      });
      if (response.data.status === "success") {
        alert(`Sale held successfully with ID: ${response.data.data.hold_id}`);
        resetPOS(false);
        loadHeldSales();
      } else {
        alert(
          "Failed to hold sale: " + (response.data.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error holding sale:", error);
      alert("Failed to hold sale.");
    } finally {
      setHoldingSale(false);
    }
  }, [
    products,
    calculateTotals,
    tax,
    billDiscount,
    billDiscountPercentage,
    shipping,
    saleType,
    customerInfo,
    billNumber,
    terminalId,
    userId,
  ]);

  const loadHeldSales = useCallback(async () => {
    setLoadingHeldSales(true);
    try {
      const response = await axios.get("/api/holds", {
        params: { terminal_id: terminalId, status: "held" },
      });
      if (response.data.status === "success") {
        setHeldSales(response.data.data);
      } else {
        alert(
          "Failed to load held sales: " +
            (response.data.message || "Unknown error")
        );
        setHeldSales([]);
      }
    } catch (error) {
      console.error("Error loading held sales:", error);
      setHeldSales([]);
    } finally {
      setLoadingHeldSales(false);
    }
  }, [terminalId]);

  const openHeldSalesList = () => {
    loadHeldSales();
    setShowHeldSalesList(true);
  };

  const closeHeldSalesList = () => {
    setShowHeldSalesList(false);
  };

  const recallHeldSale = async (holdId) => {
    try {
      const response = await axios.post(`/api/holds/${holdId}/recall`);
      if (response.data.status === "success") {
        const sale = response.data.data;
        setProducts(sale.products || []);
        setTax(sale.tax || 0);
        setBillDiscount(sale.billDiscount || 0);
        setBillDiscountPercentage(sale.billDiscountPercentage || 0);
        setShipping(sale.shipping || 0);
        setSaleType(sale.saleType || "Retail");
        setCustomerInfo(
          sale.customerInfo || {
            name: "",
            mobile: "",
            bill_number: "",
            userId: "U-1",
          }
        );
        setBillNumber(sale.billNumber || "");
        setHeldSales((prev) => prev.filter((s) => s.hold_id !== holdId));
        setShowHeldSalesList(false);
        alert(`Recalled sale with ID: ${holdId}`);
      } else {
        alert(
          "Failed to recall sale: " + (response.data.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error recalling sale:", error);
      alert("Failed to recall sale.");
    }
  };

  const deleteHeldSale = async (holdId) => {
    try {
      const response = await axios.delete(`/api/holds/${holdId}`);
      if (response.data.status === "success") {
        alert("Held sale deleted successfully");
        loadHeldSales();
      } else {
        alert(
          "Failed to delete held sale: " +
            (response.data.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error deleting held sale:", error);
      alert("Failed to delete held sale.");
    }
  };

  const resetPOS = useCallback(
    (fetchNewBill = true) => {
      setProducts([]);
      setTax(0);
      setBillDiscount(0);
      setBillDiscountPercentage(0);
      setShipping(0);
      setSearchQuery("");
      setSelectedProduct(null);
      setQuantity(1);
      setSearchResults([]);
      setSelectedSearchIndex(-1);
      setCustomerInfo({ name: "", mobile: "", bill_number: "", userId: "U-1" });

      if (fetchNewBill && !isEditMode) {
        const fetchNextBillNumber = async () => {
          try {
            const token = user?.token;
            const headers = token ? { Authorization: `Bearer ${token}` } : {};
            const response = await axios.get(
              "http://127.0.0.1:8000/api/next-bill-number",
              { headers }
            );
            setBillNumber(response.data.next_bill_number);
            setCustomerInfo((prev) => ({
              ...prev,
              bill_number: response.data.next_bill_number,
            }));
          } catch (error) {
            console.error("Error fetching next bill number:", error);
            setBillNumber("ERR-XXX");
          }
        };
        fetchNextBillNumber();
      } else {
        setCustomerInfo((prev) => ({
          ...prev,
          bill_number: isEditMode ? billNumber : "",
        }));
      }

      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    },
    [isEditMode, billNumber, user]
  );

  // Handle Alt+L for opening held sales list
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.altKey && e.key.toLowerCase() === "l") {
        e.preventDefault();
        openHeldSalesList();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  const handleOpenBill = useCallback(() => {
    if (products.length === 0) {
      alert("Cannot proceed to payment with an empty bill.");
      return;
    }

    setCustomerInfo((prevState) => ({
      ...prevState,
      bill_number: billNumber,
    }));
    setShowBillModal(true);
  }, [products, billNumber, registerStatus.isOpen]);
  // Global keyboard shortcuts
  useEffect(() => {
    const handleGlobalKeyDown = (e) => {
      if (e.altKey && e.key.toLowerCase() === "p") {
        e.preventDefault();
        handleOpenBill();
      }
      if (e.altKey && e.key.toLowerCase() === "h") {
        e.preventDefault();
        holdSale();
      }
      if (e.altKey && e.key.toLowerCase() === "r") {
        e.preventDefault();
        resetPOS(false);
      }
      if (e.altKey && e.key.toLowerCase() === "c") {
        e.preventDefault();
        setShowCalculatorModal(true);
      }
      if (e.altKey && e.key.toLowerCase() === "s") {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
    };
    window.addEventListener("keydown", handleGlobalKeyDown);
    return () => window.removeEventListener("keydown", handleGlobalKeyDown);
  }, [handleOpenBill, holdSale, resetPOS]);

  const totals = calculateTotals();

  const handleSubmit = () => {
    if (isEditMode && onSubmit) {
      onSubmit({
        products,
        billDiscount,
        billDiscountPercentage,
        tax,
        shipping,
        totals,
        customerInfo,
        saleType,
        payment_type: "Cash",
        received_amount: totals.finalTotal,
        balance_amount: 0,
      });
    }
  };

  // Daily register initialization
  useEffect(() => {
    initializeDailyRegister();
  }, [initializeDailyRegister]);

  // Check register status on mount - Only manual opening
  useEffect(() => {
    // Don't automatically open register modal - removed automatic opening logic
  }, [registerStatus.isOpen]);

  const calculateClosingDetails = () => {
    // Use totalSales and openingCash from registerStatus if available, ensure numbers
    const totalSales =
      Number(registerStatus.totalSales) ||
      products.reduce((sum, p) => sum + (p.total || 0), 0);
    const totalSalesQty =
      Number(registerStatus.totalSalesQty) ||
      products.reduce((sum, p) => sum + (p.qty || 0), 0);
    const openingCash =
      Number(registerStatus.openingCash ?? registerStatus.cashOnHand) || 0;
    return {
      totalSales,
      totalSalesQty,
      openingCash,
      inCashierAmount: 0,
      otherAmount: 0,
    };
  };

  const handleCloseRegister = () => {
    setIsClosingRegister(true);
    setShowRegisterModal(true);
  };

  const handleRegisterConfirm = async (amount) => {
    if (isClosingRegister) {
      const closingDetails = calculateClosingDetails();
      try {
        if (
          !amount ||
          typeof amount.inCashierAmount !== "number" ||
          amount.inCashierAmount < 0
        ) {
          return "Invalid amount provided for closing register.";
        }
        await closeRegister({
          ...closingDetails,
          inCashierAmount: amount.inCashierAmount,
          otherAmount: amount.otherAmount,
        });
        setIsClosingRegister(false);
        setShowRegisterModal(false);
        refreshRegisterStatus();
        return null;
      } catch (error) {
        console.error("Failed to close register:", error);
        if (
          error.response &&
          error.response.data &&
          error.response.data.message
        ) {
          return error.response.data.message;
        }
        return "Failed to close register.";
      }
    } else {
      try {
        if (!user || !user.id) {
          return "User information is missing. Cannot open register.";
        }
        let openingCash = 0;
        if (typeof amount === "number") {
          openingCash = amount;
        } else if (
          typeof amount === "object" &&
          amount !== null &&
          "inCashierAmount" in amount
        ) {
          openingCash = amount.inCashierAmount;
        } else {
          return "Invalid amount provided for opening register.";
        }
        if (openingCash < 0) {
          return "Opening cash amount cannot be negative.";
        }
        const result = await openRegister({
          user_id: user.id,
          terminal_id: terminalId,
          opening_cash: openingCash,
        });
        if (result?.success) {
          refreshRegisterStatus();
          setShowRegisterModal(false);
          return null;
        } else {
          return result?.error || "Failed to open register.";
        }
      } catch (error) {
        console.error("Failed to open register:", error);
        return "Failed to open register.";
      }
    }
  };

  // Display discount info in search results
  const getProductDiscountInfo = (item) => {
    // Create display name for batch-wise matching
    const batchInfo = item.batch_number ? ` (Batch: ${item.batch_number})` : "";
    const expiryInfo = item.expiry_date
      ? ` (Exp: ${item.expiry_date.split("T")[0]})`
      : "";
    const displayName = `${item.product_name}${batchInfo}${expiryInfo}`;

    const productScheme = activeSchemes.find(
      (scheme) =>
        scheme.active &&
        scheme.applies_to === "product" &&
        (scheme.target?.trim().toLowerCase() ===
          (item.product_name?.trim().toLowerCase() || "") ||
          scheme.target?.trim().toLowerCase() ===
            (item.description?.trim().toLowerCase() || "") ||
          scheme.target?.trim().toLowerCase() ===
            displayName.trim().toLowerCase())
    );
    const categoryScheme = activeSchemes.find(
      (scheme) =>
        scheme.active &&
        scheme.applies_to === "category" &&
        scheme.target?.trim().toLowerCase() ===
          item.category_name?.trim().toLowerCase()
    );
    let discountInfo = "";
    let freeInfo = "";
    if (productScheme) {
      if (productScheme.type === "percentage") {
        discountInfo = `Discount: Product ${productScheme.target} (${productScheme.value}%)`;
      } else if (productScheme.type === "amount") {
        discountInfo = `Discount: Product ${productScheme.target} (Rs. ${productScheme.value})`;
      } else if (productScheme.type === "free" && productScheme.buy_quantity && productScheme.free_quantity) {
        freeInfo = `Free: Buy ${productScheme.buy_quantity} Get ${productScheme.free_quantity} Free`;
      }
    } else if (categoryScheme) {
      if (categoryScheme.type === "percentage") {
        discountInfo = `Discount: Category ${categoryScheme.target} (${categoryScheme.value}%)`;
      } else if (categoryScheme.type === "amount") {
        discountInfo = `Discount: Category ${categoryScheme.target} (Rs. ${categoryScheme.value})`;
      } else if (categoryScheme.type === "free" && categoryScheme.buy_quantity && categoryScheme.free_quantity) {
        freeInfo = `Free: Buy ${categoryScheme.buy_quantity} Get ${categoryScheme.free_quantity} Free`;
      }
    }
    let info = "";
    if (discountInfo) info += ", " + discountInfo;
    if (freeInfo) info += (info ? " | " : ", ") + freeInfo;
    return info;
  };

  const openProductModal = (productId) => {
    setSelectedProductId(productId);
    setShowProductModal(true);
  };

  const closeProductModal = () => {
    setShowProductModal(false);
    setSelectedProductId(null);
  };

  // Add this handler for item add
  const handleAddItemFromPOS = () => {
    setShowAddItemForm(true);
  };

  // When generating product options for selection (e.g., in a dropdown or autocomplete):
  // Add logic to show special discount and free discount info in the label/description.

  // Example (inside your product options mapping):
  const getProductOptionLabel = (product, activeSchemes) => {
    let discountInfo = "";
    let freeInfo = "";
    const productSchemes = activeSchemes.filter(
      (s) => s.applies_to === "product" && s.target === product.product_name
    );
    productSchemes.forEach((scheme) => {
      if (scheme.type === "percentage") {
        discountInfo = `, Special: ${scheme.value}% off`;
      } else if (scheme.type === "amount") {
        discountInfo = `, Special: Rs. ${scheme.value} off`;
      } else if (scheme.type === "free" && scheme.buy_quantity && scheme.free_quantity) {
        freeInfo = `, Free: Buy ${scheme.buy_quantity} Get ${scheme.free_quantity} Free`;
      }
    });
    return `${product.product_name}${discountInfo}${freeInfo}`;
  };

  return (
    <div
      className={`min-h-screen w-full p-4 dark:bg-gray-900 bg-gray-100 ${isFullScreen ? "fullscreen-mode" : ""}`}
    >
      <div className="p-2 mb-4 rounded-lg shadow-xl bg-gradient-to-r from-slate-700 to-slate-600 dark:from-slate-800 dark:to-slate-700">
        <div className="flex flex-wrap items-center justify-between w-full gap-2 p-3 rounded-lg shadow-md md:gap-4 bg-slate-500 dark:bg-slate-600">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block mb-1 text-sm font-bold text-white">
                Sale Type
              </label>
              <select
                className="px-3 py-2 text-base text-orange-700 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:text-orange-400 dark:border-gray-600"
                value={saleType}
                onChange={(e) => setSaleType(e.target.value)}
              >
                <option value="Retail">🛒 Retail</option>
                <option value="Wholesale">📦 Wholesale</option>
              </select>
            </div>
          </div>
          <div className="flex flex-wrap items-center justify-end flex-grow gap-2 md:gap-3">
            <div className="flex items-center space-x-2">
              <label className="hidden font-bold text-white sm:inline">
                Bill No:
              </label>
              <input
                type="text"
                className="w-24 px-2 py-2 font-bold text-center text-orange-700 bg-white border border-gray-300 rounded-lg md:w-32 dark:bg-gray-700 dark:text-orange-400 dark:border-gray-600"
                value={billNumber}
                readOnly
                title="Current Bill Number"
              />
            </div>
            <button
              className="p-2 text-white bg-blue-500 rounded-lg shadow hover:bg-blue-600"
              title="View Hold List (Alt+L)"
              onClick={openHeldSalesList}
            >
              <ClipboardList size={24} />
            </button>
            <button
              className="p-2 text-white bg-yellow-500 rounded-lg shadow hover:bg-yellow-600"
              title="Dashboard"
              onClick={() => navigate("/Dashboard")}
            >
              <LayoutDashboard size={24} />
            </button>
            <button
              className="p-2 text-white bg-purple-500 rounded-lg shadow hover:bg-purple-600"
              title="Calculator (Alt+C)"
              onClick={() => setShowCalculatorModal(true)}
            >
              <Calculator size={24} />
            </button>
            <button
              className="p-2 text-white bg-green-500 rounded-lg shadow hover:bg-green-600"
              title={isFullScreen ? "Exit Fullscreen" : "Fullscreen"}
              onClick={toggleFullScreen}
            >
              {isFullScreen ? <Minimize size={24} /> : <Maximize size={24} />}
            </button>
            <button
              className="p-2 text-white bg-green-500 rounded-lg shadow hover:bg-green-600"
              title={
                registerStatus.isOpen
                  ? "Update Opening Cash"
                  : !registerStatus.dailyInitialized
                  ? "Daily Register Setup"
                  : "Add Opening Cash"
              }
              onClick={() => setShowRegisterModal(true)}
            >
              <LockOpen size={24} />
            </button>
            {registerStatus.isOpen && (
              <button
                className="p-2 text-white bg-red-500 rounded-lg shadow hover:bg-red-600"
                title="Close Register"
                onClick={handleCloseRegister}
              >
                <LogOut size={24} />
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        <div className="p-4 rounded-lg shadow-inner lg:col-span-2 bg-slate-200 dark:bg-gray-800">
          <div className="relative flex flex-col items-stretch gap-2 mb-4 md:flex-row md:items-end">
            <div className="relative flex-grow flex items-center gap-2">
              {/* Add Item Button (purple) */}
              <button
                className="p-2 text-white bg-purple-600 rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-400"
                title="Add New Item"
                onClick={handleAddItemFromPOS}
                type="button"
              >
                <Plus size={20} />
              </button>
              <div className="relative w-full">
                <input
                  id="productSearch"
                  ref={searchInputRef}
                  type="text"
                  className="w-full px-4 py-2 text-base border border-gray-300 rounded-lg text-slate-800 dark:text-white dark:bg-gray-700 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Name, Code, Barcode..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  onKeyDown={handleKeyDown}
                  disabled={loadingItems || loadingSchemes}
                  autoComplete="off"
                />
                {/* Dropdown for search results */}
                {searchResults.length > 0 && (
                  <ul className="absolute z-50 w-full mt-1 overflow-auto bg-white border border-gray-300 rounded-lg shadow-lg dark:bg-gray-700 dark:border-gray-600 max-h-60">
                    {searchResults.map((item, index) => (
                      <li
                        key={item.product_id || index}
                        className={`p-2 text-sm cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-600 ${
                          index === selectedSearchIndex
                            ? "bg-blue-200 dark:bg-blue-500 text-black dark:text-white"
                            : "text-black dark:text-gray-200"
                        }`}
                        onClick={() => handleItemSelection(item)}
                        onMouseEnter={() => setSelectedSearchIndex(index)}
                      >
                        <div className="flex flex-col">
                          <div className="font-medium">
                            {item.display_name || item.product_name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Code: {item.item_code || "N/A"} | Stock: {item.stock ?? "N/A"} | MRP: Rs{parseFloat(item.mrp || 0).toFixed(2)} | Category: {item.category_name}
                            {item.batch_number && ` | Batch: ${item.batch_number}`}
                            {item.expiry_date && ` | Exp: ${item.expiry_date.split("T")[0]}`}
                            {getProductDiscountInfo(item)}
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
            <div className="flex-shrink-0 w-full md:w-24">
              <label
                htmlFor="quantityInput"
                className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Quantity
              </label>
              <input
                id="quantityInput"
                ref={quantityInputRef}
                type="number"
                step="1"
                min="0.01"
                className="w-full px-3 py-2 text-base text-center bg-white border border-gray-300 rounded-lg md:w-24 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white dark:border-gray-600"
                placeholder="Qty"
                value={quantity}
                onChange={handleQuantityChange}
                onKeyDown={handleKeyDown}
                disabled={!selectedProduct || loadingItems || loadingSchemes}
              />
            </div>
            <div className="flex-shrink-0 w-full md:w-auto">
              <label className="block mb-1 text-sm font-medium text-transparent dark:text-transparent">
                Add
              </label>
              <button
                className="w-full px-5 py-2 text-base font-semibold text-white bg-green-600 rounded-lg shadow md:w-auto hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={addProductToTable}
                disabled={
                  !selectedProduct ||
                  parseFloat(quantity || 0) <= 0 ||
                  loadingItems ||
                  loadingSchemes
                }
              >
                Add
              </button>
            </div>
          </div>

          <h2 className="my-4 text-lg font-bold text-gray-800 dark:text-gray-200">
            Current Bill Items ({products.length})
          </h2>
          <div className="overflow-x-auto max-h-[50vh] border border-gray-300 dark:border-gray-600 rounded-lg">
            <table className="w-full text-sm text-left text-gray-700 dark:text-gray-300">
              <thead className="text-xs text-white uppercase bg-gray-700 dark:bg-gray-700 dark:text-amber-400">
                <tr>
                  <th
                    scope="col"
                    className="px-3 py-3 border-r dark:border-gray-600"
                  >
                    S.No
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3 border-r dark:border-gray-600 min-w-[200px]"
                  >
                    Name
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-center border-r dark:border-gray-600 min-w-[80px]"
                  >
                    Qty
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-center border-r dark:border-gray-600 min-w-[80px]"
                  >
                    Free
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-right border-r dark:border-gray-600 min-w-[100px]"
                  >
                    MRP
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3 text-right border-r dark:border-gray-600 min-w-[80px]"
                  >
                    Disc
                  </th>

                  <th
                    scope="col"
                    className="px-3 py-3 text-right border-r dark:border-gray-600 min-w-[80px]"
                  >
                    Price
                  </th>

                  <th
                    scope="col"
                    className="px-4 py-3 text-right border-r dark:border-gray-600 min-w-[110px]"
                  >
                    Total
                  </th>
                  <th scope="col" className="px-3 py-3 text-center">
                    <Trash2 size={16} />
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                {products.length === 0 ? (
                  <tr>
                    <td
                      colSpan="7"
                      className="py-6 italic text-center text-gray-500 dark:text-gray-400"
                    >
                      No items added to the bill yet.
                    </td>
                  </tr>
                ) : (
                  products.map((product, index) => (
                    <tr
                      key={product.product_id + "-" + index}
                      className="hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <td className="px-3 py-2 font-medium text-gray-900 border-r dark:text-white dark:border-gray-700">
                        {product.serialNumber}
                      </td>
                      <td
                        className="px-4 py-2 border-r dark:border-gray-700"
                        title={product.display_name || product.product_name}
                      >
                        <button
                          className="text-blue-600 dark:text-blue-400 hover:underline"
                          onClick={() => openProductModal(product.product_id)}
                        >
                          <div className="flex flex-col text-left">
                            <div className="font-medium">
                              {product.display_name || product.product_name}
                            </div>
                          </div>
                        </button>
                      </td>
                      <td className="px-1 py-1 text-center border-r dark:border-gray-700">
                        <input
                          type="number"
                          step="1"
                          min="0"
                          className="w-16 py-1 text-sm text-center bg-transparent border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:text-white"
                          value={product.qty}
                          onChange={(e) => updateProductQuantity(index, e.target.value)}
                          onFocus={(e) => e.target.select()}
                        />
                      </td>
                      <td className="px-3 py-2 text-center border-r dark:border-gray-700">
                        {product.free_qty || 0}
                      </td>
                      <td className="px-3 py-2 text-right border-r dark:border-gray-700">
                        <div className="flex flex-col items-end">
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            className="w-20 py-1 text-sm text-right bg-gray-100 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white no-spinner"
                            value={formatNumberWithCommas(
                              parseFloat(product.mrp || 0).toFixed(2)
                            )}
                            onChange={(e) =>
                              updateProductPrice(index, e.target.value)
                            }
                            onFocus={(e) => e.target.select()}
                            onWheel={e => e.target.blur()}
                          />
                          {/* <div className="flex flex-col items-end mt-1 text-xs text-red-600 dark:text-red-400">
                            <span>
                              MRP:{" "}
                              {formatNumberWithCommas(
                                parseFloat(product.mrp || 0).toFixed(2)
                              )}
                            </span>
                            <span>
                              Sales:{" "}
                              {formatNumberWithCommas(
                                parseFloat(product.sales_price || 0).toFixed(2)
                              )}
                            </span>
                          </div> */}
                        </div>
                      </td>
                      <td className="px-3 py-2 text-right border-r dark:border-gray-700">
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          className="w-20 py-1 text-sm text-right bg-gray-100 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white no-spinner"
                          value={formatNumberWithCommas(
                            parseFloat(product.discount || 0).toFixed(2)
                          )}
                          onChange={(e) =>
                            updateProductDiscount(index, e.target.value)
                          }
                          onFocus={(e) => e.target.select()}
                        />
                      </td>
                      <td className="px-1 py-1 text-center border-r dark:border-gray-700">
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          className="w-20 py-1 text-sm text-right bg-gray-100 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white no-spinner"
                          value={formatNumberWithCommas(
                            parseFloat(product.sales_price || 0).toFixed(2)
                          )}
                          onChange={(e) => {
                            // When unit price is changed, update discount automatically
                            const newUnitPrice = parseFloat(e.target.value) || 0;
                            setProducts((prevProducts) => prevProducts.map((p, i) => {
                              if (i === index) {
                                const qty = p.qty || 1;
                                
                                // Keep the existing special discount (don't recalculate it)
                                const existingSpecialDiscountPerUnit = p.specialDiscountPerUnit || 0;
                                const existingSpecialDiscountTotal = existingSpecialDiscountPerUnit * qty;
                                
                                // Calculate new normal discount based on new price: MRP - New Price
                                const newNormalDiscountPerUnit = Math.max(0, (p.mrp || 0) - newUnitPrice);
                                const newNormalDiscountTotal = newNormalDiscountPerUnit * qty;
                                
                                // Total discount = Normal discount + Existing Special discount
                                const totalDiscount = newNormalDiscountTotal + existingSpecialDiscountTotal;
                                
                                const newDiscountPercentage =
                                  newUnitPrice > 0 ? (newNormalDiscountPerUnit / newUnitPrice) * 100 : 0;
                                
                                // Total calculation: (MRP × Qty) - Total Discount
                                const total = (qty * (p.mrp || 0)) - totalDiscount;
                                
                                return {
                                  ...p,
                                  sales_price: newUnitPrice,
                                  price: newUnitPrice,
                                  discountPerUnit: newNormalDiscountPerUnit,
                                  specialDiscountPerUnit: existingSpecialDiscountPerUnit, // Keep existing special discount
                                  discount: totalDiscount, // Total discount (normal + special)
                                  discount_percentage: newDiscountPercentage,
                                  total: total >= 0 ? total : 0,
                                };
                              }
                              return p;
                            }));
                          }}
                          onFocus={(e) => e.target.select()}
                          onWheel={e => e.target.blur()}
                        />
                      </td>
                      <td className="px-4 py-2 font-medium text-right text-gray-900 border-r dark:text-white dark:border-gray-700">
                        {formatNumberWithCommas(
                          product.total?.toFixed(2) ?? 0.0
                        )}
                      </td>
                      <td className="px-3 py-2 text-center">
                        <button
                          onClick={() => handleDeleteClick(index)}
                          className="p-1 text-red-600 rounded-lg hover:bg-red-100 dark:hover:bg-red-700"
                          title="Delete Item"
                        >
                          <Trash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          {showNotification && (
            <Notification
              message={`Delete item "${products[pendingDeleteIndex]?.product_name ?? "this item"}"?`}
              onClose={cancelDelete}
            >
              <div className="flex justify-end gap-4 mt-4">
                <button
                  onClick={confirmDelete}
                  className="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700"
                >
                  Yes, Delete
                </button>
                <button
                  onClick={cancelDelete}
                  className="px-4 py-2 text-gray-700 bg-gray-300 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </Notification>
          )}
          {/* Add Item Modal */}
          {showAddItemForm && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
              <div className="w-full max-w-lg p-6 bg-white rounded-lg shadow-lg">
                <h2 className="text-xl font-semibold text-gray-900">Add New Item</h2>
                <ItemForm
                  onSubmit={() => {
                    setShowAddItemForm(false);
                    // Optionally, you can refresh items here if needed
                  }}
                  onClose={() => setShowAddItemForm(false)}
                  initialData={null}
                  isBatchOnly={false}
                />
              </div>
            </div>
          )}
        </div>

        <div className="w-full p-4 rounded-lg shadow-lg backdrop-blur-sm bg-slate-600 bg-opacity-80 dark:bg-gray-900 dark:bg-opacity-80">
          <h2 className="mb-4 text-xl font-bold text-white">Bill Summary</h2>
          <div className="space-y-3">
            <div>
              <label className="block mb-1 text-sm font-medium text-gray-200">
                Tax (%)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={tax}
                onChange={(e) =>
                  setTax(
                    e.target.value === "" ? "" : parseFloat(e.target.value) || 0
                  )
                }
                className="w-full px-3 py-2 text-black bg-white border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., 5"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-medium text-gray-200">
                Bill Discount (Amount)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={billDiscount}
                onChange={(e) => {
                  const discountAmount = e.target.value === "" ? 0 : parseFloat(e.target.value) || 0;
                  setBillDiscount(discountAmount);

                  // Calculate and update percentage based on discount amount
                  const currentSubtotal = totals.grandTotalBeforeAdjustments;
                  const percentage = calculatePercentageFromDiscount(discountAmount, currentSubtotal);
                  setBillDiscountPercentage(percentage);
                }}
                className="w-full px-3 py-2 text-black bg-white border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Discount amount"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-medium text-gray-200">
                Bill Discount (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={billDiscountPercentage}
                onChange={(e) => {
                  const percentage = e.target.value === "" ? 0 : parseFloat(e.target.value) || 0;
                  setBillDiscountPercentage(percentage);

                  // Calculate and update discount amount based on percentage
                  const currentSubtotal = totals.grandTotalBeforeAdjustments;
                  const discountAmount = calculateDiscountFromPercentage(percentage, currentSubtotal);
                  setBillDiscount(discountAmount);
                }}
                className="w-full px-3 py-2 text-black bg-white border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Discount percentage"
              />
            </div>
          </div>
          <div className="p-4 mt-6 text-sm text-gray-100 bg-transparent border border-gray-500 rounded-lg shadow-inner space-y-1.5 dark:border-gray-600">
            {/* Total Items / Qty line */}
            <div className="flex justify-between">
              <span className="text-sm text-white-600">Total Items / Qty:</span>
              <span className="text-sm font-medium">
                {products.length} / {products.reduce((sum, p) => sum + Number(p.qty || 0), 0)}
              </span>
            </div>

            {/* Free Items section directly under Total Items / Qty */}
            {products.some(p => p.free_qty > 0) && (
              <>
                <div className="mt-1 text-sm text-blue-500">
                  <strong>Free Items:</strong>
                  <ul className="ml-4 list-disc">
                    {products.map((p, idx) => {
                      if (p.free_qty > 0) {
                        return (
                          <li key={p.product_id + '-' + idx}>
                            {p.display_name || p.product_name}: <strong>{p.free_qty}</strong>
                          </li>
                        );
                      }
                      return null;
                    })}
                  </ul>
                </div>
                <div className="flex justify-between text-red-300">
                  <span>(-) Item Discounts:</span>
                  <span className="font-medium">
                    Rs.{" "}
                    {formatNumberWithCommas(totals.totalItemDiscounts.toFixed(2))}
                  </span>
                </div>
              </>
            )}
            {/* <div className="flex justify-between text-red-300">
              <span>(-) Normal Discounts:</span>
              <span className="font-medium">
                Rs. {formatNumberWithCommas(totals.totalNormalDiscounts.toFixed(2))}
              </span>
            </div> */}
            {/* <div className="flex justify-between ml-4 text-xs text-red-200">
              <span>• Special Discounts:</span>
              <span className="font-medium">
                Rs.{" "}
                {formatNumberWithCommas(
                  totals.totalSpecialDiscounts.toFixed(2)
                )}
              </span>
            </div> */}
            {/* <div className="flex justify-between">
              <span>Net Item Total:</span>
              <span className="font-medium">
                Rs.{" "}
                {formatNumberWithCommas(
                  totals.grandTotalBeforeAdjustments.toFixed(2)
                )}
              </span>
            </div> */}
            <div className="flex justify-between text-yellow-300">
              <span>(+) Tax ({parseFloat(tax || 0).toFixed(1)}%):</span>
              <span className="font-medium">
                Rs. {formatNumberWithCommas(totals.taxAmount.toFixed(2))}
              </span>
            </div>
            <div className="flex justify-between text-red-300">
              <span>(-) Bill Discount:</span>
              <span className="font-medium">
                Rs.{" "}
                {formatNumberWithCommas(totals.totalBillDiscount.toFixed(2))}
              </span>
            </div>
            <hr className="my-2 border-gray-500 dark:border-gray-600" />
            <div className="flex justify-between text-xl font-bold text-green-400">
              <span>Grand Total:</span>
              <span>
                Rs. {formatNumberWithCommas(totals.finalTotal.toFixed(2))}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-3 mt-6">
            {isEditMode ? (
              <>
                <button
                  className="flex items-center justify-center gap-1 px-3 py-3 text-sm font-semibold text-white bg-red-600 rounded-lg shadow hover:bg-red-700"
                  onClick={onCancel}
                >
                  Cancel
                </button>
                <button
                  className="flex items-center justify-center col-span-2 gap-1 px-3 py-3 text-sm font-semibold text-white bg-blue-600 rounded-lg shadow hover:bg-blue-700 disabled:opacity-50"
                  onClick={handleSubmit}
                  disabled={products.length === 0}
                >
                  Update Sale
                </button>
              </>
            ) : (
              <>
                <button
                  className="flex items-center justify-center gap-1 px-3 py-3 text-sm font-semibold text-white rounded-lg shadow bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50"
                  onClick={holdSale}
                  title="Hold Bill (Alt+H)"
                  disabled={products.length === 0 || holdingSale}
                >
                  <PauseCircle size={18} /> Hold
                </button>
                <button
                  className="flex items-center justify-center gap-1 px-3 py-3 text-sm font-semibold text-white bg-gray-500 rounded-lg shadow hover:bg-gray-600"
                  onClick={() => resetPOS(false)}
                  title="Reset Bill (Alt+R)"
                >
                  <RefreshCw size={18} /> Reset
                </button>
                <button
                  ref={payButtonRef}
                  className="flex items-center justify-center gap-1 px-3 py-3 text-sm font-semibold text-white rounded-lg shadow bg-fuchsia-600 hover:bg-fuchsia-700 disabled:opacity-50"
                  onClick={handleOpenBill}
                  title="Proceed to Pay (Alt+P)"
                  disabled={products.length === 0}
                >
                  <Printer size={18} /> Pay
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {showRegisterModal && (
        <RegisterModal
          isOpen={showRegisterModal}
          onClose={() => {
            setShowRegisterModal(false);
            setIsClosingRegister(false);
          }}
          onConfirm={handleRegisterConfirm}
          cashOnHand={registerStatus.cashOnHand}
          user={user}
          mode={isClosingRegister ? "close" : registerStatus.isOpen ? "updateCash" : "open"}
          closingDetails={calculateClosingDetails()}
        />
      )}
      {stockWarning && (
        <div className="flex items-center p-2 my-2 text-sm text-yellow-700 bg-yellow-100 rounded-md">
          <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
          {stockWarning}
          <span className="ml-2 text-yellow-800">(Sale will proceed)</span>
        </div>
      )}
      {showBillModal && (
        <BillPrintModal
          initialProducts={products}
          initialBillDiscount={parseFloat(billDiscount || 0)}
          initialTax={parseFloat(tax || 0)}
          initialShipping={parseFloat(shipping || 0)}
          initialTotals={totals}
          initialCustomerInfo={customerInfo}
          saleType={saleType}
          onClose={closeBillModal}
          activeSchemes={activeSchemes}
        />
      )}
      {showCalculatorModal && (
        <CalculatorModal
          isOpen={showCalculatorModal}
          onClose={() => setShowCalculatorModal(false)}
        />
      )}
      {isCloseRegisterOpen && (
        <CloseRegisterModal
          isOpen={isCloseRegisterOpen}
          onClose={() => setIsCloseRegisterOpen(false)}
        />
      )}
      {showHeldSalesList && (
        <HeldSalesList
          heldSales={heldSales}
          loading={loadingHeldSales}
          onRecall={recallHeldSale}
          onDelete={deleteHeldSale}
          onClose={closeHeldSalesList}
        />
      )}
      {showProductModal && (
        <ProductDetailsModal
          productId={selectedProductId}
          onClose={closeProductModal}
        />
      )}
    </div>
  );
};

export default POSForm;
