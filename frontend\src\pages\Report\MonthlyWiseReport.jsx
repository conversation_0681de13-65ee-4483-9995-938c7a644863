import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FiChevronDown, FiChevronUp, FiPrinter } from "react-icons/fi";
import axios from "axios";

const API_BASE_URL = "http://127.0.0.1:8000/api";

const MonthlyWiseReport = () => {
  const currentYear = new Date().getFullYear().toString();
  
  const [fromYear, setFromYear] = useState(currentYear);
  const [toYear, setToYear] = useState(currentYear);
  const [reportData, setReportData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [expandedRow, setExpandedRow] = useState(null);

  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const formatCurrency = (amount) => {
    const numericAmount = Number(amount);
    return isNaN(numericAmount)
      ? "LKR 0.00"
      : new Intl.NumberFormat("en-LK", {
          style: "currency",
          currency: "LKR",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(numericAmount);
  };

  const formatPercentage = (value) => {
    return isNaN(value) || value === Infinity || value === -Infinity
      ? "N/A"
      : `${value.toFixed(2)}%`;
  };

  const getYearRange = useCallback(() => {
    const start = parseInt(fromYear);
    const end = parseInt(toYear);
    if (isNaN(start) || isNaN(end) || start > end) return [parseInt(currentYear)];
    return Array.from({ length: end - start + 1 }, (_, i) => (start + i).toString());
  }, [fromYear, toYear, currentYear]);

  const fetchReportData = useCallback(async () => {
    setLoading(true);
    try {
      const yearRange = getYearRange();
      const fromDate = `${yearRange[0]}-01-01`;
      const toDate = `${yearRange[yearRange.length - 1]}-12-31`;

      const [invoicesResponse, salesResponse] = await Promise.all([
        axios.get(`${API_BASE_URL}/invoices`, {
          params: { from: fromDate, to: toDate },
          headers: { Accept: "application/json" },
        }),
        axios.get(`${API_BASE_URL}/sales`, {
          params: { from: fromDate, to: toDate },
          headers: { Accept: "application/json" },
        }),
      ]);

      const invoices = Array.isArray(invoicesResponse.data.data)
        ? invoicesResponse.data.data
        : Array.isArray(invoicesResponse.data)
        ? invoicesResponse.data
        : [];

      const sales = Array.isArray(salesResponse.data.data)
        ? salesResponse.data.data
        : Array.isArray(salesResponse.data)
        ? salesResponse.data
        : [];

      const combinedData = [...invoices, ...sales].map(item => ({
        ...item,
        type: item.invoice_no ? "invoice" : "sale",
        bill_number: item.invoice_no || item.bill_number || `SALE-${item.id}`,
        customer_name: item.customer_name || (item.type === "sale" ? "Walk-in Customer" : "Unknown Customer"),
        invoice_date: item.invoice_date || item.created_at,
        total_amount: parseFloat(item.total_amount) || 0,
        items: Array.isArray(item.items) ? item.items : [],
      }));

      const monthlyData = months.map((month, index) => {
        const monthIndex = index + 1;
        const yearData = yearRange.reduce((acc, year) => {
          const yearInt = parseInt(year);
          const transactions = combinedData.filter(item => {
            const date = new Date(item.invoice_date);
            return date.getFullYear() === yearInt && date.getMonth() === index;
          });

          const stats = transactions.reduce(
            (acc, item) => {
              const totalBuyingCost = item.items.reduce(
                (sum, i) => sum + (parseFloat(i.total_buying_cost || 0)),
                0
              );
              return {
                totalSales: acc.totalSales + item.total_amount,
                totalCost: acc.totalCost + totalBuyingCost,
              };
            },
            { totalSales: 0, totalCost: 0 }
          );

          const profitPercentage = stats.totalSales > 0
            ? ((stats.totalSales - stats.totalCost) / stats.totalSales) * 100
            : 0;

          const prevYear = (yearInt - 1).toString();
          const prevYearStats = acc[prevYear] || { totalSales: 0 };
          const salesGrowth = prevYearStats.totalSales > 0
            ? ((stats.totalSales - prevYearStats.totalSales) / prevYearStats.totalSales) * 100
            : stats.totalSales > 0 ? 100 : 0;

          return {
            ...acc,
            [year]: {
              year,
              ...stats,
              profitPercentage,
              transactions,
              salesGrowth: prevYearStats.totalSales ? salesGrowth : "N/A"
            }
          };
        }, {});

        return {
          month,
          years: yearData
        };
      });

      setReportData(monthlyData);
    } catch (error) {
      console.error("Error fetching monthly report data:", error);
      setReportData([]);
      alert(`Error fetching data: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  }, [getYearRange]);

  useEffect(() => {
    fetchReportData();
  }, [fetchReportData]);

  const toggleRow = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  const handlePrint = () => {
    const printWindow = window.open("", "_blank");
    const yearRange = getYearRange();

    const printContent = `
      <html>
        <head>
          <title>Monthly Sales Report (${fromYear}-${toYear})</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { text-align: center; }
            h2 { margin-top: 30px; color: #333; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; }
            td:first-child, th:first-child { text-align: left; }
            .month-header { font-weight: bold; background-color: #e6f3ff; }
            .positive { color: green; }
            .negative { color: red; }
            @media print {
              .no-print { display: none; }
              h2 { page-break-before: auto; }
              table { page-break-inside: auto; }
            }
          </style>
        </head>
        <body>
          <h1>Monthly Sales Report (${fromYear} - ${toYear})</h1>
          ${yearRange.map(year => `
            <h2>Year ${year}</h2>
            <table>
              <thead>
                <tr>
                  <th>Month</th>
                  <th>Sales</th>
                  <th>Cost</th>
                  <th>Profit (%)</th>
                  <th>Growth (%)</th>
                </tr>
              </thead>
              <tbody>
                ${reportData.map(row => `
                  <tr class="month-header">
                    <td>${row.month}</td>
                    <td>${formatCurrency(row.years[year]?.totalSales || 0)}</td>
                    <td>${formatCurrency(row.years[year]?.totalCost || 0)}</td>
                    <td>${formatPercentage(row.years[year]?.profitPercentage || 0)}</td>
                    <td class="${row.years[year]?.salesGrowth >= 0 && row.years[year]?.salesGrowth !== "N/A" ? "positive" : row.years[year]?.salesGrowth === "N/A" ? "" : "negative"}">
                      ${formatPercentage(row.years[year]?.salesGrowth)}
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          `).join('')}
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="flex flex-col min-h-screen p-4 bg-gray-50 dark:bg-gray-900">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="py-3 mb-6 text-center text-white rounded-lg shadow-lg bg-gradient-to-r from-blue-600 to-blue-800 dark:from-blue-900 dark:to-slate-800"
      >
        <h1 className="text-2xl font-bold">MONTHLY SALES REPORT</h1>
        <p className="text-sm opacity-90">Compare sales performance across years</p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="flex flex-wrap items-center justify-between gap-4 mb-6"
      >
        <div className="flex gap-4">
          <div>
            <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">From Year</label>
            <input
              type="number"
              value={fromYear}
              onChange={(e) => setFromYear(e.target.value)}
              className="w-32 p-2 border border-gray-300 rounded-lg dark:bg-gray-800 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="2000"
              max="2099"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">To Year</label>
            <input
              type="number"
              value={toYear}
              onChange={(e) => setToYear(e.target.value)}
              className="w-32 p-2 border border-gray-300 rounded-lg dark:bg-gray-800 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="2000"
              max="2099"
            />
          </div>
          <div className="flex gap-2 mt-6">
            <button
              onClick={fetchReportData}
              disabled={loading}
              className={`px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${
                loading ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {loading ? "Loading..." : "Generate Report"}
            </button>
            <button
              onClick={handlePrint}
              disabled={loading || reportData.length === 0}
              className={`px-4 py-2 text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center gap-2 ${
                loading || reportData.length === 0 ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              <FiPrinter size={18} />
              Print Report
            </button>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700"
      >
        {loading ? (
          <div className="flex items-center justify-center p-20">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity }}
              className="w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full"
            ></motion.div>
            <p className="ml-4 text-gray-600 dark:text-gray-400">Loading Report...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-300">
                <tr>
                  <th className="px-4 py-3 font-semibold text-left">Month</th>
                  {getYearRange().map(year => (
                    <React.Fragment key={year}>
                      <th className="px-4 py-3 font-semibold text-right">{year} Sales</th>
                      <th className="px-4 py-3 font-semibold text-right">{year} Cost</th>
                      <th className="px-4 py-3 font-semibold text-right">{year} Profit (%)</th>
                      <th className="px-4 py-3 font-semibold text-right">{year} Growth (%)</th>
                    </React.Fragment>
                  ))}
                  <th className="px-4 py-3 font-semibold text-right">Details</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                {reportData.map((row, index) => (
                  <React.Fragment key={row.month}>
                    <motion.tr
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                        expandedRow === index ? "bg-blue-50 dark:bg-gray-700" : ""
                      }`}
                    >
                      <td className="px-4 py-3 font-medium text-gray-900 dark:text-white">{row.month}</td>
                      {getYearRange().map(year => (
                        <React.Fragment key={year}>
                          <td className="px-4 py-3 text-right text-gray-600 dark:text-gray-300">
                            {formatCurrency(row.years[year]?.totalSales || 0)}
                          </td>
                          <td className="px-4 py-3 text-right text-gray-600 dark:text-gray-300">
                            {formatCurrency(row.years[year]?.totalCost || 0)}
                          </td>
                          <td className="px-4 py-3 text-right text-gray-600 dark:text-gray-300">
                            {formatPercentage(row.years[year]?.profitPercentage || 0)}
                          </td>
                          <td className="px-4 py-3 text-right">
                            <span className={`font-semibold ${
                              row.years[year]?.salesGrowth >= 0 && row.years[year]?.salesGrowth !== "N/A"
                                ? "text-green-600 dark:text-green-400"
                                : row.years[year]?.salesGrowth === "N/A"
                                ? "text-gray-600 dark:text-gray-400"
                                : "text-red-600 dark:text-red-400"
                            }`}>
                              {formatPercentage(row.years[year]?.salesGrowth)}
                            </span>
                          </td>
                        </React.Fragment>
                      ))}
                      <td className="px-4 py-3 text-right">
                        <button
                          onClick={() => toggleRow(index)}
                          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white focus:outline-none"
                        >
                          {expandedRow === index ? <FiChevronUp size={18} /> : <FiChevronDown size={18} />}
                        </button>
                      </td>
                    </motion.tr>
                    <AnimatePresence>
                      {expandedRow === index && (
                        <motion.tr
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="bg-gray-50 dark:bg-gray-800"
                        >
                          <td colSpan={1 + getYearRange().length * 4} className="px-4 py-4">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                              {getYearRange().map(year => (
                                <div key={year} className="p-3 border border-gray-200 rounded-md dark:border-gray-700">
                                  <h4 className="mb-2 text-xs font-semibold text-gray-600 uppercase dark:text-gray-400">
                                    {year} Transactions ({row.years[year]?.transactions.length || 0})
                                  </h4>
                                  {row.years[year]?.transactions.length > 0 ? (
                                    <table className="min-w-full text-xs divide-y divide-gray-200 dark:divide-gray-600">
                                      <thead className="bg-gray-100 dark:bg-gray-700">
                                        <tr>
                                          <th className="px-2 py-1 text-left">Type</th>
                                          <th className="px-2 py-1 text-left">Bill #</th>
                                          <th className="px-2 py-1 text-left">Customer</th>
                                          <th className="px-2 py-1 text-right">Amount</th>
                                          <th className="px-2 py-1 text-right">Buying Cost</th>
                                        </tr>
                                      </thead>
                                      <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                                        {row.years[year]?.transactions.map((tx) => (
                                          <tr key={`${tx.type}-${tx.id}`}>
                                            <td className="px-2 py-1">{tx.type === "invoice" ? "Invoice" : "Sale"}</td>
                                            <td className="px-2 py-1">{tx.bill_number}</td>
                                            <td className="px-2 py-1">{tx.customer_name}</td>
                                            <td className="px-2 py-1 text-right">{formatCurrency(tx.total_amount)}</td>
                                            <td className="px-2 py-1 text-right">{formatCurrency(
                                              tx.items.reduce((sum, i) => sum + (parseFloat(i.total_buying_cost || 0)), 0)
                                            )}</td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  ) : (
                                    <p className="text-sm text-gray-500 dark:text-gray-400">No transactions</p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </td>
                        </motion.tr>
                      )}
                    </AnimatePresence>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default MonthlyWiseReport;