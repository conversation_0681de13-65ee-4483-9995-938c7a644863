import React, { useState, useEffect } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";

export const AccountGroupModal = ({ isOpen, onClose, onSubGroupAdded }) => {
  const [formData, setFormData] = useState({
    sub_group_name: "",
    main_group: "",
  });

  const [subGroups, setSubGroups] = useState([]);
  const [selectedSubGroups, setSelectedSubGroups] = useState([]);
  const [loading, setLoading] = useState(false);

  const accountGroups = [
    "Stock in hand",
    "Purchase Account",
    "Direct Expenses",
    "Sales Accounts",
    "Indirect Expenses",
    "Indirect Income",
    "Loan Liabilities",
    "Bank OD",
    "Current Liabilities",
    "Sundry Creditors",
    "Capital Account",
    "Bank Accounts",
    "Cash in Hand",
    "Current Asset",
    "Sundry Debtors",
    "Fixed Asset",
  ];

  const API_URL = "http://127.0.0.1:8000/api/account-sub-groups";

  useEffect(() => {
    if (isOpen) {
      fetchSubGroups();
    }
  }, [isOpen]);

  const fetchSubGroups = async () => {
    try {
      const response = await axios.get(API_URL);
      setSubGroups(response.data.data);
    } catch (error) {
      console.error("Error fetching sub groups:", error);
      toast.error("Failed to fetch sub groups");
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.sub_group_name || !formData.main_group) {
      toast.error("Please fill all required fields");
      return;
    }

    setLoading(true);
    try {
      await axios.post(API_URL, formData);
      toast.success("Sub group added successfully");
      setFormData({ sub_group_name: "", main_group: "" });
      fetchSubGroups();
      if (onSubGroupAdded) {
        onSubGroupAdded();
      }
    } catch (error) {
      console.error("Error adding sub group:", error);
      toast.error(error.response?.data?.message || "Failed to add sub group");
    } finally {
      setLoading(false);
    }
  };

  const handleSelectSubGroup = (id) => {
    setSelectedSubGroups((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleDeleteSelected = async () => {
    if (selectedSubGroups.length === 0) {
      toast.error("Please select sub groups to delete");
      return;
    }

    if (!window.confirm("Are you sure you want to delete selected sub groups?")) {
      return;
    }

    setLoading(true);
    try {
      await axios.post(`${API_URL}/bulk-delete`, { ids: selectedSubGroups });
      toast.success("Selected sub groups deleted successfully");
      setSelectedSubGroups([]);
      fetchSubGroups();
      if (onSubGroupAdded) {
        onSubGroupAdded();
      }
    } catch (error) {
      console.error("Error deleting sub groups:", error);
      toast.error("Failed to delete sub groups");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 backdrop-blur-sm bg-black/30">
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-4xl bg-white shadow-xl dark:bg-gray-900 rounded-2xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100">
              Account Sub Groups
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 transition-colors rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-6 h-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Add Sub Group Form */}
            <form onSubmit={handleSubmit} className="mb-8 bg-white">
              <h3 className="mb-4 text-lg font-medium text-gray-800 dark:text-gray-100">
                Add New Sub Group
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Account Sub Group <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="sub_group_name"
                    value={formData.sub_group_name}
                    onChange={handleChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter sub group name"
                    required
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Under Group (Main Groups) <span className="text-red-500">*</span>
                  </label>
                  <select
                    name="main_group"
                    value={formData.main_group}
                    onChange={handleChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    required
                  >
                    <option value="">Select Main Group</option>
                    {accountGroups.map((group, index) => (
                      <option key={index} value={group}>
                        {group}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="flex items-end">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    {loading ? "Adding..." : "Add"}
                  </button>
                </div>
              </div>
            </form>

            {/* Sub Groups Table */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100">
                  Group Names (Sub Groups)
                </h3>
                {selectedSubGroups.length > 0 && (
                  <button
                    onClick={handleDeleteSelected}
                    disabled={loading}
                    className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    Delete Selected ({selectedSubGroups.length})
                  </button>
                )}
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                        Select
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                        Sub Group Name
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                        Main Group
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
                    {subGroups.map((subGroup) => (
                      <tr key={subGroup.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedSubGroups.includes(subGroup.id)}
                            onChange={() => handleSelectSubGroup(subGroup.id)}
                            className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                          />
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap dark:text-gray-100">
                          {subGroup.sub_group_name}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap dark:text-gray-100">
                          {subGroup.main_group}
                        </td>
                      </tr>
                    ))}
                    {subGroups.length === 0 && (
                      <tr>
                        <td colSpan="3" className="px-6 py-4 text-sm text-center text-gray-500 dark:text-gray-400">
                          No sub groups found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer position="bottom-right" autoClose={3000} />
    </div>
  );
};
