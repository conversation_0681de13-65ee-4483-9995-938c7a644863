import React, { useState, useEffect } from "react";
import axios from "axios";
import { motion } from "framer-motion";

export const SalesBillTemplate = () => {
  const [templates, setTemplates] = useState([]);
  const [templateName, setTemplateName] = useState("");
  const [templateContent, setTemplateContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [currentTemplateId, setCurrentTemplateId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  // Available placeholders with icons
  const placeholders = [
    { label: "Customer Name", value: "{{customer_name}}", icon: "👤" },
    { label: "Bill Number", value: "{{bill_number}}", icon: "📝" },
    { label: "Bill Date", value: "{{bill_date}}", icon: "📅" },
    { label: "Bill Amount", value: "{{bill_amount}}", icon: "💰" },
    { label: "Bill URL", value: "{{bill_url}}", icon: "🔗" },
    { label: "Company Name", value: "{{company_name}}", icon: "🏢" },
    { label: "Cashier Name", value: "{{cashier_name}}", icon: "👨‍💼" },
    { label: "Bill Time", value: "{{bill_time}}", icon: "⏰" },
  ];

  // Sample template suggestions
  const templateSuggestions = [
    {
      name: "Thank You Message",
      content:
        "Thank you {{customer_name}} for your purchase! View your bill: {{bill_url}}",
    },
  ];

  // Fetch templates on component mount
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setIsLoading(true);
        const response = await axios.get(
          "http://127.0.0.1:8000/api/sales-templates"
        );
        setTemplates(response.data.data || []);
      } catch (err) {
        setError("Failed to fetch templates");
        console.error("Error fetching templates:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!templateName.trim() || !templateContent.trim()) {
      setError("Template name and content are required");
      return;
    }

    try {
      setIsLoading(true);
      setError("");
      setSuccessMessage("");

      if (isEditing) {
        // Update existing template
        await axios.put(
          `http://127.0.0.1:8000/api/sales-templates/${currentTemplateId}`,
          { name: templateName, content: templateContent }
        );
        setSuccessMessage("Template updated successfully!");
      } else {
        // Create new template
        await axios.post("http://127.0.0.1:8000/api/sales-templates", {
          name: templateName,
          content: templateContent,
          is_default: templates.length === 0,
        });
        setSuccessMessage("Template created successfully!");
      }

      // Refresh templates
      const response = await axios.get(
        "http://127.0.0.1:8000/api/sales-templates"
      );
      setTemplates(response.data.data || []);

      // Reset form
      setTemplateName("");
      setTemplateContent("");
      setIsEditing(false);
      setCurrentTemplateId(null);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err) {
      setError(err.response?.data?.message || "Failed to save template");
      console.error("Error saving template:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Edit template
  const handleEdit = (template) => {
    setTemplateName(template.name);
    setTemplateContent(template.content);
    setIsEditing(true);
    setCurrentTemplateId(template.id);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // Delete template
  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this template?"))
      return;

    try {
      setIsLoading(true);
      await axios.delete(`http://127.0.0.1:8000/api/sales-templates/${id}`);

      // Refresh templates
      const response = await axios.get(
        "http://127.0.0.1:8000/api/sales-templates"
      );
      setTemplates(response.data.data || []);
      setSuccessMessage("Template deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err) {
      setError("Failed to delete template");
      console.error("Error deleting template:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Set template as default
  const handleSetDefault = async (id) => {
    try {
      setIsLoading(true);
      await axios.post(
        `http://127.0.0.1:8000/api/sales-templates/${id}/set-default`
      );

      // Refresh templates
      const response = await axios.get(
        "http://127.0.0.1:8000/api/sales-templates"
      );
      setTemplates(response.data.data || []);
      setSuccessMessage("Default template set successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err) {
      setError("Failed to set default template");
      console.error("Error setting default template:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Insert placeholder into content
  const insertPlaceholder = (placeholder) => {
    const textarea = document.getElementById("templateContent");
    const startPos = textarea.selectionStart;
    const endPos = textarea.selectionEnd;
    const currentValue = textarea.value;

    setTemplateContent(
      currentValue.substring(0, startPos) +
        placeholder +
        currentValue.substring(endPos)
    );
    textarea.focus();
  };

  // Apply template suggestion
  const applySuggestion = (suggestion) => {
    setTemplateName(suggestion.name);
    setTemplateContent(suggestion.content);
  };

  return (
    <div className="container px-4 py-8 mx-auto max-w-7xl ">
      <motion.div
        className="p-6 mb-8 text-center shadow-sm from-blue-50 to-indigo-50 rounded-xl "
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <h1 className="mb-2 text-3xl font-bold text-gray-800">
          Customer Message Templates
        </h1>
        <p className="text-gray-600">
          Create beautiful message templates to send to customers with their
          bill details
        </p>
      </motion.div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Template Form */}
        <motion.div
          className="p-6 bg-white border border-gray-100 shadow-sm rounded-xl"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <h2 className="mb-4 text-xl font-semibold text-gray-800">
            {isEditing ? "✏️ Edit Template" : "✨ Create New Template"}
          </h2>

          {error && (
            <div className="p-3 mb-4 text-red-700 rounded-lg bg-red-50">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="p-3 mb-4 text-green-700 rounded-lg bg-green-50">
              {successMessage}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-4 ">
              <label
                htmlFor="templateName"
                className="block mb-2 font-medium text-gray-700"
              >
                Template Name
              </label>
              <input
                type="text"
                id="templateName"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                className="w-full p-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-300 focus:border-blue-300"
                placeholder="e.g., Payment Reminder"
                required
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="templateContent"
                className="block mb-2 font-medium text-gray-700"
              >
                Message Content
              </label>
              <textarea
                id="templateContent"
                value={templateContent}
                onChange={(e) => setTemplateContent(e.target.value)}
                className="w-full h-48 p-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-300 focus:border-blue-300"
                placeholder="Write your message template here..."
                required
              />
            </div>

            <div className="mb-6">
              <label className="block mb-2 font-medium text-gray-700">
                Available Placeholders
              </label>
              <div className="flex flex-wrap gap-2">
                {placeholders.map((placeholder) => (
                  <motion.button
                    key={placeholder.value}
                    type="button"
                    onClick={() => insertPlaceholder(placeholder.value)}
                    className="flex items-center px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg hover:bg-gray-50"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <span className="mr-2">{placeholder.icon}</span>
                    {placeholder.label}
                  </motion.button>
                ))}
              </div>
            </div>

            <div className="flex justify-end gap-3">
              {isEditing && (
                <motion.button
                  type="button"
                  onClick={() => {
                    setIsEditing(false);
                    setTemplateName("");
                    setTemplateContent("");
                    setCurrentTemplateId(null);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Cancel
                </motion.button>
              )}
              <motion.button
                type="submit"
                className="px-6 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="w-5 h-5 mr-2 -ml-1 animate-spin"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    {isEditing ? "Updating..." : "Creating..."}
                  </span>
                ) : isEditing ? (
                  "Update Template"
                ) : (
                  "Create Template"
                )}
              </motion.button>
            </div>
          </form>
        </motion.div>

        {/* Template List */}
        <motion.div
          className="p-6 bg-white border border-gray-100 shadow-sm rounded-xl"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-800">
              📋 Saved Templates
            </h2>
            {templates.length > 0 && (
              <span className="px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full">
                {templates.length} templates
              </span>
            )}
          </div>

          {isLoading && !templates.length ? (
            <div className="flex items-center justify-center h-32">
              <div className="w-8 h-8 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
            </div>
          ) : templates.length === 0 ? (
            <div className="p-8 text-center rounded-lg bg-gray-50">
              <svg
                className="w-12 h-12 mx-auto text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
              <h3 className="mt-2 text-lg font-medium text-gray-700">
                No templates yet
              </h3>
              <p className="mt-1 text-gray-500">
                Create your first template to get started
              </p>

              <div className="mt-6">
                <h4 className="mb-3 font-medium text-gray-700">
                  Try these suggestions:
                </h4>
                <div className="space-y-3">
                  {templateSuggestions.map((suggestion, index) => (
                    <motion.div
                      key={index}
                      className="p-3 text-left rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                      onClick={() => applySuggestion(suggestion)}
                      whileHover={{ x: 5 }}
                    >
                      <div className="font-medium text-gray-800">
                        {suggestion.name}
                      </div>
                      <div className="text-sm text-gray-600 truncate">
                        {suggestion.content}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {templates.map((template) => (
                <motion.div
                  key={template.id}
                  className={`p-4 bg-white border rounded-lg hover:border-blue-200 hover:shadow-xs ${template.is_default ? "border-green-500 bg-green-50" : "border-gray-200"}`}
                  whileHover={{ scale: 1.005 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-800">
                      {template.name}
                    </h3>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(template)}
                        className="p-1.5 text-blue-600 rounded-full hover:bg-blue-50"
                        title="Edit"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-5 h-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleDelete(template.id)}
                        className="p-1.5 text-red-600 rounded-full hover:bg-red-50"
                        title="Delete"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-5 h-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleSetDefault(template.id)}
                        className={`p-1.5 rounded-full ${template.is_default ? "text-green-600 bg-green-100" : "text-gray-600 hover:bg-gray-50"}`}
                        title={
                          template.is_default
                            ? "Default Template"
                            : "Set as Default"
                        }
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-5 h-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-gray-600 whitespace-pre-wrap">
                    {template.content}
                  </div>
                  <div className="flex items-center justify-between mt-3">
                    <div className="text-xs text-gray-500">
                      Last updated:{" "}
                      {new Date(template.updated_at).toLocaleString()}
                    </div>
                    <button
                      onClick={() => {
                        setTemplateName(template.name);
                        setTemplateContent(template.content);
                      }}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      Use as draft
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>

      {/* Preview Section */}
      <motion.div
        className="p-6 mt-8 bg-white border border-gray-100 shadow-sm rounded-xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <h2 className="mb-4 text-xl font-semibold text-gray-800">
          💬 Message Preview
        </h2>
        <div className="p-4 rounded-lg bg-gray-50">
          <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-inner">
            {templateContent ? (
              <div className="whitespace-pre-wrap">
                {templateContent
                  .replace("{{customer_name}}", "John Doe")
                  .replace("{{bill_number}}", "INV-2023-001")
                  .replace("{{bill_date}}", new Date().toLocaleDateString())
                  .replace("{{bill_amount}}", "₹1,250.00")
                  .replace(
                    "{{bill_url}}",
                    "https://example.com/bills/INV-2023-001"
                  )
                  .replace("{{company_name}}", "Your Company Name")
                  .replace("{{cashier_name}}", "Sarah Johnson")
                  .replace("{{bill_time}}", new Date().toLocaleTimeString())}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 text-center text-gray-500">
                <svg
                  className="w-12 h-12 mb-3 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="1.5"
                    d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                  ></path>
                </svg>
                <p>Create or select a template to see preview</p>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
};
