import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import Select from "react-select";
import axios from "axios";
import { toast } from "react-toastify";
import { format } from "date-fns";
import { useAuth } from "../../context/NewAuthContext";

const API_BASE_URL = "http://127.0.0.1:8000/api";

const unifiedSelectStyles = (hasError) => ({
  control: (provided, state) => ({
    ...provided,
    borderColor: hasError ? "#ef4444" : state.isFocused ? "#3b82f6" : "#d1d5db",
    boxShadow: state.isFocused
      ? "0 0 0 1px #3b82f6"
      : hasError
      ? "0 0 0 1px #ef4444"
      : "none",
    "&:hover": { borderColor: hasError ? "#ef4444" : "#9ca3af" },
    minHeight: "42px",
    borderRadius: "0.375rem",
    fontSize: "0.875rem",
  }),
  menu: (provided) => ({ ...provided, zIndex: 9999, fontSize: "0.875rem" }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? "#dbeafe"
      : state.isFocused
      ? "#eff6ff"
      : "white",
    color: state.isSelected ? "#2563eb" : "black",
    cursor: "pointer",
  }),
  placeholder: (provided) => ({
    ...provided,
    color: "#6b7280",
  }),
  singleValue: (provided) => ({
    ...provided,
    color: "#111827",
  }),
});

class ErrorBoundary extends React.Component {
  state = { hasError: false };
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  componentDidCatch(error, errorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }
  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-600 bg-red-100 border border-red-400 rounded">
          <h1 className="font-bold">Something went wrong.</h1>
          <p>Please refresh the page or contact support.</p>
        </div>
      );
    }
    return this.props.children;
  }
}

const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-LK", {
    style: "currency",
    currency: "LKR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const StockTransferForm = ({ initialData, isEditMode, onCreateTransfer, onUpdateTransfer, onCancel }) => {
  const { user: currentUser } = useAuth();
  const draftKey = "stockTransferDraft";
  const formatDate = (date) => {
    try {
      return date ? format(new Date(date), "yyyy-MM-dd") : format(new Date(), "yyyy-MM-dd");
    } catch {
      return format(new Date(), "yyyy-MM-dd");
    }
  };

  // Refs
  const transferIdRef = useRef(null);
  const dateRef = useRef(null);
  const toBranchRef = useRef(null);
  const addressRef = useRef(null);
  const phoneRef = useRef(null);
  const itemRefs = useRef([]);
  const newItemProductSelectRef = useRef(null);
  const newItemQtyRef = useRef(null);
  const newItemUnitPriceRef = useRef(null);
  const newItemDiscountAmountRef = useRef(null);

  // State
  const [formData, setFormData] = useState(() => {
    const defaultState = {
      transfer: { id: "", date: format(new Date(), "yyyy-MM-dd") },
      branch: { id: null, name: "", address: "", phone: "" },
      items: [],
      id: null,
    };

    if (isEditMode && initialData) {
      return {
        ...defaultState,
        ...initialData,
        transfer: {
          ...defaultState.transfer,
          id: initialData.transfer_number || "",
          date: formatDate(initialData.transfer_date),
        },
        branch: {
          ...defaultState.branch,
          id: initialData.branch_id || null,
          name: initialData.branch?.branch_name || "",
          address: initialData.branch?.branch_address || "",
          phone: initialData.branch?.phone_number || "",
        },
        items: (initialData.items || []).map((item, idx) => ({
          ...item,
          id: item.id || Date.now() + idx,
          productId: item.product_id || null,
          qty: parseFloat(item.quantity || 1),
          unitPrice: parseFloat(item.unit_price || 0),
          salesPrice: parseFloat(item.sales_price || 0),
          discountAmount: parseFloat(item.discount_amount || 0),
          total: parseFloat(item.total || (item.quantity * item.unit_price - (item.discount_amount || 0))),
          mrp: parseFloat(item.mrp || 0),
          supplier: item.supplier || "",
          category: item.category || "",
          store_location: item.store_location || "",
        })),
        id: initialData.id || null,
      };
    }

    const savedDraft = JSON.parse(localStorage.getItem(draftKey) || "null");
    if (savedDraft) {
      return {
        ...defaultState,
        ...savedDraft,
        transfer: {
          ...defaultState.transfer,
          ...savedDraft.transfer,
          date: formatDate(savedDraft.transfer?.date),
        },
        items: (savedDraft.items || []).map((item, idx) => ({
          ...item,
          id: item.id || Date.now() + idx,
          productId: item.productId || null,
          qty: parseFloat(item.qty || 1),
          unitPrice: parseFloat(item.unitPrice || 0),
          salesPrice: parseFloat(item.salesPrice || 0),
          discountAmount: parseFloat(item.discountAmount || 0),
          total: parseFloat(item.total || (item.qty * item.unitPrice - (item.discountAmount || 0))),
          mrp: parseFloat(item.mrp || 0),
          supplier: item.supplier || "",
          category: item.category || "",
          store_location: item.store_location || "",
        })),
      };
    }

    return defaultState;
  });

  const [products, setProducts] = useState([]);
  const [branches, setBranches] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [newItem, setNewItem] = useState({
    productId: null,
    qty: 1,
    unitPrice: 0,
    discountAmount: 0,
    salesPrice: 0,
    mrp: 0,
  });

  // Clear form to default state
  const clearForm = () => {
    setFormData({
      transfer: { id: "", date: format(new Date(), "yyyy-MM-dd") },
      branch: { id: null, name: "", address: "", phone: "" },
      items: [],
      id: null,
    });
    setErrors({});
    setNewItem({ productId: null, qty: 1, unitPrice: 0, discountAmount: 0, salesPrice: 0, mrp: 0 });
    localStorage.removeItem(draftKey);
  };

  // Override onCancel to clear form and draft
  const handleCancel = () => {
    clearForm();
    if (onCancel) {
      onCancel();
    }
  };

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const today = format(new Date(), "yyyy-MM-dd");

        const endpoints = [
          { url: `${API_BASE_URL}/detailed-stock-reports`, params: { toDate: today }, name: "stock" },
          { url: `${API_BASE_URL}/products`, name: "products" },
          { url: `${API_BASE_URL}/branches`, name: "branches" },
        ];

        const responses = await Promise.all(
          endpoints.map((endpoint) =>
            axios
              .get(endpoint.url, {
                params: endpoint.params,
                headers: { Authorization: `Bearer ${currentUser.token}` },
              })
              .then((res) => ({ data: res.data, name: endpoint.name }))
              .catch((err) => {
                console.error(`Error fetching ${endpoint.url}:`, err);
                toast.error(`Failed to load ${endpoint.name} data`);
                return { data: null, name: endpoint.name };
              })
          )
        );

        const responseData = responses.reduce((acc, response) => {
          acc[response.name] = response.data;
          return acc;
        }, {});

        if (responseData.products?.data) {
          const productsWithStock = responseData.products.data.map((product) => ({
            ...product,
            product_id: product.product_id,
            category_name: product.category || "Unknown",
            // opening_stock_quantity: parseFloat(
            //   responseData.stock?.data?.find((stock) => stock.itemCode === product.item_code)?.closingStock ||
            //   product.opening_stock_quantity ||
            //   0
            // ),
            buying_cost: parseFloat(product.buying_cost || 0),
            mrp: parseFloat(product.mrp || 0),
            sales_price: parseFloat(product.sales_price || 0),
          }));
          setProducts(productsWithStock);
        }

        if (responseData.branches?.data) {
          setBranches(
            responseData.branches.data.map((branch) => ({
              id: branch.branch_id,
              name: branch.branch_name,
              address: branch.branch_address || "",
              phone: branch.phone_number || "",
            }))
          );
        }
      } catch (error) {
        console.error("Error in main fetch:", error);
        toast.error("Failed to load some data. Check console for details.");
      } finally {
        setLoading(false);
      }
    };

    if (currentUser?.token) {
      fetchData();
    }
  }, [currentUser?.token]);

  // Update item refs
  useEffect(() => {
    itemRefs.current = formData.items.map((_, index) => ({
      description: { current: null },
      qty: { current: null },
      unitPrice: { current: null },
      discountAmount: { current: null },
    }));
  }, [formData.items.length]);

  // Save draft to localStorage
  useEffect(() => {
    if (!isEditMode) {
      const { id, ...draftData } = formData;
      localStorage.setItem(draftKey, JSON.stringify(draftData));
    }
  }, [formData, isEditMode]);

  // Handle new item product selection
  const handleNewItemProductSelect = (selectedOption) => {
    const productId = selectedOption ? selectedOption.value : null;
    const product = products.find((p) => p.product_id === productId);
    setNewItem((prev) => ({
      ...prev,
      productId,
      unitPrice: product ? parseFloat(product.mrp) || 0 : 0,
      salesPrice: product ? parseFloat(product.sales_price) || 0 : 0,
      mrp: product ? parseFloat(product.mrp) || 0 : 0,
      discountAmount: 0,
      qty: 1,
    }));
    setErrors((prev) => ({ ...prev, newItemDescription: undefined }));
  };

  const handleNewItemInputChange = (e, field) => {
    const value = e.target.value;
    setNewItem((prev) => ({
      ...prev,
      [field]: field === "qty" || field === "unitPrice" || field === "discountAmount"
        ? value === "" ? "" : parseFloat(value) || 0
        : value,
    }));
    setErrors((prev) => ({
      ...prev,
      [`newItem${field.charAt(0).toUpperCase() + field.slice(1)}`]: undefined,
    }));
  };

  const handleAddNewItem = () => {
    const { productId, qty, unitPrice, discountAmount, salesPrice, mrp } = newItem;
    const newErrors = {};
    if (!productId) newErrors.newItemDescription = "Product is required";
    if (qty === "" || qty <= 0) newErrors.newItemQty = "Quantity must be positive";
    if (unitPrice === "" || unitPrice < 0) newErrors.newItemUnitPrice = "Unit price must be non-negative";
    if (discountAmount !== "" && discountAmount < 0)
      newErrors.newItemDiscountAmount = "Discount cannot be negative";
    const product = products.find((p) => p.product_id === productId);
    // if (product && parseFloat(qty) > parseFloat(product.opening_stock_quantity)) {
    //   newErrors.newItemQty = `Quantity exceeds available stock (${product.opening_stock_quantity})`;
    // }

    if (Object.keys(newErrors).length > 0) {
      setErrors((prev) => ({ ...prev, ...newErrors }));
      return;
    }

    setFormData((prev) => {
      const newItemToAdd = {
        id: Date.now(),
        productId,
        qty,
        unitPrice,
        salesPrice,
        discountAmount: discountAmount || 0,
        total: qty * unitPrice - (discountAmount || 0),
        mrp,
        description: product ? product.product_name : "",
        supplier: product ? product.supplier || "" : "",
        category: product ? product.category || "" : "",
        store_location: product ? product.store_location || "" : "",
      };

      return {
        ...prev,
        items: [...prev.items, newItemToAdd],
      };
    });

    setNewItem({ productId: null, qty: 1, unitPrice: 0, discountAmount: 0, salesPrice: 0, mrp: 0 });
    setErrors((prev) => ({
      ...prev,
      newItemDescription: undefined,
      newItemQty: undefined,
      newItemUnitPrice: undefined,
      newItemDiscountAmount: undefined,
    }));
    newItemProductSelectRef.current?.focus();
  };

  const handleInputChange = (e, section, field, index = null) => {
    const { name, value } = e.target;
    const targetName = name || field;
    let processedValue = value;

    setFormData((prev) => {
      const newData = { ...prev };
      if (index !== null && section === "items") {
        const newItems = [...newData.items];
        if (targetName === "qty" || targetName === "unitPrice" || targetName === "discountAmount") {
          processedValue = value === "" ? "" : parseFloat(value) || 0;
          newItems[index] = {
            ...newItems[index],
            [targetName]: processedValue,
            total:
              targetName === "qty" || targetName === "unitPrice"
                ? processedValue * (targetName === "qty" ? newItems[index].unitPrice : newItems[index].qty) -
                  (newItems[index].discountAmount || 0)
                : newItems[index].qty * newItems[index].unitPrice - processedValue,
          };
        } else {
          newItems[index] = { ...newItems[index], [targetName]: processedValue };
        }
        newData.items = newItems;
      } else {
        newData[section] = { ...newData[section], [targetName]: processedValue };
      }
      return newData;
    });

    setErrors((prev) => ({
      ...prev,
      [`${section === "items" ? `item${targetName.charAt(0).toUpperCase() + targetName.slice(1)}${index}` : `${section}${targetName.charAt(0).toUpperCase() + targetName.slice(1)}`}`]: undefined,
      items: undefined,
    }));
  };

  const handleBranchSelect = (selectedOption) => {
    if (!selectedOption) {
      setFormData((prev) => ({
        ...prev,
        branch: { id: null, name: "", address: "", phone: "" },
      }));
      setErrors((prev) => ({ ...prev, branchName: undefined }));
      return;
    }
    const branch = branches.find((b) => b.id === selectedOption.value);
    setFormData((prev) => ({
      ...prev,
      branch: {
        id: branch ? branch.id : null,
        name: branch ? branch.name || "" : "",
        address: branch ? branch.address || "" : "",
        phone: branch ? branch.phone || "" : "",
      },
    }));
    setErrors((prev) => ({ ...prev, branchName: undefined }));
    setTimeout(() => addressRef.current?.focus(), 0);
  };

  const handleProductSelect = async (selectedOption, index) => {
    const productId = selectedOption ? selectedOption.value : null;
    const product = products.find((p) => p.product_id === productId);
    setFormData((prev) => {
      const newItems = [...prev.items];
      if (!newItems[index]) return prev;
      const qty = parseFloat(newItems[index].qty) || 1;
      newItems[index] = {
        ...newItems[index],
        productId: product ? product.product_id : null,
        description: product ? product.product_name : "",
        unitPrice: product ? parseFloat(product.mrp) || 0 : 0,
        salesPrice: product ? parseFloat(product.sales_price) || 0 : 0,
        mrp: product ? parseFloat(product.mrp) || 0 : 0,
        discountAmount: 0,
        total: qty * (product ? parseFloat(product.mrp) || 0 : 0),
        supplier: product ? product.supplier || "" : "",
        category: product ? product.category || "" : "",
        store_location: product ? product.store_location || "" : "",
      };
      return { ...prev, items: newItems };
    });

    setErrors((prev) => ({ ...prev, [`itemDescription${index}`]: undefined }));
    setTimeout(() => itemRefs.current[index]?.qty?.current?.focus(), 0);
  };

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          id: Date.now(),
          description: "",
          qty: 1,
          unitPrice: 0,
          salesPrice: 0,
          discountAmount: 0,
          total: 0,
          productId: null,
          mrp: 0,
          supplier: "",
          category: "",
          store_location: "",
        },
      ],
    }));
    setTimeout(() => itemRefs.current[formData.items.length]?.description?.current?.focus(), 0);
  };

  const removeItem = (index) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const calculateSubtotal = useCallback(() => {
    return formData.items.reduce((sum, item) => {
      const qty = parseFloat(item.qty) || 0;
      const unitPrice = parseFloat(item.unitPrice) || 0;
      return sum + qty * unitPrice;
    }, 0);
  }, [formData.items]);

  const calculateTotalDiscount = useCallback(() => {
    return formData.items.reduce((sum, item) => {
      return sum + (parseFloat(item.discountAmount) || 0);
    }, 0);
  }, [formData.items]);

  const calculateTotal = useCallback(() => {
    return calculateSubtotal() - calculateTotalDiscount();
  }, [calculateSubtotal, calculateTotalDiscount]);

  const validateForm = () => {
    const newErrors = {};
    const { transfer, branch, items } = formData;

    if (!transfer.date) newErrors.transferDate = "Date is required";
    if (!branch.id) newErrors.branchName = "Branch is required";
    if (!items.length) newErrors.items = "At least one item is required";

    items.forEach((item, idx) => {
      if (!item.productId) newErrors[`itemDescription${idx}`] = "Product is required";
      if (item.qty === "" || parseFloat(item.qty) <= 0)
        newErrors[`itemQty${idx}`] = "Quantity must be positive";
      if (item.unitPrice === "" || parseFloat(item.unitPrice) < 0)
        newErrors[`itemUnitPrice${idx}`] = "Unit price must be non-negative";
      if (item.discountAmount !== "" && parseFloat(item.discountAmount) < 0)
        newErrors[`itemDiscountAmount${idx}`] = "Discount cannot be negative";
      if (item.salesPrice === "" || parseFloat(item.salesPrice) < 0)
        newErrors[`itemSalesPrice${idx}`] = "Sales price must be non-negative";
      if (item.mrp === "" || parseFloat(item.mrp) < 0)
        newErrors[`itemMrp${idx}`] = "MRP must be non-negative";
      const product = products.find((p) => p.product_id === item.productId);
    //   if (product && parseFloat(item.qty) > parseFloat(product.opening_stock_quantity)) {
    //     newErrors[`itemQty${idx}`] = `Quantity exceeds available stock (${product.opening_stock_quantity})`;
    //   }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (!validateForm()) {
        toast.warn("Please fix validation errors.");
        return;
      }

      let transferId = formData.transfer.id;
      if (!transferId || transferId.trim() === "") {
        const lastIndexStr = localStorage.getItem("lastTransferIndex");
        let lastIndex = lastIndexStr ? parseInt(lastIndexStr, 10) : 0;
        lastIndex += 1;
        localStorage.setItem("lastTransferIndex", lastIndex.toString());
        transferId = `TRF-${lastIndex.toString().padStart(3, "0")}`;
        setFormData((prev) => ({
          ...prev,
          transfer: { ...prev.transfer, id: transferId },
        }));
      }

      if (!transferId || !formData.transfer.date || !formData.items.length) {
        toast.error("Required fields are missing.");
        return;
      }

      setLoading(true);
      const payload = {
        transfer: {
          id: transferId,
          date: formData.transfer.date,
        },
        branch: {
          id: formData.branch.id,
        },
        items: formData.items.map((item) => ({
          id: isEditMode && item.id ? item.id : undefined,
          product_id: item.productId,
          description: item.description || "Item",
          quantity: parseFloat(item.qty) || 0,
          unit_price: parseFloat(item.unitPrice) || 0,
          sales_price: parseFloat(item.salesPrice) || 0,
          discount_amount: parseFloat(item.discountAmount) || 0,
          total: parseFloat(item.total) || 0,
          supplier: item.supplier || null,
          category: item.category || null,
          store_location: item.store_location || null,
          mrp: parseFloat(item.mrp) || 0,
        })),
      };

      try {
        if (isEditMode) {
          if (typeof onUpdateTransfer !== 'function') {
            console.warn('onUpdateTransfer is not a function, using direct API call');
            await axios.put(`${API_BASE_URL}/stock-transfers/${formData.id}`, payload, {
              headers: { Authorization: `Bearer ${currentUser.token}` },
            });
          } else {
            await onUpdateTransfer(payload, formData.id);
          }
          toast.success("Stock transfer updated successfully!");
          localStorage.removeItem(draftKey);
          clearForm();
        } else {
          if (typeof onCreateTransfer !== 'function') {
            console.warn('onCreateTransfer is not a function, using direct API call');
            await axios.post(`${API_BASE_URL}/stock-transfers`, payload, {
              headers: { Authorization: `Bearer ${currentUser.token}` },
            });
          } else {
            await onCreateTransfer(payload);
          }
          toast.success("Stock transfer created successfully!");
          localStorage.removeItem(draftKey);
          clearForm();
        }
      } catch (error) {
        const message = error.response?.data?.message || "Failed to save stock transfer.";
        const errors = error.response?.data?.errors || {};
        let errorDetails = "";
        if (Object.keys(errors).length > 0) {
          errorDetails = Object.entries(errors)
            .map(([field, messages]) => `${field}: ${messages.join(", ")}`)
            .join("\n");
        } else {
          errorDetails = error.message || "No detailed errors provided.";
        }
        console.error("API Error Details:", { message, errors, response: error.response?.data, fullError: error });
        toast.error(`${message}\n${errorDetails}`);
        setErrors((prev) => ({
          ...prev,
          ...Object.entries(errors).reduce((acc, [key, value]) => {
            if (key.startsWith("items.")) {
              const [_, index, field] = key.split(".");
              acc[`item${field.charAt(0).toUpperCase() + field.slice(1)}${index}`] = value.join(", ");
            } else if (key.startsWith("transfer.")) {
              const field = key.split(".")[1];
              acc[`transfer${field.charAt(0).toUpperCase() + field.slice(1)}`] = value.join(", ");
            } else if (key.startsWith("branch.")) {
              const field = key.split(".")[1];
              acc[`branch${field.charAt(0).toUpperCase() + field.slice(1)}`] = value.join(", ");
            } else {
              acc[key] = value.join(", ");
            }
            return acc;
          }, {}),
        }));
      } finally {
        setLoading(false);
      }
    },
    [formData, isEditMode, onCreateTransfer, onUpdateTransfer, currentUser?.token]
  );

  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        handleSubmit(e);
      } else if (e.key === "Escape") {
        e.preventDefault();
        handleCancel();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleSubmit, handleCancel]);

  const getFieldOrder = useCallback(() => {
    const fields = [
      { ref: transferIdRef, name: "transferId", type: "input" },
      { ref: dateRef, name: "transferDate", type: "input" },
      { ref: toBranchRef, name: "branchName", type: "select" },
      { ref: addressRef, name: "branchAddress", type: "input" },
      { ref: phoneRef, name: "branchPhone", type: "input" },
      { ref: newItemProductSelectRef, name: "newItemDescription", type: "select" },
      { ref: newItemQtyRef, name: "newItemQty", type: "input" },
      { ref: newItemUnitPriceRef, name: "newItemUnitPrice", type: "input" },
      { ref: newItemDiscountAmountRef, name: "newItemDiscountAmount", type: "input" },
    ];
    formData.items.forEach((_, index) => {
      fields.push(
        { ref: itemRefs.current[index]?.description, name: `itemDescription${index}`, type: "select", index },
        { ref: itemRefs.current[index]?.qty, name: `itemQty${index}`, type: "input", index },
        { ref: itemRefs.current[index]?.unitPrice, name: `itemUnitPrice${index}`, type: "input", index },
        { ref: itemRefs.current[index]?.discountAmount, name: `itemDiscountAmount${index}`, type: "input", index }
      );
    });
    return fields;
  }, [formData.items]);

  const handleEnterKey = (e, currentRef, itemIndex = null) => {
    if (e.key !== "Enter" || e.shiftKey) return;
    e.preventDefault();

    const fields = getFieldOrder();
    const currentFieldIndex = fields.findIndex((field) => field.ref?.current === currentRef.current);
    if (currentFieldIndex === -1) return;

    const currentField = fields[currentFieldIndex];
    if (currentField.type === "select" && currentField.name === "branchName" && !formData.branch.id) return;
    if (currentField.type === "select" && currentField.name.startsWith("itemDescription") && !formData.items[itemIndex]?.productId) return;
    if (currentField.name === "newItemDiscountAmount") {
      handleAddNewItem();
      return;
    }

    for (let i = currentFieldIndex + 1; i < fields.length; i++) {
      const nextField = fields[i];
      if (nextField.ref?.current) {
        nextField.ref.current.focus();
        if (nextField.type === "input") nextField.ref.current.select?.();
        else if (nextField.type === "select") nextField.ref.current?.openMenu?.();
        break;
      }
    }
  };

  const branchOptions = useMemo(() => {
    return branches.map((b) => ({
      value: b.id,
      label: b.name || "Unknown Branch",
    }));
  }, [branches]);

  const filteredProductOptions = useMemo(() => {
    return products
      .filter(
        (product) =>
          product.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .map((p) => ({
        value: p.product_id,
        label: `${p.product_name} (${p.description || "No description"})`,
        description: `${p.product_name}, Category: ${p.category_name || "Unknown"}`,
        stock: p.opening_stock_quantity,
        mrp: p.mrp,
        salesPrice: p.sales_price,
      }));
  }, [products, searchTerm]);

  const subtotal = useMemo(() => calculateSubtotal(), [calculateSubtotal]);
  const totalDiscount = useMemo(() => calculateTotalDiscount(), [calculateTotalDiscount]);
  const total = useMemo(() => calculateTotal(), [calculateTotal]);

  const assignRef = (index, field, element) => {
    if (!itemRefs.current[index]) itemRefs.current[index] = {};
    itemRefs.current[index][field] = { current: element };
  };

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-60 backdrop-blur-sm">
        <div className="relative flex flex-col w-full h-full overflow-y-auto bg-white dark:bg-gray-800">
          <h3 className="p-6 text-2xl font-bold text-blue-600 border-b border-gray-200">
            {isEditMode ? "Edit Stock Transfer" : "Create Stock Transfer"}
          </h3>
          <form
            id="stockTransferForm"
            onSubmit={handleSubmit}
            noValidate
            className="w-full h-full p-6 overflow-y-auto bg-gray-50"
          >
            <div className="p-4 mb-6 bg-white border rounded-lg shadow-sm">
              <h4 className="pb-2 mb-3 text-lg font-semibold border-b">
                Transfer Details
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label htmlFor="transferId" className="block mb-1 text-sm font-medium">
                    Transfer ID
                  </label>
                  <input
                    id="transferId"
                    ref={transferIdRef}
                    type="text"
                    value={formData.transfer.id}
                    onChange={(e) => handleInputChange(e, "transfer", "id")}
                    onKeyDown={(e) => handleEnterKey(e, transferIdRef)}
                    className={`w-full p-2.5 border rounded-md focus:outline-none ${
                      errors.transferId ? "border-red-500" : "border-gray-300 focus:border-blue-500"
                    }`}
                    placeholder="TRF-001"
                    readOnly
                  />
                  {errors.transferId && (
                    <p className="mt-1 text-xs text-red-600">{errors.transferId}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="date" className="block mb-1 text-sm font-medium">
                    Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="date"
                    ref={dateRef}
                    type="date"
                    value={formData.transfer.date}
                    onChange={(e) => handleInputChange(e, "transfer", "date")}
                    onKeyDown={(e) => handleEnterKey(e, dateRef)}
                    className={`w-full p-2.5 border rounded-md focus:outline-none ${
                      errors.transferDate ? "border-red-500" : "border-gray-300 focus:border-blue-500"
                    }`}
                    required
                  />
                  {errors.transferDate && (
                    <p className="mt-1 text-xs text-red-600">{errors.transferDate}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="p-4 mb-6 bg-white border rounded-lg shadow-sm">
              <h4 className="pb-2 mb-3 text-lg font-semibold border-b">
                Branch Information
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                <div>
                  <label htmlFor="toBranch" className="block mb-1 text-sm font-medium">
                    To Branch <span className="text-red-500">*</span>
                  </label>
                  <Select
                    inputId="toBranch"
                    ref={toBranchRef}
                    options={branchOptions}
                    value={branchOptions.find((option) => option.value === formData.branch.id) || null}
                    onChange={handleBranchSelect}
                    placeholder="Select branch"
                    isClearable
                    isSearchable
                    styles={unifiedSelectStyles(!!errors.branchName)}
                    onKeyDown={(e) => handleEnterKey(e, toBranchRef)}
                  />
                  {errors.branchName && (
                    <p className="mt-1 text-xs text-red-600">{errors.branchName}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="address" className="block mb-1 text-sm font-medium">
                    Address
                  </label>
                  <input
                    id="address"
                    ref={addressRef}
                    type="text"
                    value={formData.branch.address}
                    onChange={(e) => handleInputChange(e, "branch", "address")}
                    onKeyDown={(e) => handleEnterKey(e, addressRef)}
                    className="w-full p-2.5 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                    placeholder="Branch address"
                    readOnly
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block mb-1 text-sm font-medium">
                    Phone
                  </label>
                  <input
                    id="phone"
                    ref={phoneRef}
                    type="tel"
                    value={formData.branch.phone}
                    onChange={(e) => handleInputChange(e, "branch", "phone")}
                    onKeyDown={(e) => handleEnterKey(e, phoneRef)}
                    className="w-full p-2.5 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                    placeholder="+94 ************"
                    readOnly
                  />
                </div>
              </div>
            </div>

            <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <h4 className="pb-2 mb-4 text-lg font-semibold text-gray-800 border-b border-gray-200">
                Add New Item
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-12">
                <div className="md:col-span-5">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    Product <span className="text-red-500">*</span>
                  </label>
                  <Select
                    ref={newItemProductSelectRef}
                    options={filteredProductOptions}
                    value={
                      newItem.productId
                        ? filteredProductOptions.find((option) => option.value === newItem.productId) || null
                        : null
                    }
                    placeholder="Search or select product"
                    isClearable
                    isSearchable
                    onChange={handleNewItemProductSelect}
                    onInputChange={(value) => setSearchTerm(value)}
                    styles={unifiedSelectStyles(!!errors.newItemDescription)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && newItem.productId) {
                        e.preventDefault();
                        newItemQtyRef.current?.focus();
                        newItemQtyRef.current?.select();
                      }
                    }}
                  />
                  {errors.newItemDescription && (
                    <p className="mt-1 text-xs text-red-600">{errors.newItemDescription}</p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    Quantity <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="0.01"
                    step="0.01"
                    ref={newItemQtyRef}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.newItemQty ? "border-red-500" : "border-gray-300"
                    }`}
                    value={newItem.qty}
                    onChange={(e) => handleNewItemInputChange(e, "qty")}
                    onKeyDown={(e) => handleEnterKey(e, newItemQtyRef)}
                  />
                  {errors.newItemQty && (
                    <p className="mt-1 text-xs text-red-600">{errors.newItemQty}</p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    MRP <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    ref={newItemUnitPriceRef}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.newItemUnitPrice ? "border-red-500" : "border-gray-300"
                    }`}
                    value={newItem.unitPrice}
                    onChange={(e) => handleNewItemInputChange(e, "unitPrice")}
                    onKeyDown={(e) => handleEnterKey(e, newItemUnitPriceRef)}
                  />
                  {errors.newItemUnitPrice && (
                    <p className="mt-1 text-xs text-red-600">{errors.newItemUnitPrice}</p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    Discount
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    ref={newItemDiscountAmountRef}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.newItemDiscountAmount ? "border-red-500" : "border-gray-300"
                    }`}
                    value={newItem.discountAmount}
                    onChange={(e) => handleNewItemInputChange(e, "discountAmount")}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        handleAddNewItem();
                      }
                    }}
                  />
                  {errors.newItemDiscountAmount && (
                    <p className="mt-1 text-xs text-red-600">{errors.newItemDiscountAmount}</p>
                  )}
                </div>
                <div className="flex items-end md:col-span-1">
                  <button
                    type="button"
                    onClick={handleAddNewItem}
                    className="w-full h-[42px] px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    Add
                  </button>
                </div>
              </div>
            </div>

            <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-800">
                  Transfer Items
                </h4>
                <div className="text-sm text-gray-500">
                  {formData.items.length} item(s)
                </div>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                        Item
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                        Qty
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                        MRP
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                        Selling Price
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                        Discount
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                        Total
                      </th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {formData.items.map((item, index) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Select
                            ref={(el) => assignRef(index, "description", el)}
                            options={filteredProductOptions}
                            value={
                              item.productId
                                ? filteredProductOptions.find((option) => option.value === item.productId) || null
                                : null
                            }
                            onChange={(option) => handleProductSelect(option, index)}
                            placeholder="Select item"
                            isClearable
                            isSearchable
                            styles={unifiedSelectStyles(!!errors[`itemDescription${index}`])}
                            onKeyDown={(e) => handleEnterKey(e, itemRefs.current[index]?.description, index)}
                          />
                          {errors[`itemDescription${index}`] && (
                            <p className="mt-1 text-xs text-red-600">{errors[`itemDescription${index}`]}</p>
                          )}
                        </td>
                        <td className="px-6 py-4 text-right whitespace-nowrap">
                          <input
                            type="number"
                            min="0.01"
                            step="0.01"
                            ref={(el) => assignRef(index, "qty", el)}
                            value={item.qty}
                            onChange={(e) => handleInputChange(e, "items", "qty", index)}
                            onKeyDown={(e) => handleEnterKey(e, itemRefs.current[index]?.qty, index)}
                            className={`w-20 p-1 text-right border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                              errors[`itemQty${index}`] ? "border-red-500" : "border-gray-300"
                            }`}
                          />
                          {errors[`itemQty${index}`] && (
                            <p className arylassName="mt-1 text-xs text-red-600">{errors[`itemQty${index}`]}</p>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-right text-gray-500 whitespace-nowrap">
                          {formatCurrency(item.unitPrice)}
                        </td>
                        <td className="px-6 py-4 text-sm text-right text-gray-500 whitespace-nowrap">
                          {formatCurrency(item.salesPrice)}
                        </td>
                        <td className="py-4 text-right px 6 whitespace-nowrap">
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            ref={(el) => assignRef(index, "discountAmount", el)}
                            value={item.discountAmount}
                            onChange={(e) => handleInputChange(e, "items", "discountAmount", index)}
                            onKeyDown={(e) => handleEnterKey(e, itemRefs.current[index]?.discountAmount, index)}
                            className={`w-24 p-1 text-right border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                              errors[`itemDiscountAmount${index}`] ? "border-red-500" : "border-gray-300"
                            }`}
                          />
                          {errors[`itemDiscountAmount${index}`] && (
                            <p className="mt-1 text-xs text-red-600">{errors[`itemDiscountAmount${index}`]}</p>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                          {formatCurrency(item.total)}
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                          <button
                            type="button"
                            onClick={() => removeItem(index)}
                            className="p-1 text-red-600 rounded-md hover:text-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {errors.items && (
                <p className="mt-2 text-xs text-red-600">{errors.items}</p>
              )}
            </div>

            <div className="p-3 bg-white border border-gray-200 rounded-lg shadow-sm">
              <h4 className="pb-2 mb-3 text-lg font-semibold text-gray-800 border-b border-gray-200">
                Transfer Summary
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Subtotal:</span>
                  <span className="text-sm font-medium">{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Discount:</span>
                  <span className="text-sm font-medium">{formatCurrency(totalDiscount)}</span>
                </div>
                <div className="flex justify-between pt-3 border-t border-gray-200">
                  <span className="text-base font-semibold">Total:</span>
                  <span className="text-base font-semibold">{formatCurrency(total)}</span>
                </div>
              </div>
            </div>

            <div className="sticky bottom-0 py-4 mt-6 -mx-6 bg-white border-t border-gray-200 rounded-b-xl">
              <div className="flex justify-end px-6 space-x-4">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring focus:ring-blue-500 focus:ring-offset-2"
                >
                  Cancel (Esc)
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${loading ? "opacity-70 cursor-not-allowed" : ""}`}
                >
                  {loading ? (
                    <span className="flex items-center justify-center">
                      <svg
                        className="w-4 h-4 mr-2 animate-spin"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Processing...
                    </span>
                  ) : isEditMode ? (
                    "Update Transfer (Ctrl+S)"
                  ) : (
                    "Save Transfer (Ctrl+S)"
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default StockTransferForm;