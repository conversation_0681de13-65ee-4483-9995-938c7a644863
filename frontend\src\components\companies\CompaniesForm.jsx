import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import { Edit, Trash } from "lucide-react";
import Notification from "../notification/Notification";

const CompanyForm = ({ company, onSuccess }) => {
  const [companyName, setCompanyName] = useState("");
  const [contact, setContact] = useState("");
  const [address, setAddress] = useState("");
  const [openingBalance, setOpeningBalance] = useState("");

  // Refs for Enter key navigation
  const companyNameRef = useRef(null);
  const contactRef = useRef(null);
  const addressRef = useRef(null);
  const openingBalanceRef = useRef(null);
  const submitButtonRef = useRef(null);

  useEffect(() => {
    if (company) {
      setCompanyName(company.company_name || "");
      setContact(company.contact || "");
      setAddress(company.address || "");
      setOpeningBalance(company.opening_balance ?? "");
    } else {
      setCompanyName("");
      setContact("");
      setAddress("");
      setOpeningBalance("");
    }
    // Auto-focus on company name field
    setTimeout(() => {
      if (companyNameRef.current) {
        companyNameRef.current.focus();
      }
    }, 100);
  }, [company]);

  // Handle Enter key navigation
  const handleKeyDown = (e, nextFieldRef) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (nextFieldRef && nextFieldRef.current) {
        nextFieldRef.current.focus();
      } else {
        // If no next field, submit the form
        handleSubmit(e);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const data = {
        company_name: companyName,
        contact,
        address,
        opening_balance: openingBalance ? parseFloat(openingBalance) : null,
      };
      if (company) {
        await axios.put(
          `http://127.0.0.1:8000/api/add-companies/${company.id}`,
          data
        );
        onSuccess("Company updated successfully!", "success");
      } else {
        await axios.post("http://127.0.0.1:8000/api/add-companies", data);
        onSuccess("Company added successfully!", "success");
      }
      setCompanyName("");
      setContact("");
      setAddress("");
      setOpeningBalance("");
      // Auto-focus back to first field after successful submission
      setTimeout(() => {
        if (companyNameRef.current) {
          companyNameRef.current.focus();
        }
      }, 100);
    } catch (error) {
      console.error("Error saving company:", error);
      onSuccess("Error saving company!", "error");
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="max-w-md p-6 mx-auto mb-6 transition-transform duration-300 ease-in-out transform bg-white rounded-lg shadow-lg hover:scale-105"
    >
      <h2 className="mb-4 text-2xl font-bold text-center text-amber-600">
        {company ? "Update" : "Add"} Company
      </h2>
      <div className="mb-4">
        <input
          ref={companyNameRef}
          type="text"
          value={companyName}
          onChange={(e) => setCompanyName(e.target.value)}
          onKeyDown={(e) => handleKeyDown(e, contactRef)}
          placeholder="Company Name"
          required
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="mb-4">
        <input
          ref={contactRef}
          type="text"
          value={contact}
          onChange={(e) => setContact(e.target.value)}
          onKeyDown={(e) => handleKeyDown(e, addressRef)}
          placeholder="Contact"
          required
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="mb-4">
        <input
          ref={addressRef}
          type="text"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          onKeyDown={(e) => handleKeyDown(e, openingBalanceRef)}
          placeholder="Address"
          required
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="mb-4">
        <input
          ref={openingBalanceRef}
          type="number"
          value={openingBalance}
          onChange={(e) => setOpeningBalance(e.target.value)}
          onKeyDown={(e) => handleKeyDown(e, submitButtonRef)}
          placeholder="Opening Balance"
          step="0.01"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <button
        ref={submitButtonRef}
        type="submit"
        className="w-full py-2 font-semibold text-white transition duration-200 ease-in-out transform bg-blue-600 rounded-lg hover:bg-blue-700 hover:scale-105"
      >
        {company ? "Update" : "Add"} Company
      </button>
    </form>
  );
};

const CompanyTable = ({ companies, onEdit, onDelete }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border border-collapse">
        <thead className="bg-gray-500">
          <tr className="text-left">
            <th className="px-4 py-2 border-b-2 border-gray-300">S.No</th>
            <th className="px-4 py-2 border-b-2 border-gray-300">
              Company Name
            </th>
            <th className="px-4 py-2 border-b-2 border-gray-300">Contact</th>
            <th className="px-4 py-2 border-b-2 border-gray-300">Address</th>
            <th className="px-4 py-2 border-b-2 border-gray-300">
              Opening Balance
            </th>
            <th className="px-4 py-2 border-b-2 border-gray-300">Actions</th>
          </tr>
        </thead>
        <tbody className="text-center">
          {companies.map((company, index) => (
            <tr key={company.id} className="hover:bg-gray-100">
              <td className="px-4 py-2 border-b border-gray-300">
                {index + 1}
              </td>
              <td className="px-4 py-2 border-b border-gray-300">
                {company.company_name}
              </td>
              <td className="px-4 py-2 border-b border-gray-300">
                {company.contact}
              </td>
              <td className="px-4 py-2 border-b border-gray-300">
                {company.address}
              </td>
              <td className="px-4 py-2 border-b border-gray-300">
                {company.opening_balance !== null
                  ? company.opening_balance
                  : "N/A"}
              </td>
              <td className="px-4 py-2 border-b border-gray-300">
                <div className="flex justify-center space-x-2">
                  <button
                    onClick={() => onEdit(company)}
                    className="px-2 py-1 text-blue-600 bg-blue-200 rounded-lg hover:bg-blue-300"
                    title="Edit Company"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => onDelete(company.id)}
                    className="px-2 py-1 text-red-600 bg-red-200 rounded-lg hover:bg-red-300"
                    title="Delete Company"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const CompanyManagement = () => {
  const [companies, setCompanies] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [notification, setNotification] = useState({
    message: "",
    type: "",
    visible: false,
    onConfirm: null,
  });

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/add-companies");
      setCompanies(response.data);
    } catch (error) {
      console.error("Error fetching companies:", error);
      showNotification("Error fetching companies!", "error");
    }
  };

  const handleEdit = (company) => {
    setSelectedCompany(company);
  };

  const handleDelete = (id) => {
    setNotification({
      message: "Are you sure you want to delete this company?",
      type: "confirm",
      visible: true,
      onConfirm: () => confirmDelete(id),
    });
  };

  const confirmDelete = async (id) => {
    try {
      await axios.delete(`http://127.0.0.1:8000/api/companies/${id}`);
      fetchCompanies();
      showNotification("Company deleted successfully!", "success");
    } catch (error) {
      console.error("Error deleting company:", error);
      showNotification("Error deleting company!", "error");
    }
  };

  const handleSuccess = (message, type) => {
    setSelectedCompany(null);
    fetchCompanies();
    showNotification(message, type);
  };

  const showNotification = (message, type) => {
    setNotification({ message, type, visible: true, onConfirm: null });
    setTimeout(
      () => setNotification({ ...notification, visible: false }),
      3000
    );
  };

  return (
    <div className="container p-4 mx-auto">
      {notification.visible && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification({ ...notification, visible: false })}
        >
          {notification.type === "confirm" && (
            <div className="flex justify-center mt-4 space-x-4">
              <button
                onClick={() => {
                  notification.onConfirm();
                  setNotification({ ...notification, visible: false });
                }}
                className="px-4 py-2 text-white bg-red-600 rounded-lg"
              >
                Yes, Delete
              </button>
              <button
                onClick={() =>
                  setNotification({ ...notification, visible: false })
                }
                className="px-4 py-2 text-white bg-gray-400 rounded-lg"
              >
                Cancel
              </button>
            </div>
          )}
        </Notification>
      )}
      <div className="flex flex-col md:flex-row md:space-x-4">
        <CompanyForm company={selectedCompany} onSuccess={handleSuccess} />
        <CompanyTable
          companies={companies}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>
    </div>
  );
};

export default CompanyManagement;
