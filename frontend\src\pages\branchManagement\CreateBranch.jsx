import React, { useState, useEffect, useRef } from "react";
import { FiPlus, FiSearch, FiEdit, FiTrash2, <PERSON>Eye, FiUsers, FiToggleLeft, FiToggleRight, FiUserMinus } from "react-icons/fi";
import { FaBuilding } from "react-icons/fa";
import axios from "axios";
import { useAuth } from "../../context/NewAuthContext";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { motion, AnimatePresence } from "framer-motion";

const API_BASE_URL = "http://127.0.0.1:8000/api";

const CreateBranch = () => {
  const [showForm, setShowForm] = useState(false);
  const [branches, setBranches] = useState([]);
  const [formData, setFormData] = useState({
    branchName: "",
    branchAddress: "",
    phoneNumber: "",
    email: "",
  });
  const [errors, setErrors] = useState({});
  const [filterType, setFilterType] = useState("branch_id");
  const [searchQuery, setSearchQuery] = useState("");
  const [editId, setEditId] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  
  // Branch Users Management States
  const [showBranchUsersModal, setShowBranchUsersModal] = useState(false);
  const [selectedBranchForUsers, setSelectedBranchForUsers] = useState(null);
  const [branchUsers, setBranchUsers] = useState([]);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [showAddUserForm, setShowAddUserForm] = useState(false);
  const [newUserData, setNewUserData] = useState({
    user_id: "",
    role: "",
    permissions: [],
  });
  const [loadingBranchUsers, setLoadingBranchUsers] = useState(false);
  const [loadingUserOperation, setLoadingUserOperation] = useState(false);
  const [showInactiveUsers, setShowInactiveUsers] = useState(true);
  const [operatingUserId, setOperatingUserId] = useState(null);
  
  const { user: currentUser } = useAuth();

  // Refs for form inputs and buttons
  const branchNameRef = useRef(null);
  const branchAddressRef = useRef(null);
  const phoneNumberRef = useRef(null);
  const emailRef = useRef(null);
  const submitButtonRef = useRef(null);
  const cancelButtonRef = useRef(null);

  // Fetch branches
  const fetchBranches = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/branches`, {
        headers: { Authorization: `Bearer ${currentUser.token}` },
      });
      setBranches(Array.isArray(response.data?.data) ? response.data.data : []);
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to fetch branches";
      toast.error(errorMessage);
    }
  };

  useEffect(() => {
    if (currentUser?.token) {
      fetchBranches();
    }
  }, [currentUser?.token]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error for the field when user starts typing
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({}); // Clear previous errors

    // Frontend validation
    const newErrors = {};
    if (!formData.branchName) newErrors.branchName = "Branch Name is required";
    if (formData.branchName.length < 2)
      newErrors.branchName = "Branch Name must be at least 2 characters";
    if (!formData.branchAddress)
      newErrors.branchAddress = "Branch Address is required";
    if (!formData.phoneNumber)
      newErrors.phoneNumber = "Phone Number is required";
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))
      newErrors.email = "Invalid email format";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      const payload = {
        branchName: formData.branchName,
        branchAddress: formData.branchAddress,
        phoneNumber: formData.phoneNumber,
        email: formData.email || null,
      };

      if (editId) {
        const response = await axios.put(
          `${API_BASE_URL}/branches/${editId}`,
          payload,
          {
            headers: { Authorization: `Bearer ${currentUser.token}` },
          }
        );
        setBranches((prev) =>
          prev.map((branch) =>
            branch.branch_id === editId ? response.data.data : branch
          )
        );
        toast.success("Branch updated successfully");
      } else {
        const response = await axios.post(
          `${API_BASE_URL}/branches`,
          payload,
          {
            headers: { Authorization: `Bearer ${currentUser.token}` },
          }
        );
        setBranches((prev) => [...prev, response.data.data]);
        toast.success("Branch created successfully");
      }

      setFormData({
        branchName: "",
        branchAddress: "",
        phoneNumber: "",
        email: "",
      });
      setShowForm(false);
      setEditId(null);
      setErrors({});
    } catch (error) {
      if (error.response?.status === 422) {
        // Handle validation errors from backend
        const backendErrors = error.response.data.errors || {};
        const newErrors = {};
        Object.keys(backendErrors).forEach((key) => {
          const fieldMap = {
            branch_name: "branchName",
            branchName: "branchName",
            branch_address: "branchAddress",
            branchAddress: "branchAddress",
            phone_number: "phoneNumber",
            phoneNumber: "phoneNumber",
            email: "email",
          };
          newErrors[fieldMap[key] || key] = backendErrors[key][0];
        });
        setErrors(newErrors);
      } else {
        const errorMessage =
          error.response?.data?.error ||
          error.response?.data?.message ||
          "Failed to save branch";
        toast.error(errorMessage);
      }
    }
  };

  const handleCancel = () => {
    setFormData({
      branchName: "",
      branchAddress: "",
      phoneNumber: "",
      email: "",
    });
    setErrors({});
    setShowForm(false);
    setEditId(null);
  };

  const handleEdit = (branch) => {
    setFormData({
      branchName: branch.branch_name,
      branchAddress: branch.branch_address,
      phoneNumber: branch.phone_number,
      email: branch.email || "",
    });
    setEditId(branch.branch_id);
    setShowForm(true);
    setErrors({});
  };

  const handleDelete = (branchId) => {
    setDeleteId(branchId);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (deleteId) {
      try {
        await axios.delete(`${API_BASE_URL}/branches/${deleteId}`, {
          headers: { Authorization: `Bearer ${currentUser.token}` },
        });
        setBranches((prev) =>
          prev.filter((branch) => branch.branch_id !== deleteId)
        );
        toast.success("Branch deleted successfully");
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "Failed to delete branch";
        toast.error(errorMessage);
      }
      setShowDeleteModal(false);
      setDeleteId(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDeleteId(null);
  };

  const handleView = (branch) => {
    setSelectedBranch(branch);
    setShowViewModal(true);
  };

  const closeViewModal = () => {
    setShowViewModal(false);
    setSelectedBranch(null);
  };

  // Branch Users Management Functions
  const handleManageBranchUsers = async (branch) => {
    setSelectedBranchForUsers(branch);
    setShowBranchUsersModal(true);
    await fetchBranchUsers(branch.branch_id);
    await fetchAvailableUsers(branch.branch_id);
  };

  const fetchBranchUsers = async (branchId) => {
    try {
      setLoadingBranchUsers(true);
      const response = await axios.get(`${API_BASE_URL}/branches/${branchId}/users`, {
        headers: { Authorization: `Bearer ${currentUser.token}` },
      });
      setBranchUsers(Array.isArray(response.data?.data) ? response.data.data : []);
    } catch (error) {
      toast.error(`Failed to fetch branch users: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoadingBranchUsers(false);
    }
  };

  const fetchAvailableUsers = async (branchId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/branches/${branchId}/available-users`, {
        headers: { Authorization: `Bearer ${currentUser.token}` },
      });
      setAvailableUsers(Array.isArray(response.data?.data) ? response.data.data : []);
    } catch (error) {
      toast.error(`Failed to fetch available users: ${error.response?.data?.message || error.message}`);
    }
  };

  const handleAddUserToBranch = async () => {
    if (!newUserData.user_id || !newUserData.role) {
      toast.error("Please select a user and enter a role");
      return;
    }

    try {
      setLoadingUserOperation(true);
      const response = await axios.post(`${API_BASE_URL}/branches/${selectedBranchForUsers.branch_id}/users`, newUserData, {
        headers: { Authorization: `Bearer ${currentUser.token}` },
      });
      
      const addedUser = response.data.data;
      toast.success("User added to branch successfully");
      setNewUserData({ user_id: "", role: "", permissions: [] });
      setShowAddUserForm(false);
      await fetchBranchUsers(selectedBranchForUsers.branch_id);
      await fetchAvailableUsers(selectedBranchForUsers.branch_id);
      
      // Update the specific branch's user count in the main branches list using returned data
      setBranches(prev => prev.map(branch => 
        branch.branch_id === selectedBranchForUsers.branch_id 
          ? { 
              ...branch, 
              users: [...(branch.users || []), { 
                id: addedUser.user_id,
                user_id: addedUser.user_id,
                username: addedUser.username,
                name: addedUser.name,
                email: addedUser.email,
                role: addedUser.role,
                permissions: addedUser.permissions,
                is_active: true,
                assigned_at: addedUser.assigned_at,
                branch_user_id: addedUser.id
              }] 
            }
          : branch
      ));
      
      // Also update the selectedBranchForUsers state to reflect changes in modal
      setSelectedBranchForUsers(prev => ({
        ...prev,
        users: [...(prev.users || []), { 
          id: addedUser.user_id,
          user_id: addedUser.user_id,
          username: addedUser.username,
          name: addedUser.name,
          email: addedUser.email,
          role: addedUser.role,
          permissions: addedUser.permissions,
          is_active: true,
          assigned_at: addedUser.assigned_at,
          branch_user_id: addedUser.id
        }]
      }));
    } catch (error) {
      toast.error(`Failed to add user: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoadingUserOperation(false);
    }
  };

  const handleRemoveUserFromBranch = async (userId) => {
    if (window.confirm("Are you sure you want to remove this user from the branch?")) {
      try {
        setLoadingUserOperation(true);
        await axios.delete(`${API_BASE_URL}/branches/${selectedBranchForUsers.branch_id}/users/${userId}`, {
          headers: { Authorization: `Bearer ${currentUser.token}` },
        });
        toast.success("User removed from branch successfully");
        await fetchBranchUsers(selectedBranchForUsers.branch_id);
        await fetchAvailableUsers(selectedBranchForUsers.branch_id);
        
        // Update the specific branch's user count in the main branches list
        setBranches(prev => prev.map(branch => 
          branch.branch_id === selectedBranchForUsers.branch_id 
            ? { ...branch, users: (branch.users || []).filter(user => user.id !== userId && user.user_id !== userId) }
            : branch
        ));
        
        // Also update the selectedBranchForUsers state to reflect changes in modal
        setSelectedBranchForUsers(prev => ({
          ...prev,
          users: (prev.users || []).filter(user => user.id !== userId && user.user_id !== userId)
        }));
      } catch (error) {
        toast.error(`Failed to remove user: ${error.response?.data?.message || error.message}`);
      } finally {
        setLoadingUserOperation(false);
      }
    }
  };

  const handleToggleUserStatus = async (userId, currentStatus) => {
    try {
      setLoadingUserOperation(true);
      setOperatingUserId(userId);
      
      const response = await axios.patch(`${API_BASE_URL}/branches/${selectedBranchForUsers.branch_id}/users/${userId}/toggle`, {}, {
        headers: { Authorization: `Bearer ${currentUser.token}` },
      });
      
      const newStatus = response.data.data.is_active;
      const statusText = newStatus ? "activated" : "deactivated";
      toast.success(`User ${statusText} successfully`);
      
      await fetchBranchUsers(selectedBranchForUsers.branch_id);
      await fetchAvailableUsers(selectedBranchForUsers.branch_id);
      
      // Update the branch users state to reflect the status change
      setBranchUsers(prev => prev.map(user => 
        user.user_id === userId 
          ? { ...user, is_active: newStatus }
          : user
      ));
      
    } catch (error) {
      toast.error(`Failed to toggle user status: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoadingUserOperation(false);
      setOperatingUserId(null);
    }
  };

  const handlePermanentlyRemoveUser = async (userId, username) => {
    if (window.confirm(`Are you sure you want to permanently delete "${username}" from this branch? This action cannot be undone.`)) {
      try {
        setLoadingUserOperation(true);
        setOperatingUserId(userId);
        
        await axios.delete(`${API_BASE_URL}/branches/${selectedBranchForUsers.branch_id}/users/${userId}/permanent`, {
          headers: { Authorization: `Bearer ${currentUser.token}` },
        });
        toast.success("User permanently removed from branch successfully");
        await fetchBranchUsers(selectedBranchForUsers.branch_id);
        await fetchAvailableUsers(selectedBranchForUsers.branch_id);
        
        // Update the specific branch's user count in the main branches list
        setBranches(prev => prev.map(branch => 
          branch.branch_id === selectedBranchForUsers.branch_id 
            ? { ...branch, users: (branch.users || []).filter(user => user.id !== userId && user.user_id !== userId) }
            : branch
        ));
        
        // Also update the selectedBranchForUsers state to reflect changes in modal
        setSelectedBranchForUsers(prev => ({
          ...prev,
          users: (prev.users || []).filter(user => user.id !== userId && user.user_id !== userId)
        }));
      } catch (error) {
        toast.error(`Failed to permanently remove user: ${error.response?.data?.message || error.message}`);
      } finally {
        setLoadingUserOperation(false);
        setOperatingUserId(null);
      }
    }
  };

  const closeBranchUsersModal = () => {
    setShowBranchUsersModal(false);
    setSelectedBranchForUsers(null);
    setBranchUsers([]);
    setAvailableUsers([]);
    setShowAddUserForm(false);
    setNewUserData({ user_id: "", role: "", permissions: [] });
    setLoadingUserOperation(false);
    setShowInactiveUsers(true);
  };

  // Handle Enter key navigation
  const handleKeyDown = (e, nextRef, prevRef) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (nextRef && nextRef.current) {
        nextRef.current.focus();
      } else if (e.target === submitButtonRef.current) {
        // Submit form when Enter is pressed on Submit button
        handleSubmit(e);
      }
    } else if (e.key === "ArrowUp" && prevRef && prevRef.current) {
      e.preventDefault();
      // Move focus to previous field on ArrowUp
      prevRef.current.focus();
    }
  };

  const filteredBranches = branches.filter((branch) =>
    branch[filterType]?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Framer Motion variants for modals
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.7 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, scale: 0.7, transition: { duration: 0.3 } },
  };

  const deleteModalVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, transition: { duration: 0.3 } },
  };

  return (
    <div className="flex flex-col min-h-screen p-4 bg-transparent">
      {/* Header */}
      <div className="py-3 mb-6 text-center text-white rounded-lg shadow-md bg-gradient-to-r from-blue-500 to-blue-800 dark:bg-gradient-to-r dark:from-blue-900 dark:to-slate-800">
        <h1 className="text-2xl font-bold">BRANCH MANAGEMENT</h1>
        <p className="text-sm opacity-90">Manage your branch details efficiently</p>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowForm(true)}
            className="flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            <FaBuilding /> Add Branch
          </button>

          <div className="relative flex items-center gap-2">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-gray-900 dark:border-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="branch_id">Branch ID</option>
              <option value="branch_name">Branch Name</option>
              <option value="branch_address">Address</option>
              <option value="phone_number">Phone Number</option>
            </select>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={`Search by ${filterType.replace(/_/g, " ")}...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full max-w-md py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-gray-900 dark:border-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Form Modal */}
      <AnimatePresence>
        {showForm && (
          <motion.div
            className="fixed inset-0 z-[60] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={modalVariants}
          >
            <motion.div
              className="relative w-full max-w-4xl max-h-[80vh] p-6 bg-white rounded-lg shadow-xl dark:bg-gray-800 overflow-y-auto"
              variants={modalVariants}
            >
              <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                {editId ? "Edit Branch" : "Add New Branch"}
              </h3>
              <form onSubmit={handleSubmit} className="grid grid-cols-1 gap-4 bg-white md:grid-cols-2">
                <div>
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">Branch Name</label>
                  <input
                    type="text"
                    name="branchName"
                    value={formData.branchName}
                    onChange={handleInputChange}
                    onKeyDown={(e) => handleKeyDown(e, branchAddressRef, null)}
                    ref={branchNameRef}
                    required
                    className={`w-full p-2 bg-white border rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500 ${
                      errors.branchName ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.branchName && (
                    <p className="mt-1 text-xs text-red-500">{errors.branchName}</p>
                  )}
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">Branch Address</label>
                  <input
                    type="text"
                    name="branchAddress"
                    value={formData.branchAddress}
                    onChange={handleInputChange}
                    onKeyDown={(e) => handleKeyDown(e, phoneNumberRef, branchNameRef)}
                    ref={branchAddressRef}
                    required
                    className={`w-full p-2 bg-white border rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500 ${
                      errors.branchAddress ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.branchAddress && (
                    <p className="mt-1 text-xs text-red-500">{errors.branchAddress}</p>
                  )}
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">Phone Number</label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    onKeyDown={(e) => handleKeyDown(e, emailRef, branchAddressRef)}
                    ref={phoneNumberRef}
                    required
                    className={`w-full p-2 bg-white border rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500 ${
                      errors.phoneNumber ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.phoneNumber && (
                    <p className="mt-1 text-xs text-red-500">{errors.phoneNumber}</p>
                  )}
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">Email (Optional)</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    onKeyDown={(e) => handleKeyDown(e, submitButtonRef, phoneNumberRef)}
                    ref={emailRef}
                    className={`w-full p-2 bg-white border rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500 ${
                      errors.email ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.email && (
                    <p className="mt-1 text-xs text-red-500">{errors.email}</p>
                  )}
                </div>
                <div className="flex justify-end gap-2 md:col-span-2">
                  <button
                    type="button"
                    onClick={handleCancel}
                    onKeyDown={(e) => handleKeyDown(e, submitButtonRef, emailRef)}
                    ref={cancelButtonRef}
                    className="px-4 py-2 text-sm text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    onKeyDown={(e) => handleKeyDown(e, null, cancelButtonRef)}
                    ref={submitButtonRef}
                    className="px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {editId ? "Update" : "Submit"}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* View Modal */}
      <AnimatePresence>
        {showViewModal && selectedBranch && (
          <motion.div
            className="fixed inset-0 z-[70] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={modalVariants}
          >
            <motion.div
              className="relative w-full max-w-lg p-6 bg-white rounded-lg shadow-xl dark:bg-gray-800"
              variants={modalVariants}
            >
              <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                Branch Details
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Branch ID:</span>
                  <span className="text-gray-600 dark:text-gray-400">{selectedBranch.branch_id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Branch Name:</span>
                  <span className="text-gray-600 dark:text-gray-400">{selectedBranch.branch_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Address:</span>
                  <span className="text-gray-600 dark:text-gray-400">{selectedBranch.branch_address}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Phone:</span>
                  <span className="text-gray-600 dark:text-gray-400">{selectedBranch.phone_number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Email:</span>
                  <span className="text-gray-600 dark:text-gray-400">{selectedBranch.email || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Branch Users:</span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {selectedBranch.users?.length || 0} users assigned
                  </span>
                </div>
              </div>
              <div className="flex justify-end mt-6">
                <button
                  onClick={closeViewModal}
                  className="px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Close
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {showDeleteModal && (
          <motion.div
            className="fixed inset-0 z-[70] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={deleteModalVariants}
          >
            <motion.div
              className="relative w-full max-w-md p-6 bg-white rounded-lg shadow-xl dark:bg-gray-800"
              variants={deleteModalVariants}
            >
              <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                Confirm Deletion
              </h3>
              <p className="mb-6 text-sm text-gray-600 dark:text-gray-400">
                Are you sure you want to delete this branch? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-2">
                <button
                  onClick={cancelDelete}
                  className="px-4 py-2 text-sm text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-4 py-2 text-sm text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Delete
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Branch Users Management Modal */}
      <AnimatePresence>
        {showBranchUsersModal && selectedBranchForUsers && (
          <motion.div
            className="fixed inset-0 z-[80] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={modalVariants}
          >
            <motion.div
              className="relative w-full max-w-4xl max-h-[90vh] p-6 bg-white rounded-lg shadow-xl dark:bg-gray-800 overflow-y-auto"
              variants={modalVariants}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Manage Users - {selectedBranchForUsers.branch_name}
                </h3>
                <button
                  onClick={closeBranchUsersModal}
                  className="p-1 text-gray-500 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 dark:text-gray-400 focus:outline-none"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* Current Branch Users */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                      Current Users ({branchUsers.filter(user => showInactiveUsers || user.is_active).length})
                    </h4>
                    <div className="flex items-center gap-2">
                      <label className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                        <input
                          type="checkbox"
                          checked={showInactiveUsers}
                          onChange={(e) => setShowInactiveUsers(e.target.checked)}
                          className="mr-1 text-blue-600"
                        />
                        Show inactive
                      </label>
                      <button
                        onClick={() => setShowAddUserForm(!showAddUserForm)}
                        className="px-3 py-1 text-xs text-white bg-blue-600 rounded-md hover:bg-blue-700"
                      >
                        {showAddUserForm ? "Cancel" : "Add User"}
                      </button>
                    </div>
                  </div>

                  {loadingBranchUsers ? (
                    <div className="flex items-center justify-center p-4">
                      <div className="w-6 h-6 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {branchUsers.length === 0 ? (
                        <p className="text-sm text-gray-500 text-center py-4">No users assigned to this branch</p>
                      ) : (
                        branchUsers
                          .filter(user => showInactiveUsers || user.is_active)
                          .map((user) => (
                          <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <p className="font-medium text-gray-900 dark:text-white">{user.username}</p>
                                <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                                  user.is_active 
                                    ? 'text-green-700 bg-green-100 dark:bg-green-900 dark:text-green-300' 
                                    : 'text-red-700 bg-red-100 dark:bg-red-900 dark:text-red-300'
                                }`}>
                                  {user.is_active ? 'Active' : 'Inactive'}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">{user.role}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => handleToggleUserStatus(user.user_id, user.is_active)}
                                disabled={loadingUserOperation}
                                title={user.is_active ? 'Deactivate user' : 'Activate user'}
                                className={`p-1 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                                  user.is_active 
                                    ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-100 dark:text-orange-400 dark:hover:bg-orange-900/20' 
                                    : 'text-green-600 hover:text-green-800 hover:bg-green-100 dark:text-green-400 dark:hover:bg-green-900/20'
                                }`}
                              >
                                {loadingUserOperation && operatingUserId === user.user_id ? (
                                  <div className="w-4 h-4 border-2 border-t-transparent border-gray-400 rounded-full animate-spin"></div>
                                ) : (
                                  user.is_active ? <FiToggleRight size={16} /> : <FiToggleLeft size={16} />
                                )}
                              </button>
                              <button
                                onClick={() => handlePermanentlyRemoveUser(user.user_id, user.username)}
                                disabled={loadingUserOperation}
                                title="Permanently delete user from branch"
                                className="p-1 text-red-600 rounded-md transition-colors hover:text-red-800 hover:bg-red-100 dark:text-red-400 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {loadingUserOperation && operatingUserId === user.user_id ? (
                                  <div className="w-4 h-4 border-2 border-t-transparent border-gray-400 rounded-full animate-spin"></div>
                                ) : (
                                  <FiUserMinus size={16} />
                                )}
                              </button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </div>

                {/* Add User Form or Available Users */}
                <div>
                  {showAddUserForm ? (
                    <div>
                      <h4 className="mb-3 text-sm font-semibold text-gray-700 dark:text-gray-300">Add New User</h4>
                      <div className="space-y-3">
                        <div>
                          <label className="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">Select User</label>
                          <select
                            value={newUserData.user_id}
                            onChange={(e) => {
                              const selectedUserId = e.target.value;
                              
                              if (!selectedUserId) {
                                // Clear role when no user is selected
                                setNewUserData(prev => ({
                                  ...prev, 
                                  user_id: '',
                                  role: ''
                                }));
                                return;
                              }
                              
                              const selectedUser = availableUsers.find(user => user.id.toString() === selectedUserId);
                              
                              // Determine the role from multiple sources
                              let userRole = '';
                              if (selectedUser) {
                                // Priority: role field, then first role_name, then fallback
                                userRole = selectedUser.role || 
                                          (selectedUser.role_names && selectedUser.role_names.length > 0 ? selectedUser.role_names[0] : '') ||
                                          'Staff'; // Default fallback role
                              }
                              
                              setNewUserData(prev => ({
                                ...prev, 
                                user_id: selectedUserId,
                                role: userRole
                              }));
                            }}
                            className="w-full p-2 text-sm bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white"
                          >
                            <option value="">Select a user...</option>
                            {availableUsers.map((user) => {
                              const displayRole = user.role || 
                                                  (user.role_names && user.role_names.length > 0 ? user.role_names[0] : '') ||
                                                  'No Role Assigned';
                              return (
                                <option key={user.id} value={user.id}>
                                  {user.username} ({user.name}) - {displayRole}
                                </option>
                              );
                            })}
                          </select>
                        </div>
                        <div>
                          <label className="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                            Role
                            {newUserData.user_id && (
                              <span className="text-xs text-green-600 ml-1">✓ Auto-filled</span>
                            )}
                          </label>
                          <input
                            type="text"
                            value={newUserData.role}
                            onChange={(e) => setNewUserData(prev => ({...prev, role: e.target.value}))}
                            placeholder={newUserData.user_id ? "Role auto-filled from user" : "Select a user first to auto-fill role"}
                            className={`w-full p-2 text-sm bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white ${
                              newUserData.user_id && newUserData.role ? 'border-green-300 bg-green-50 dark:bg-green-900/20' : ''
                            }`}
                            readOnly={!newUserData.user_id}
                          />
                          {newUserData.user_id && newUserData.role && (
                            <p className="mt-1 text-xs text-green-600">
                              Role automatically set from user's profile. You can edit if needed.
                            </p>
                          )}
                        </div>
                        <button
                          onClick={handleAddUserToBranch}
                          disabled={loadingUserOperation}
                          className="w-full px-3 py-2 text-sm text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                        >
                          {loadingUserOperation ? (
                            <>
                              <div className="w-4 h-4 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
                              Adding User...
                            </>
                          ) : (
                            "Add User to Branch"
                          )}
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <h4 className="mb-3 text-sm font-semibold text-gray-700 dark:text-gray-300">
                        Available Users ({availableUsers.length})
                      </h4>
                      <div className="space-y-2 max-h-60 overflow-y-auto">
                        {availableUsers.length === 0 ? (
                          <p className="text-sm text-gray-500 text-center py-4">All users are already assigned</p>
                        ) : (
                          availableUsers.map((user) => (
                            <div key={user.id} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                              <p className="font-medium text-gray-900 dark:text-white">{user.username}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">{user.email}</p>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Branch Table */}
      <div className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-slate-700">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
            <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
              <tr>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Branch ID</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Branch Name</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Address</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Phone</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Email</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Users Count</th>
                <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-600">
              {filteredBranches.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-gray-500 dark:text-gray-400">
                    No branch data found.
                  </td>
                </tr>
              ) : (
                filteredBranches.map((branch) => (
                  <tr key={branch.branch_id} className="transition-colors hover:bg-gray-50 dark:hover:bg-slate-700/50">
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{branch.branch_id}</td>
                    <td className="px-4 py-3 font-medium text-blue-600 dark:text-blue-400 whitespace-nowrap">{branch.branch_name}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{branch.branch_address}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{branch.phone_number}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{branch.email || "N/A"}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300 ${
                        loadingUserOperation && selectedBranchForUsers?.branch_id === branch.branch_id ? 'animate-pulse' : ''
                      }`}>
                        {loadingUserOperation && selectedBranchForUsers?.branch_id === branch.branch_id ? (
                          <>
                            <div className="w-3 h-3 border-t border-blue-600 rounded-full animate-spin mr-1"></div>
                            Updating...
                          </>
                        ) : (
                          `${branch.users?.length || 0} users`
                        )}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-right whitespace-nowrap">
                      <div className="flex items-center justify-end gap-x-3">
                        <button
                          onClick={() => handleManageBranchUsers(branch)}
                          title="Manage Branch Users"
                          className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 focus:outline-none"
                        >
                          <FiUsers size={16} />
                        </button>
                        <button
                          onClick={() => handleView(branch)}
                          title="View Branch"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 focus:outline-none"
                        >
                          <FiEye size={16} />
                        </button>
                        <button
                          onClick={() => handleEdit(branch)}
                          title="Edit Branch"
                          className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 focus:outline-none"
                        >
                          <FiEdit size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(branch.branch_id)}
                          title="Delete Branch"
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                        >
                          <FiTrash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CreateBranch;